#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复数据库表结构以匹配代码期望
"""

import sqlite3
from pathlib import Path

def fix_table_structure():
    """修复数据库表结构"""
    print("🔧 修复数据库表结构...")
    
    db_path = Path("D:/myquant/FZoo/database/factorzoo.sqlite")
    
    # 修复factor_performance_logs表结构
    fix_sql = """
    -- 删除旧表
    DROP TABLE IF EXISTS factor_performance_logs;
    
    -- 创建新表，匹配代码期望的结构
    CREATE TABLE factor_performance_logs (
        run_uid TEXT PRIMARY KEY,
        operation_type TEXT NOT NULL,
        factor_id TEXT,
        batch_id TEXT,
        start_time DATETIME,
        end_time DATETIME,
        total_time_ms INTEGER,
        cpu_time TEXT,
        memory_usage_mb REAL,
        data_size INTEGER,
        success BOOLEAN DEFAULT TRUE,
        error_message TEXT,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
    
    -- 创建索引
    CREATE INDEX IF NOT EXISTS idx_factor_performance_logs_operation_type ON factor_performance_logs(operation_type);
    CREATE INDEX IF NOT EXISTS idx_factor_performance_logs_batch_id ON factor_performance_logs(batch_id);
    CREATE INDEX IF NOT EXISTS idx_factor_performance_logs_start_time ON factor_performance_logs(start_time);
    """
    
    try:
        with sqlite3.connect(str(db_path)) as conn:
            conn.executescript(fix_sql)
            conn.commit()
            print("✅ factor_performance_logs表结构修复成功")
            
            # 验证表结构
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(factor_performance_logs)")
            columns = cursor.fetchall()
            
            print(f"📊 修复后的表包含 {len(columns)} 列:")
            for col in columns:
                print(f"   - {col[1]} ({col[2]})")
            
            return True
                
    except Exception as e:
        print(f"❌ 表结构修复失败: {str(e)}")
        return False

if __name__ == "__main__":
    fix_table_structure() 