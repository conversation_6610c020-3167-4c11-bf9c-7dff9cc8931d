# encoding:utf8
from datetime import datetime
from tqdm import tqdm
import backtrader as bt
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
# from loguru import self.logger
import ffn
# # 添加项目根目录到 Python 路径
# import sys
# import os
# sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from common.cls_base import BaseObj
from datafeed.hku_dataloader import HKUDataloader
from ..task_base import Task
from .strategy import StrategyAlgo
from .comminfo import stampDutyCommInfo
from .analyzer import WeightAnalyzer
from .printer import printTradeAnalysis

ffn.extend_pandas()

class BacktraderEngine(BaseObj):
    def __init__(self, task:Task):
        self.task = task
        self.benchmark = task.benchmark
        cerebro = bt.Cerebro()
        cerebro.broker.setcash(task.init_cash)
        comminfo = stampDutyCommInfo(stamp_duty=task.stamp_duty, commission=task.commission) # eft不收印花税
        cerebro.broker.addcommissioninfo(comminfo)
        
        # 设置滑点类型
        if task.slippage_type.lower() == 'fixed':
            cerebro.broker.set_slippage_fixed(task.slippage)  # 固定价格滑点
        elif task.slippage_type.lower() == 'perc':
            cerebro.broker.set_slippage_perc(task.slippage)  # 百分比滑点
        else:
            self.log(f"未知的滑点类型: {task.slippage_type}，使用默认百分比滑点", "WARNING")
            cerebro.broker.set_slippage_perc(task.slippage)
            
        self.cerebro = cerebro
        self.results = []
        self.df_data = task.load_datas_and_calc_exprs() # 此处集中数据加载+特征值计算...
        # 添加数据检查
        self.log(f"加载数据概览:")
        self.log(f"数据形状: {self.df_data.shape}")
        self.log(f"数据列: {self.df_data.columns.tolist()}")
        self.log(f"数据示例:\n{self.df_data.head()}")
        self.symbols = HKUDataloader.get_HKU_symbols(task.symbols)
        self.dates = list(self.df_data.index.unique())
        self._add_symbols_data()
        self._init_analyzers()
        
    def _init_analyzers(self):
        # ========= 打印用途新增 ==============
        self.cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
        self.cerebro.addanalyzer(bt.analyzers.Calmar, _name='calmar')
        self.cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='ta')
        #self.cerebro.addanalyzer(bt.analyzers.AnnualReturn, _name='_AnnualReturn')
        self.cerebro.addanalyzer(bt.analyzers.SharpeRatio, riskfreerate=0.0, annualize=True, _name='sharpe',timeframe=bt.TimeFrame.Days)
        self.cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
        self.cerebro.addanalyzer(bt.analyzers.VWR, _name='vwr')
        self.cerebro.addanalyzer(bt.analyzers.SQN, _name='sqn')
        # ======== end of 新增 ================
        self.cerebro.addanalyzer(bt.analyzers.Transactions, _name='txn')
        self.cerebro.addanalyzer(bt.analyzers.PyFolio, _name='pyfolio')
        self.cerebro.addanalyzer(WeightAnalyzer, _name='wa')
        # self.cerebro.addanalyzer(bt.analyzers.T, _name='_TotalValue')
    
    def _add_symbols_data(self):
        # 加载数据
        # 加载数据
        print("加载数据到backtrader引擎...")
        for s in tqdm(self.symbols):
            df_symbol = self.df_data[self.df_data['symbol'] == s].copy(deep=True)
            df = to_backtrader_dataframe(df_symbol)
            # print(df_symbol),exit()
            data = bt.feeds.PandasData(dataname=df, name=s, fromdate=self.dates[0], todate=self.dates[-1])
            self.cerebro.adddata(data)
            # self.cerebro.addobserver(bt.observers.Benchmark,
            #                          data=data)
            # self.cerebro.addobserver(bt.observers.TimeReturn)

    def run_algo_strategy(self, g=None):
        algo_list=self.task.get_algos()
        self.g = g
        self.cerebro.addstrategy(StrategyAlgo, algo_list=algo_list, engine=self, global_observer=g)
        self.log('开始回测...')
        try:
            self.results = self.cerebro.run()
        except:
            import traceback
            traceback.print_exc()
        self.log('回测完成')

    def _bokeh_plot(self):
        from backtrader_plotting import Bokeh
        from backtrader_plotting.schemes import Tradimo
        plotconfig = {
            'id:ind#0': dict(
                subplot=True,
            ),
        }
        b = Bokeh(style='line', plog_mode='single', scheme=Tradimo(), plotconfig=plotconfig)
        self.cerebro.plot(b)

    def analysis(self, rs_id=0, console=False):
        self.log('开始分析回测结果...')
        # benchmark equities are its close prices 
        if self.benchmark:
            if self.benchmark in self.symbols:
                df_bench = self.df_data[self.df_data['symbol'] == self.benchmark].copy()
            else:
                df_bench = HKUDataloader.load_df_all([self.benchmark], set_index=True,
                                     start_date=self.dates[0], end_date=self.dates[-1],
                                     freq=self.task.freq)
            bench_close_df = df_bench['close']
            bench_close_df.name = self.benchmark
        else:
            bench_close_df = None
        
        printTradeAnalysis(self.results[rs_id].analyzers, bench_close_df)
        
        portfolio_stats = self.results[rs_id].analyzers.getbyname('pyfolio')
        capital_rets, positions, transactions,_ = portfolio_stats.get_pf_items()
        # returns 为日收益率
        returns = capital_rets.copy()
        returns.index = returns.index.tz_convert(None)
            # 检查索引是否匹配
        if bench_close_df is not None and not returns.index.equals(bench_close_df.index):
            self.log("Returns和Benchmark索引不匹配，以returns日期为准进行对齐", "WARNING")            
            aligned_bench = pd.Series(index=returns.index, dtype=float)
            # 使用benchmark的值填充，对于缺失的日期使用前向填充方法
            aligned_bench.loc[bench_close_df.index] = bench_close_df
            # 使用ffill()替代fillna(method='ffill')
            aligned_bench = aligned_bench.ffill()
            # 更新bench_close_df
            bench_close_df = aligned_bench
            bench_close_df.name = self.benchmark
                
        import quantstats_lumi as qsl
        qsl.reports.html(returns, benchmark=bench_close_df, 
                         periods_per_year=252, 
                         output='qsl_{}.html'.format(datetime.now().strftime('%y%m%d_%H')))
        # qsl.reports.html(returns=returns, benchmark=bench_close_df, periods_per_year=252)

    def get_orders_df(self,rs_id=0):
        portfolio_stats = self.results[rs_id].analyzers.getbyname('pyfolio')
        capital_rets, positions, orders,_ = portfolio_stats.get_pf_items()
        orders.index = orders.index.strftime('%Y-%m-%d')
        cols = ['symbol','amount','price','value']
        orders = orders[cols]
        return orders
    
    def get_weights_df(self,rs_id=0):
        weights_df = pd.DataFrame(self.results[rs_id].analyzers.wa.get_analysis())
        weights_df = weights_df.T
        weights_df.replace([np.inf, -np.inf, np.nan], 0, inplace=True)
        weights_df.index.name = 'date'
        ''' weights_df >>
                    510050.SH  159915.SZ  510300.SH  159920.SZ  510500.SH  ...  512100.SH  513520.SH  159967.SZ  159985.SZ  563300.SH
        date                                                               ...
        2024-04-01   0.114033   0.038479   0.083429   0.058171   0.038168  ...   0.029686   0.076529   0.033718   0.091245   0.026239        
        '''
        return weights_df

def to_backtrader_dataframe(df):
    df = df.copy()
    
    # 更新数值转换逻辑
    numeric_columns = ['open', 'high', 'low', 'close', 'volume']
    for col in numeric_columns:
        try:
            df[col] = pd.to_numeric(df[col])
        except (ValueError, TypeError):
            # 如果转换失败，保持原值
            pass
    
    df.index = pd.to_datetime(df.index)
    df['openinterest'] = 0
    df = df[['open', 'high', 'low', 'close', 'volume', 'openinterest']]
    
    return df

if __name__ == '__main__':
    pass
