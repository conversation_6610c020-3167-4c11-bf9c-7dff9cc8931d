from pathlib import Path
import pytz
import platform
from .settings import settings

class ConfigFeat:
    def __init__(self):
        # 特征配置
        self.feat_norm_model = settings.get('feat.norm.model', 'linear')        # 特征归一化模型
        self.feat_drop_names = settings.get('feat.drop.names', [])        # 需要删除的特征列名
        self.feat_selected_names = settings.get('feat.selected.names', [])  # 需要选择的特征列名
        # 特征norm参数配置
        from .settings import get_norm_params
        self.norm_pos = get_norm_params(self.feat_norm_model, "pos")
        self.norm_X = get_norm_params(self.feat_norm_model, "X")
        self.norm_label = get_norm_params(self.feat_norm_model, "label")        

# 挖掘参数配置
class ConfigMine:
    def __init__(self):
        # 挖掘配置
        self.mine_norm_model = settings.get('mine.run.norm_model', 'linear')
        self.mine_jobnum = settings.get('mine.run.jobnum', -1)
        self.mine_fee = settings.get('mine.run.fee', 0.002)
        self.mine_free = settings.get('mine.run.free', 0.03)
        self.mine_daybars = settings.get('mine.run.perf.daybars', 16)
        self.mine_anndays = settings.get('mine.run.perf.anndays', 252)
        self.mine_nextbar = settings.get('mine.run.nextbar', True)
        self.mine_runnum = settings.get('mine.run.runnum', 3)
        
        # 筛选阈值
        self.skew_thresh = settings.get('mine.filter.skewthresh', 0.5)
        self.kurt_thresh = settings.get('mine.filter.kurtthresh', 5)
        self.corr_thresh = settings.get('mine.filter.corrthresh', 0.3)
        self.metric2use = settings.get('mine.filter.metric2use', 'sic')
        self.sr_thresh = settings.get('mine.filter.sr.srthresh', 0.8)
        self.pjsr_thresh = settings.get('mine.filter.sr.pjsrthresh', 0.2)
        self.rankic_tdelay = settings.get('mine.filter.rankic.tdelay', 1)
        self.rankic_window = settings.get('mine.filter.rankic.window', 240)
        self.rankic_minperiod = settings.get('mine.filter.rankic.minperiod', 120)
        self.rankic_toppct = settings.get('mine.filter.rankic.toppct', 0.2)
        self.rankic_pjpct = settings.get('mine.filter.rankic.pjpct', 0.2)
        
        # 挖掘时norm参数配置
        from .settings import get_norm_params
        self.norm_pos = get_norm_params(self.mine_norm_model, "pos")
        self.norm_X = get_norm_params(self.mine_norm_model, "X")
        self.norm_label = get_norm_params(self.mine_norm_model, "label")
    
    @property
    def sr_params(self):
        """返回metric参数字典，用于**kwargs解包"""
        return {
            'sr_thresh': self.sr_thresh,
            'pjsr_thresh': self.pjsr_thresh,
            'day_bars': self.mine_daybars,
            'ann_days': self.mine_anndays,
            'fixed_return': self.mine_free,
            'fee_rate': self.mine_fee,
            'nextbar_open': self.mine_nextbar,
            'norm_pos_params': self.norm_pos
        }
    
    @property
    def rankic_params(self):
        """返回rankic完整参数字典，用于**kwargs解包"""
        return {
            'window': self.rankic_window,
            'min_period': self.rankic_minperiod,
            'top_pct': self.rankic_toppct,
            'pj_pct': self.rankic_pjpct,
            'norm_pos_params': self.norm_pos
        }

# 创建全局配置实例
cfg_mine = ConfigMine()
cfg_feat = ConfigFeat()

# 路径配置
system = platform.system()
WORKDIR = Path(__file__).parent.parent if system == 'Windows' else Path('/home/<USER>/myquant/XentZ')
SETTING_TZ = pytz.timezone('Asia/Shanghai')
DATA_DIR = Path("D:/myquant/datahist/")
DATA_DIR_HKU = DATA_DIR.joinpath('hku')
REPORTS_DIR = Path("D:/myquant/reports/XentZ")
FACTOR_ZOO_DIR = Path("D:/myquant/FZoo/")

for dir in [DATA_DIR, DATA_DIR_HKU, REPORTS_DIR, FACTOR_ZOO_DIR]:
    dir.mkdir(exist_ok=True, parents=True)

__all__ = ['cfg_mine', 'cfg_feat', 'REPORTS_DIR', 'WORKDIR', 'DATA_DIR', 'DATA_DIR_HKU', 'FACTOR_ZOO_DIR']