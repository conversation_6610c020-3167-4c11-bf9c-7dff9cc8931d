#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查正确的数据库路径
"""

import sqlite3
from pathlib import Path

def check_correct_db_path():
    """检查正确的数据库路径"""
    print("🔍 检查正确的数据库路径...")
    
    # 检查代码期望的路径
    expected_path = Path("D:/myquant/FZoo/database/factorzoo.sqlite")
    print(f"代码期望的数据库路径: {expected_path}")
    print(f"数据库文件存在: {expected_path.exists()}")
    
    if not expected_path.exists():
        print("❌ 数据库文件不存在")
        return False
    
    try:
        with sqlite3.connect(str(expected_path)) as conn:
            cursor = conn.cursor()
            
            # 检查所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            table_names = [t[0] for t in tables]
            
            print(f"📊 数据库包含 {len(table_names)} 个表:")
            for table in sorted(table_names):
                print(f"   - {table}")
            
            # 特别检查factor_performance_logs表
            if 'factor_performance_logs' in table_names:
                print("✅ factor_performance_logs表存在")
                cursor.execute("PRAGMA table_info(factor_performance_logs)")
                columns = cursor.fetchall()
                print(f"   表包含 {len(columns)} 列")
                
                # 检查是否有数据
                cursor.execute("SELECT COUNT(*) FROM factor_performance_logs")
                count = cursor.fetchone()[0]
                print(f"   表包含 {count} 条记录")
            else:
                print("❌ factor_performance_logs表不存在")
                return False
            
            return True
                
    except Exception as e:
        print(f"❌ 数据库检查失败: {str(e)}")
        return False

if __name__ == "__main__":
    check_correct_db_path() 