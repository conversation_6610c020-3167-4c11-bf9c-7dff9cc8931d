#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FactorZoo 包入口
提供 XentZ 项目与 FactorZoo 数据库的统一接口
"""

from typing import Dict, List

# 导入配置
from .config_zoo import (
    FACTOR_ZOO_DIR, 
    FACTOR_ZOO_DB, 
    FACTOR_ZOO_CONFIG,
    TABLE_CONFIGS,
    BATCH_ID_PATTERNS
)

# 导入核心连接器
from .connector import FactorZooConnector

# 导入监控模块
from .monitor import FactorMonitorContext, FactorZooRunManager, FactorZooResMonitor

# 创建全局连接器实例
factorzoo = FactorZooConnector()

# 便捷函数 - 直接代理到连接器
def get_categories(level: int = None) -> List[Dict]:
    """获取因子分类"""
    return factorzoo.get_categories(level)

def get_universes(current_only: bool = True) -> List[Dict]:
    """获取股票池"""
    return factorzoo.get_universes(current_only)

def create_batch(batch_id: str, batch_name: str, creation_tool: str, **kwargs) -> bool:
    """创建批次"""
    return factorzoo.create_batch(batch_id, batch_name, creation_tool, **kwargs)

def add_factor(factor_id: str, factor_name: str, factor_expression: str,
              factor_type: str, data_source_type: str, symbols: List[str],
              frequencies: List[str], date_ranges: Dict, creation_method: str,
              primary_category: str, **kwargs) -> bool:
    """添加因子"""
    return factorzoo.add_factor(
        factor_id, factor_name, factor_expression, factor_type,
        data_source_type, symbols, frequencies, date_ranges,
        creation_method, primary_category, **kwargs
    )

def search_factors(filters: Dict = None, limit: int = 100) -> List[Dict]:
    """搜索因子"""
    return factorzoo.search_factors(filters, limit)

def get_factor_stats() -> Dict:
    """获取因子统计"""
    return factorzoo.get_factor_stats()

def check_database() -> bool:
    """检查数据库是否可用"""
    try:
        with factorzoo.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM factor_categories")
            count = cursor.fetchone()[0]
            return count > 0
    except:
        return False

# 暴露主要接口
__all__ = [
    # 核心组件
    'factorzoo',
    'FactorZooConnector',
    
    # 监控组件
    'FactorMonitorContext',
    'FactorZooRunManager', 
    'FactorZooResMonitor',
    
    # 便捷函数
    'get_categories',
    'get_universes', 
    'create_batch',
    'add_factor',
    'search_factors',
    'get_factor_stats',
    'check_database',
    
    # 配置相关
    'FACTOR_ZOO_CONFIG',
    'FACTOR_ZOO_DIR',
    'FACTOR_ZOO_DB',
    'TABLE_CONFIGS',
    'BATCH_ID_PATTERNS'
]

if __name__ == "__main__":
    # 测试数据库连接
    if check_database():
        print("✅ FactorZoo 数据库连接正常")
        stats = get_factor_stats()
        print(f"📊 当前活跃因子数量: {stats.get('total_factors', 0)}")
    else:
        print("❌ FactorZoo 数据库连接失败") 