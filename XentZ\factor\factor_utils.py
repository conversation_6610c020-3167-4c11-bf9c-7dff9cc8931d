import numpy as np
import numba as nb
from common.cls_base import BaseObj

def warmup_jit_functions():
    """预热所有jit函数，避免第一次运行时的编译开销"""
    # 生成小规模测试数据
    n_samples, n_factors = 300, 2
    fct_values = np.random.randn(n_samples, n_factors).astype(np.float32)
    returns = np.random.randn(n_samples).astype(np.float32)
    # 预热所有jit函数
    _ = FactorPerf.calc_rolling_rankic(fct_values, returns)
    
@nb.jit(nopython=True)
def rank_1d(arr):
    """计算1D数组的排名，与numpy双重argsort结果一致"""
    arr = arr.copy()
    n = len(arr)
    # 第一次argsort：获取值从小到大的索引位置
    # 注意：np.argsort默认是稳定排序，相同值的相对顺序保持不变
    first_sort = np.argsort(arr)
    ranks = np.zeros(n, dtype=np.int32)
    # 第二次：将排序位置赋值给原始位置
    for i in range(n):
        ranks[first_sort[i]] = i
    return ranks

@nb.jit(nopython=True, parallel=True)
def fast_spearman(x: np.ndarray, y: np.ndarray) -> np.ndarray:
    """使用numba加速的Spearman相关系数计算"""
    x = x.copy()
    y = y.copy()
    n = x.shape[1]
    rho = np.empty(x.shape[0])
    
    if n <= 1:
        rho.fill(np.nan)
        return rho
    
    for i in nb.prange(x.shape[0]):
        d_squared = (x[i] - y[i]) ** 2
        sum_d_squared = np.sum(d_squared)
        rho[i] = 1 - (6 * sum_d_squared) / (n * (n**2 - 1))
    
    return rho

class FactorPerf(BaseObj):
    """ === 因子绩效数据计算 === """
    def __init__(self):
        pass
    
    @staticmethod
    @nb.jit(nopython=True, parallel=True)
    def calc_rolling_rankic(fct_values: np.ndarray, returns: np.ndarray, 
                            window: int = 240, min_periods: int = 120) -> np.ndarray:
        """
        计算滚动 RankIC 矩阵（每个时间窗口对应一个 RankIC 值）
        
        Args:
            fct_values: 因子值序列, shape (n_samples,) 或 (n_samples, n_factors)
            returns: 未来 t 期收益率序列（与因子序列对齐）, shape (n_samples,)
            window: 滚动窗口大小
            min_periods: 每个窗口要求的最小有效样本数（这里以因子值非 0 的数量判断）
            
        Returns:
            滚动 RankIC 矩阵，形状为 (n_samples, n_factors) —— 前 window-1 行为 NaN，
            后续每一行对应一个窗口计算得到的 RankIC 值
        """
        """优化后实现"""
        # 数据预处理：处理NaN和无穷值
        fct_values_clean = fct_values.copy().astype(np.float32)
        returns_clean = returns.copy().astype(np.float32)
        
        if fct_values.ndim == 1:
            fct_values = fct_values.reshape(-1, 1)
        n_samples, n_factors = fct_values.shape
        
        rolling_rankic = np.full((n_samples, n_factors), np.nan)
            
        # 计算收益率的滑动窗口
        ret_roll = np.lib.stride_tricks.sliding_window_view(returns_clean, window)
        n_windows = ret_roll.shape[0]
        
        # 预计算收益率排名
        ret_ranks = np.empty_like(ret_roll, dtype=np.int32)
        for i in range(n_windows):
            ret_ranks[i] = rank_1d(ret_roll[i])
        
        # 并行处理每个因子
        for j in nb.prange(n_factors):
            fct_roll = np.lib.stride_tricks.sliding_window_view(fct_values_clean[:, j], window)
            fct_ranks = np.empty_like(fct_roll, dtype=np.int32)
            
            # 计算因子排名
            for i in range(n_windows):
                fct_ranks[i] = rank_1d(fct_roll[i])
            
            # 使用简单的比较操作替代isclose
            valid_mask = np.zeros(n_windows, dtype=np.bool_)
            for i in range(n_windows):
                valid_count = 0
                for k in range(window):
                    if abs(fct_roll[i, k]) > 1e-8:  # 使用小阈值替代isclose
                        valid_count += 1
                valid_mask[i] = (valid_count >= min_periods)
            
            corr = fast_spearman(fct_ranks, ret_ranks)
            corr[~valid_mask] = np.nan
            rolling_rankic[window-1:, j] = corr
            
        return rolling_rankic
    
    @staticmethod
    def calc_sr(bar_ret:np.ndarray, day_bars: int = 16, trading_days:int = 252, free_rate:float = 0.03)-> np.ndarray:
        '''
            输入: 列名因子值, 矩阵值是每bar的算数收益率
            返回: 各因子值对应的sr -- 一维数组
        '''
        bar_ret = np.nan_to_num(bar_ret, nan=0.0, posinf=0.0, neginf=0.0, copy=False)
        # NOTE: 结论: 百分比收益率ret, ret.mean() * N = ann_ret
        ret_mean = np.mean(bar_ret,axis=0)
        ann_ret = ret_mean * day_bars * trading_days
        ret_std = bar_ret.std(axis=0) # np.std(ret,ddof=1) 速度接近且结果一致
        ann_vol = np.sqrt(day_bars * trading_days) * ret_std # 每日16个15min bar
        cond_0 = np.isclose(ann_vol, 0)
        with np.errstate(divide='ignore', invalid='ignore'):
            sr = np.where(cond_0, 0, (ann_ret - free_rate) / ann_vol)
        return sr
    
    
