import pandas as pd
import numpy as np
import sys
sys.path.append('/d:/myquant/xentz')

from datafeed.features.feature_utils import FeatEngineering
from common.cls_base import BaseObj

def create_preprocessed_test_data():
    """创建已预处理的测试数据（模拟真实的量化数据格式）"""
    np.random.seed(42)
    
    # 创建多个symbol的时间序列数据
    symbols = ['AAPL', 'MSFT', 'GOOGL']
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    
    all_data = []
    
    for symbol in symbols:
        # 模拟已预处理的价格数据（标准化后）
        base_price = np.random.randn(100).cumsum() * 0.02  # 价格变化
        volume_factor = np.random.randn(100) * 0.1 + 1     # 成交量因子
        
        symbol_data = pd.DataFrame({
            'datetime': dates,
            'close': base_price,
            'volume': volume_factor,
            'returns': np.diff(np.concatenate([[0], base_price])),  # 收益率
            'volatility': pd.Series(base_price).rolling(5).std().fillna(0),  # 波动率
            'label_1d': np.roll(base_price, -1),  # 未来1天标签
            'label_5d': np.roll(base_price, -5),  # 未来5天标签
        })
        symbol_data['symbol'] = symbol
        all_data.append(symbol_data)
    
    # 合并数据并设置多级索引（模拟HKUDataloader的输出格式）
    df_all = pd.concat(all_data, ignore_index=True)
    df_all = df_all.set_index(['datetime', 'symbol'])
    
    BaseObj.log(f"创建测试数据: {df_all.shape}, 索引类型: {type(df_all.index)}")
    BaseObj.log(f"数据列: {list(df_all.columns)}")
    BaseObj.log(f"Symbol数量: {df_all.index.get_level_values('symbol').nunique()}")
    
    return df_all

def test_tsfresh_basic():
    """测试基本功能"""
    print("\n" + "="*60)
    print("测试1: 基本功能测试")
    print("="*60)
    
    df_all = create_preprocessed_test_data()
    
    # 测试基本提取
    result = FeatEngineering.tsfresh_extract(
        df_all,
        feature_set='minimal',  # 使用最小集合加快测试
        enable_feature_selection=False,  # 先不启用特征选择
        n_jobs=1
    )
    
    print(f"✓ 基本提取结果: {result.shape}")
    print(f"✓ 索引类型: {type(result.index)}")
    print(f"✓ 特征列示例: {list(result.columns[:5])}")
    
    # 验证索引对齐
    assert result.index.equals(df_all.index), "索引未对齐"
    print("✓ 索引对齐检查通过")
    
    # 验证无缺失值
    missing_count = result.isna().sum().sum()
    print(f"✓ 缺失值数量: {missing_count}")
    
    return result

def test_tsfresh_feature_selection():
    """测试特征选择功能"""
    print("\n" + "="*60)
    print("测试2: 特征选择功能测试")
    print("="*60)
    
    df_all = create_preprocessed_test_data()
    
    # 测试启用特征选择
    result = FeatEngineering.tsfresh_extract(
        df_all,
        feature_set='efficient',
        enable_feature_selection=True,
        fdr_level=0.1,  # 放宽阈值以获得更多特征
        ml_task='regression',  # 明确指定回归任务
        n_jobs=1
    )
    
    print(f"✓ 特征选择结果: {result.shape}")
    print(f"✓ 选择后的特征列示例: {list(result.columns[:3])}")
    
    # 验证特征选择确实起作用（应该比不选择时特征更少）
    result_no_selection = FeatEngineering.tsfresh_extract(
        df_all,
        feature_set='efficient',
        enable_feature_selection=False,
        n_jobs=1
    )
    
    print(f"✓ 无选择时特征数: {result_no_selection.shape[1]}")
    print(f"✓ 有选择时特征数: {result.shape[1]}")
    
    if result.shape[1] <= result_no_selection.shape[1]:
        print("✓ 特征选择功能正常工作")
    else:
        print("⚠ 特征选择可能未生效")
    
    return result

def test_tsfresh_different_sets():
    """测试不同特征集合"""
    print("\n" + "="*60)
    print("测试3: 不同特征集合测试")
    print("="*60)
    
    df_all = create_preprocessed_test_data()
    
    feature_sets = ['minimal', 'efficient']  # 跳过comprehensive以节省时间
    results = {}
    
    for fs in feature_sets:
        print(f"\n测试特征集合: {fs}")
        result = FeatEngineering.tsfresh_extract(
            df_all,
            feature_set=fs,
            enable_feature_selection=False,
            n_jobs=1
        )
        results[fs] = result
        print(f"✓ {fs}: {result.shape[1]} 个特征")
    
    # 验证特征数量递增关系
    if results['minimal'].shape[1] <= results['efficient'].shape[1]:
        print("✓ 特征集合大小关系正确")
    else:
        print("⚠ 特征集合大小关系异常")
    
    return results

def test_tsfresh_single_column():
    """测试指定单列提取"""
    print("\n" + "="*60)
    print("测试4: 单列特征提取测试")
    print("="*60)
    
    df_all = create_preprocessed_test_data()
    
    # 测试指定单列
    result = FeatEngineering.tsfresh_extract(
        df_all,
        feature_set='minimal',
        column_value='close',  # 只处理close列
        enable_feature_selection=False,
        n_jobs=1
    )
    
    print(f"✓ 单列提取结果: {result.shape}")
    
    # 验证所有特征都以指定列名开头
    close_features = [col for col in result.columns if col.startswith('close__')]
    print(f"✓ close相关特征数: {len(close_features)}")
    
    if len(close_features) == result.shape[1]:
        print("✓ 单列提取功能正常")
    else:
        print("⚠ 单列提取可能包含其他列的特征")
    
    return result

def test_tsfresh_error_handling():
    """测试错误处理"""
    print("\n" + "="*60)
    print("测试5: 错误处理测试")
    print("="*60)
    
    # 测试空数据
    empty_df = pd.DataFrame()
    result = FeatEngineering.tsfresh_extract(empty_df)
    assert result.empty, "空数据应返回空DataFrame"
    print("✓ 空数据处理正常")
    
    # 测试无效特征集合
    df_all = create_preprocessed_test_data()
    result = FeatEngineering.tsfresh_extract(
        df_all,
        feature_set='invalid_set'
    )
    assert result.empty, "无效特征集合应返回空DataFrame"
    print("✓ 无效参数处理正常")
    
    # 测试无效列名
    result = FeatEngineering.tsfresh_extract(
        df_all,
        column_value='nonexistent_column'
    )
    assert result.empty, "无效列名应返回空DataFrame"
    print("✓ 无效列名处理正常")
    
    print("✓ 所有错误处理测试通过")

def test_tsfresh_performance():
    """测试性能和并行处理"""
    print("\n" + "="*60)
    print("测试6: 性能测试")
    print("="*60)
    
    df_all = create_preprocessed_test_data()
    
    import time
    
    # 测试串行处理
    start_time = time.time()
    result_serial = FeatEngineering.tsfresh_extract(
        df_all,
        feature_set='minimal',
        n_jobs=1
    )
    serial_time = time.time() - start_time
    
    # 测试并行处理
    start_time = time.time()
    result_parallel = FeatEngineering.tsfresh_extract(
        df_all,
        feature_set='minimal',
        n_jobs=2
    )
    parallel_time = time.time() - start_time
    
    print(f"✓ 串行处理时间: {serial_time:.2f}秒")
    print(f"✓ 并行处理时间: {parallel_time:.2f}秒")
    print(f"✓ 结果一致性: {result_serial.equals(result_parallel)}")
    
    if parallel_time < serial_time * 1.2:  # 允许一些开销
        print("✓ 并行处理效果良好")
    else:
        print("⚠ 并行处理效果不明显（可能因为数据量小）")

def run_all_tests():
    """运行所有测试"""
    print("开始 tsfresh_extract 功能测试")
    print("="*80)
    
    try:
        # 检查tsfresh是否安装
        import tsfresh
        print(f"✓ tsfresh 版本: {tsfresh.__version__}")
    except ImportError:
        print("❌ tsfresh 未安装，请运行: pip install tsfresh")
        return
    
    try:
        test_tsfresh_basic()
        test_tsfresh_feature_selection()
        test_tsfresh_different_sets()
        test_tsfresh_single_column()
        test_tsfresh_error_handling()
        test_tsfresh_performance()
        
        print("\n" + "="*80)
        print("🎉 所有测试通过！tsfresh_extract 功能正常")
        print("="*80)
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def debug_index_issue():
    """调试索引对齐问题"""
    np.random.seed(42)
    
    # 创建测试数据
    symbols = ['AAPL', 'MSFT']
    dates = pd.date_range('2023-01-01', periods=5, freq='D')  # 减少数据量便于调试
    
    all_data = []
    for symbol in symbols:
        base_price = np.random.randn(5).cumsum() * 0.02
        symbol_data = pd.DataFrame({
            'datetime': dates,
            'close': base_price,
            'volume': np.random.randn(5) * 0.1 + 1,
            'label_1d': np.roll(base_price, -1),
        })
        symbol_data['symbol'] = symbol
        all_data.append(symbol_data)
    
    df_all = pd.concat(all_data, ignore_index=True)
    df_all = df_all.set_index(['datetime', 'symbol'])
    
    print("原始数据索引:")
    print(f"类型: {type(df_all.index)}")
    print(f"形状: {df_all.shape}")
    print(f"索引: {df_all.index}")
    print()
    
    # 测试特征提取
    result = FeatEngineering.tsfresh_extract(
        df_all,
        feature_set='minimal',
        enable_feature_selection=False,
        n_jobs=1
    )
    
    print("结果数据索引:")
    print(f"类型: {type(result.index)}")
    print(f"形状: {result.shape}")
    print(f"索引: {result.index}")
    print()
    
    print("索引比较:")
    print(f"原始索引长度: {len(df_all.index)}")
    print(f"结果索引长度: {len(result.index)}")
    print(f"索引相等: {df_all.index.equals(result.index)}")
    
    # 详细比较
    if not df_all.index.equals(result.index):
        print("\n详细索引差异:")
        print("原始索引前5个:")
        print(df_all.index[:5])
        print("结果索引前5个:")
        print(result.index[:5])

if __name__ == "__main__":
    run_all_tests()
    debug_index_issue()