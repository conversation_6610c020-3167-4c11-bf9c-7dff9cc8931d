# FactorZoo实用脚本工具集

这个目录包含了一系列实用的脚本工具，用于管理和操作FactorZoo系统中的因子数据。

## 📁 脚本分类

### 🔧 核心工具脚本
- `data_viewer.py` - 数据查看器，支持feather/parquet/csv文件的查看和分析
- `batch_tools.py` - 批次管理工具，提供批次查看、统计和清理功能
- `format_converter.py` - 格式转换工具，支持多种数据格式间的转换

### 🚀 一键运行脚本 (中文命名)
- `查看数据.py` - 快速查看FactorZoo数据的交互式工具
- `批次管理.py` - 批次数据的完整管理界面
- `格式转换.py` - 数据格式转换的便捷工具
- `数据统计.py` - 生成详细的数据统计报告
- `快速测试.py` - 系统功能的快速验证工具

## 🎯 使用场景

### 日常数据检查
```python
# 在IDE中直接运行
python factorzoo/scripts/查看数据.py
```

### 批次维护
```python
# 管理批次数据
python factorzoo/scripts/批次管理.py
```

### 数据导出
```python
# 转换数据格式
python factorzoo/scripts/格式转换.py
```

### 系统监控
```python
# 生成统计报告
python factorzoo/scripts/数据统计.py
```

### 功能验证
```python
# 快速测试系统
python factorzoo/scripts/快速测试.py
```

## 📝 详细功能说明

### 1. 查看数据.py
- **功能**: 交互式数据浏览器
- **特性**: 
  - 显示最新批次列表
  - 支持按批次ID查看详情
  - 支持按品种筛选查看
  - 集成数据文件浏览功能

### 2. 批次管理.py
- **功能**: 完整的批次管理界面
- **特性**:
  - 按日期/品种/阶段查看批次
  - 存储空间分析
  - 旧数据清理 (支持预览模式)
  - 批次文件详情查看

### 3. 格式转换.py
- **功能**: 数据格式转换工具
- **特性**:
  - 单文件转换
  - 目录批量转换
  - FactorZoo批次专用转换
  - 支持CSV/Parquet/Feather格式

### 4. 数据统计.py
- **功能**: 生成详细统计报告
- **分析内容**:
  - 批次趋势分析 (时间/品种/阶段分布)
  - 存储使用分析 (大小/文件类型)
  - 因子分布统计 (数量/分布)
  - 数据质量检查 (完整性验证)

### 5. 快速测试.py
- **功能**: 系统功能验证
- **测试项目**:
  - 基础功能 (配置/目录)
  - 数据操作 (存储/读取)
  - 性能功能 (缓存/并行)
  - 批次操作 (查询/筛选)
  - 错误处理 (异常情况)

## 🛠️ 命令行工具使用

### 高级用户命令行模式
```bash
# 数据查看
python factorzoo/scripts/data_viewer.py base_data.feather

# 批次管理
python factorzoo/scripts/batch_tools.py list --detailed
python factorzoo/scripts/batch_tools.py cleanup --days 30

# 格式转换
python factorzoo/scripts/format_converter.py file input.feather --format csv
python factorzoo/scripts/format_converter.py batch BATCH_ID --format parquet
```

## 📋 最佳实践

### 日常维护流程
1. **数据检查**: 运行`数据统计.py`了解系统状态
2. **查看数据**: 使用`查看数据.py`检查最新因子数据
3. **清理维护**: 定期运行`批次管理.py`清理旧数据
4. **功能验证**: 系统更新后运行`快速测试.py`验证

### 数据导出流程
1. **批次选择**: 使用`批次管理.py`选择要导出的批次
2. **格式转换**: 使用`格式转换.py`转换为所需格式
3. **质量检查**: 使用`查看数据.py`验证导出结果

### 故障排查流程
1. **系统测试**: 运行`快速测试.py`检查基础功能
2. **数据分析**: 运行`数据统计.py`检查数据完整性
3. **手动检查**: 使用`查看数据.py`检查具体数据文件

## ⚡ IDE集成建议

### PyCharm/VSCode
1. 将脚本添加到运行配置
2. 设置工作目录为项目根目录
3. 配置快捷键一键运行常用脚本

### Jupyter Notebook
```python
# 在notebook中运行
%run factorzoo/scripts/数据统计.py
%run factorzoo/scripts/快速测试.py
```

## 🔧 扩展开发

如需添加新的脚本工具:
1. 参考现有脚本的结构和错误处理
2. 遵循中文命名约定 (一键运行脚本)
3. 添加相应的文档说明
4. 确保与现有工具的兼容性 