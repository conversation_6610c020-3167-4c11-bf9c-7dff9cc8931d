# XentZ API 参考文档

## 1. 数据管理模块 (datafeed)

### 1.1 HKUDataloader

#### 类方法

##### `load_df_all(symbols, set_index=False, start_date='20100101', end_date=None, freq='D', recover='NO_RECOVER')`

批量加载多个品种的历史数据

**参数:**
- `symbols` (List[str], optional): 品种代码列表，None表示加载所有品种
- `set_index` (bool): 是否将datetime设为索引，默认False
- `start_date` (str): 开始日期，格式'YYYYMMDD'
- `end_date` (str): 结束日期，格式'YYYYMMDD'
- `freq` (str): 数据频率，支持 'D'(日线), 'W'(周线), '1min'(1分钟)等
- `recover` (str): 复权类型，'NO_RECOVER'(不复权), 'FORWARD'(前复权)等

**返回:**
- `pd.DataFrame`: 包含所有品种的历史数据，格式为[datetime, open, high, low, close, amount, volume, symbol]

**示例:**
```python
from datafeed.hku_dataloader import HKUDataloader

# 加载多个品种的日线数据
df = HKUDataloader.load_df_all(
    symbols=['SH000300', 'SZ399006'], 
    start_date='20200101',
    freq='D',
    set_index=True
)
```

##### `load_a_backtrader_df(symbol, start_date='20100101', end_date=None, freq='D', recover='NO_RECOVER')`

加载单个品种数据并转换为BackTrader兼容格式

**参数:**
- `symbol` (str): 品种代码
- 其他参数同`load_df_all`

**返回:**
- `pd.DataFrame`: BackTrader格式的数据，包含[open, high, low, close, volume, openinterest]列

##### `get_col_pivot_df(df_all, col='close', start_date='20100101', end_date=None)`

从多品种数据中提取指定列并转换为透视表格式

**参数:**
- `df_all` (pd.DataFrame): 多品种数据，index必须为datetime
- `col` (str): 要提取的列名，如'close', 'volume'等
- `start_date` (str): 筛选起始日期
- `end_date` (str): 筛选结束日期

**返回:**
- `pd.DataFrame`: 透视表格式，index为datetime，columns为symbol

##### `load_col_pivot_df(symbols, col='close', start_date='20100101', end_date=None, freq='D', recover='NO_RECOVER')`

直接加载指定列的透视表格式数据

**参数:**
- 参数组合了`load_df_all`和`get_col_pivot_df`的参数

**返回:**
- `pd.DataFrame`: 透视表格式的指定列数据

## 2. 特征工程模块 (features)

### 2.1 FeatPreprocessing

所有方法都支持`@calc_df_by_symbol`装饰器，自动按品种分组处理。

#### `fill_missing(df_all, method='ffill', limit=None, value=None, axis=0, columns=None, **kwargs)`

缺失值填充

**参数:**
- `df_all` (pd.DataFrame): 待处理数据
- `method` (str): 填充方法
  - `'ffill'`: 前向填充
  - `'bfill'`: 后向填充
  - `'mean'`: 均值填充
  - `'median'`: 中位数填充
  - `'interpolate'`: 插值填充
  - `'value'`: 固定值填充
- `limit` (int, optional): 连续填充的最大数量
- `value` (float, optional): 固定值填充时使用的值
- `axis` (int): 填充方向，0按列，1按行
- `columns` (List[str], optional): 指定要填充的列

**返回:**
- `pd.DataFrame`: 填充后的数据

**示例:**
```python
from datafeed.features.feature_utils import FeatPreprocessing

# 前向填充缺失值
df_filled = FeatPreprocessing.fill_missing(df, method='ffill', limit=5)

# 用均值填充特定列
df_filled = FeatPreprocessing.fill_missing(
    df, method='mean', columns=['close', 'volume']
)
```

#### `mad_clip_df(df_all, k=3, axis=0, **kwargs)`

使用MAD方法进行异常值截断

**参数:**
- `df_all` (pd.DataFrame): 待处理数据
- `k` (int): MAD倍数，默认3倍
- `axis` (int): 计算方向，0按列，1按行

**返回:**
- `pd.DataFrame`: 截断后的数据

#### `norm_df(df, window=2000, n_clip=6, logmode=0, smooth=0, demean=True, algomode=0, **kwargs)`

数据标准化处理

**参数:**
- `df` (pd.DataFrame): 待标准化数据
- `window` (int): 滚动窗口大小
- `n_clip` (int): 截断倍数
- `logmode` (int): 对数变换模式，0不变换，1取对数
- `smooth` (int): 平滑参数
- `demean` (bool): 是否去均值
- `algomode` (int): 标准化算法
  - `0`: Z-Score标准化
  - `1`: L2范数归一化
  - `2`: MinMax归一化
  - `3`: 分位数归一化

**返回:**
- `pd.DataFrame`: 标准化后的数据

**示例:**
```python
# Z-Score标准化
df_norm = FeatPreprocessing.norm_df(df, algomode=0, window=1000)

# MinMax归一化
df_norm = FeatPreprocessing.norm_df(df, algomode=2, demean=False)
```

### 2.2 FeatEngineering

#### `create_lag_features(df, lags, column_sort='datetime', column_value=None, **kwargs)`

创建滞后特征

**参数:**
- `df` (pd.DataFrame): 输入数据
- `lags` (List[int]): 滞后期列表，如[1, 5, 10]
- `column_sort` (str): 时间排序列名
- `column_value` (str, optional): 要创建滞后特征的列名，None表示所有数值列

**返回:**
- `pd.DataFrame`: 包含滞后特征的数据

**示例:**
```python
from datafeed.features.feature_utils import FeatEngineering

# 创建1、5、10期滞后特征
df_lag = FeatEngineering.create_lag_features(
    df, lags=[1, 5, 10], column_value='close'
)
```

#### `create_rolling_features(df, windows, funcs=['mean', 'std', 'min', 'max'], column_sort='datetime', column_value=None, **kwargs)`

创建滚动窗口特征

**参数:**
- `df` (pd.DataFrame): 输入数据
- `windows` (List[int]): 窗口大小列表，如[5, 10, 20]
- `funcs` (List[str]): 统计函数列表
  - `'mean'`: 均值
  - `'std'`: 标准差
  - `'min'`: 最小值
  - `'max'`: 最大值
  - `'median'`: 中位数
  - `'skew'`: 偏度
  - `'kurt'`: 峰度
- `column_sort` (str): 时间排序列名
- `column_value` (str, optional): 要计算特征的列名

**返回:**
- `pd.DataFrame`: 包含滚动特征的数据

**示例:**
```python
# 创建多个窗口的滚动特征
df_rolling = FeatEngineering.create_rolling_features(
    df, 
    windows=[5, 10, 20], 
    funcs=['mean', 'std'],
    column_value='close'
)
```

#### `extract_tsfresh_features(df, feature_set='efficient', column_sort='datetime', column_value=None, n_jobs=1, **kwargs)`

使用TSFresh提取时间序列特征

**参数:**
- `df` (pd.DataFrame): 输入数据
- `feature_set` (str): 特征集合
  - `'efficient'`: 高效特征集（推荐）
  - `'comprehensive'`: 全面特征集（计算量大）
- `column_sort` (str): 时间排序列名
- `column_value` (str, optional): 要提取特征的列名
- `n_jobs` (int): 并行进程数

**返回:**
- `pd.DataFrame`: TSFresh特征

**示例:**
```python
# 提取TSFresh高效特征
df_tsfresh = FeatEngineering.extract_tsfresh_features(
    df, feature_set='efficient', n_jobs=4
)
```

### 2.3 FeatSelection

#### `select_by_importance(X, y, method='random_forest', top_n=10)`

基于重要性的特征选择

**参数:**
- `X` (pd.DataFrame): 特征矩阵
- `y` (pd.Series): 目标变量
- `method` (str): 重要性计算方法
  - `'random_forest'`: 随机森林
  - `'xgboost'`: XGBoost
  - `'mutual_info'`: 互信息
- `top_n` (int): 选择特征数量

**返回:**
- `List[str]`: 选中的特征名列表

**示例:**
```python
from datafeed.features.feature_utils import FeatSelection

# 使用随机森林选择特征
selected_features = FeatSelection.select_by_importance(
    X, y, method='random_forest', top_n=20
)
```

#### `select_by_correlation(df, threshold=0.7)`

基于相关性的特征过滤

**参数:**
- `df` (pd.DataFrame): 特征数据
- `threshold` (float): 相关系数阈值

**返回:**
- `List[str]`: 过滤后的特征名列表

#### `select_by_statistical_tests(X, y, ml_task='auto', fdr_level=0.05, **kwargs)`

基于统计检验的特征选择

**参数:**
- `X` (pd.DataFrame): 特征矩阵
- `y` (pd.Series): 目标变量
- `ml_task` (str): 机器学习任务类型，'auto'自动识别
- `fdr_level` (float): 误发现率水平

**返回:**
- `pd.DataFrame`: 特征相关性表

## 3. 配置管理模块 (config)

### 3.1 settings

#### `get_norm_params(model_type='linear', data_type='X')`

获取标准化参数

**参数:**
- `model_type` (str): 模型类型，如'linear', 'tree'等
- `data_type` (str): 数据类型，如'X', 'y'等

**返回:**
- `dict`: 标准化参数字典

**示例:**
```python
from config.settings import get_norm_params

# 获取线性模型的X数据标准化参数
params = get_norm_params('linear', 'X')
# 返回: {'window': 2000, 'algomode': 0, 'n_clip': 6, ...}
```

#### `get_missing_mode()`

获取缺失值处理模式

**返回:**
- `str`: 缺失值处理模式，如'ffill', 'drop'等

#### `get_outliers_params()`

获取异常值处理参数

**返回:**
- `dict`: 异常值处理参数

#### `update_config(value, *keys)`

动态更新配置

**参数:**
- `value`: 要设置的值
- `*keys`: 配置路径，如('norm', 'window')

**返回:**
- `bool`: 是否更新成功

**示例:**
```python
from config.settings import update_config

# 更新标准化窗口大小
success = update_config(1500, 'norm', 'window')
```

## 4. 基础工具模块 (common)

### 4.1 BaseObj

#### `log(txt, level='INFO')`

统一日志记录

**参数:**
- `txt` (str): 日志内容
- `level` (str): 日志级别，'DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'

**示例:**
```python
from common.cls_base import BaseObj

class MyClass(BaseObj):
    def process(self):
        self.log("开始处理数据", level="INFO")
        # 处理逻辑
        self.log("处理完成", level="INFO")
```

#### `gen_ordered_uid()`

生成有序唯一ID

**返回:**
- `str`: 16位有序唯一ID

### 4.2 ResMonitor

#### `start_timer(label)`

开始计时

**参数:**
- `label` (str): 计时标签

#### `end_timer(label)`

结束计时

**参数:**
- `label` (str): 计时标签

**返回:**
- `str`: 格式化的时间字符串

#### `log_memory_usage(message='')`

记录内存使用情况

**参数:**
- `message` (str): 附加消息

**返回:**
- `str`: 内存使用量（MB）

**示例:**
```python
from common.cls_base import ResMonitor

rm = ResMonitor()
rm.start_timer("data_processing")
rm.log_memory_usage("开始处理")

# 数据处理逻辑...

rm.log_memory_usage("处理完成")
elapsed = rm.end_timer("data_processing")
```

## 5. 装饰器

### 5.1 calc_df_by_symbol

自动按品种分组处理的装饰器

**功能:**
- 多级索引DataFrame：按第二级（symbol）分组处理
- 普通DataFrame有symbol列：按symbol列分组处理
- 无symbol信息：直接处理整个DataFrame
- 自动传递`current_symbol`参数给被装饰函数

**使用方式:**
```python
from datafeed.features.feature_utils import calc_df_by_symbol

@calc_df_by_symbol
def my_feature_function(df: pd.DataFrame, param1: int, **kwargs):
    current_symbol = kwargs.get('current_symbol')
    # 处理单个品种的数据
    return processed_df

# 调用时传入多品种数据，自动分组处理
result = my_feature_function(multi_symbol_df, param1=10)
```

## 6. 数据格式规范

### 6.1 标准DataFrame格式

#### 单品种格式
```python
df_single = pd.DataFrame({
    'open': [100.0, 101.0, 102.0],
    'high': [102.0, 103.0, 104.0], 
    'low': [99.0, 100.0, 101.0],
    'close': [101.0, 102.0, 103.0],
    'volume': [1000, 1100, 1200]
}, index=pd.DatetimeIndex(['2021-01-01', '2021-01-02', '2021-01-03']))
```

#### 多品种格式
```python
df_multi = pd.DataFrame({
    'open': [100.0, 200.0, 101.0, 201.0],
    'high': [102.0, 202.0, 103.0, 203.0],
    'low': [99.0, 199.0, 100.0, 200.0], 
    'close': [101.0, 201.0, 102.0, 202.0],
    'volume': [1000, 2000, 1100, 2100],
    'symbol': ['AAPL', 'MSFT', 'AAPL', 'MSFT']
}, index=pd.DatetimeIndex(['2021-01-01', '2021-01-01', '2021-01-02', '2021-01-02']))
```

#### 透视表格式
```python
df_pivot = pd.DataFrame({
    'AAPL': [101.0, 102.0, 103.0],
    'MSFT': [201.0, 202.0, 203.0],
    'GOOG': [301.0, 302.0, 303.0]
}, index=pd.DatetimeIndex(['2021-01-01', '2021-01-02', '2021-01-03']))
```

## 7. 错误码和异常处理

### 7.1 常见异常

#### `TypeError`
- 输入数据类型错误
- 解决方案：检查输入是否为pandas DataFrame

#### `ValueError`
- 数据值错误，如重复索引问题
- 解决方案：检查数据完整性，使用适当的处理方法

#### `KeyError`
- 缺少必要的列或索引
- 解决方案：确保数据包含必要的列（如symbol列）

#### `ImportError`
- 缺少可选依赖
- 解决方案：安装相应的包，如TSFresh

### 7.2 最佳实践

```python
# 1. 错误处理
try:
    result = process_data(df)
except Exception as e:
    BaseObj.log(f"处理失败: {e}", level="ERROR")
    return None

# 2. 数据验证
if df.empty:
    BaseObj.log("输入数据为空", level="WARNING")
    return df

# 3. 参数验证
assert isinstance(df, pd.DataFrame), "输入必须是DataFrame"
assert 'symbol' in df.columns, "数据必须包含symbol列"
``` 