import numpy as np
import pandas as pd
from typing import Callable, Union
from joblib import wrap_non_picklable_objects
import talib
import sys
from pathlib import Path
# path_root = Path(__file__).parent.absolute().parent.parent.parent
# if str(path_root) not in sys.path:
#     sys.path.append(str(path_root))

from config import cfg_mine
from datafeed.features.feature_utils import FeatPreprocessing

__all__ = ['make_function']

'''  NOTE: 共152个funcs, 但函数定义多4个, 待仔细检查发现  '''
class _Function(object):

    """A representation of a mathematical relationship, a node in a program.

    This object is able to be called with NumPy vectorized arguments and return
    a resulting vector based on a mathematical relationship.

    Parameters
    ----------
    function : callable
        A function with signature function(x1, *args) that returns a Numpy
        array of the same shape as its arguments.

    name : str
        The name for the function as it should be represented in the program
        and its visualizations.

    arity : int
        The number of arguments that the ``function`` takes.
    """
    def __init__(self, function, name, arity):
        self.function = function
        self.name = name
        self.arity = arity

    def __call__(self, *args):
        return self.function(*args)


def make_function(*, function, name, arity, wrap=True):
    """Make a function node, a representation of a mathematical relationship.

    This factory function creates a function node, one of the core nodes in any
    program. The resulting object is able to be called with NumPy vectorized
    arguments and return a resulting vector based on a mathematical
    relationship.

    Parameters
    ----------
    function : callable
        A function with signature `function(x1, *args)` that returns a Numpy
        array of the same shape as its arguments.
    name : str
        The name for the function as it should be represented in the program
        and its visualizations.
    arity : int
        The number of arguments that the `function` takes.
    wrap : bool, optional (default=True)
        When running in parallel, pickling of custom functions is not supported
        by Python's default pickler. This option will wrap the function using
        cloudpickle allowing you to pickle your solution, but the evolution may
        run slightly more slowly. If you are running single-threaded in an
        interactive Python session or have no need to save the model, set to
        `False` for faster runs.

    """
    if not isinstance(arity, int):
        raise ValueError('arity must be an int, got %s' % type(arity))
    if not isinstance(function, np.ufunc):
        if function.__code__.co_argcount != arity:
            raise ValueError('arity %d does not match required number of '
                             'function arguments of %d.'
                             % (arity, function.__code__.co_argcount))
    if not isinstance(name, str):
        raise ValueError('name must be a string, got %s' % type(name))
    if not isinstance(wrap, bool):
        raise ValueError('wrap must be an bool, got %s' % type(wrap))

    # Check output shape
    args = [np.ones(10) for _ in range(arity)]
    try:
        function(*args)
    except (ValueError, TypeError):
        raise ValueError('supplied function %s does not support arity of %d.'
                         % (name, arity))
    if not hasattr(function(*args), 'shape'):
        raise ValueError('supplied function %s does not return a numpy array.'
                         % name)
    if function(*args).shape != (10,):
        raise ValueError('supplied function %s does not return same shape as '
                         'input vectors.' % name)

    # Check closure for zero & negative input arguments
    args = [np.zeros(10) for _ in range(arity)]
    if not np.all(np.isfinite(function(*args))):
        raise ValueError('supplied function %s does not have closure against '
                         'zeros in argument vectors.' % name)
    args = [-1 * np.ones(10) for _ in range(arity)]
    if not np.all(np.isfinite(function(*args))):
        raise ValueError('supplied function %s does not have closure against '
                         'negatives in argument vectors.' % name)

    if wrap:
        return _Function(function=wrap_non_picklable_objects(function),
                         name=name,
                         arity=arity)
    return _Function(function=function,
                     name=name,
                     arity=arity)

# def norm(x): # X.k原版norm
#     factors_data = pd.DataFrame(x, columns=['factor'])
#     factors_data = factors_data.replace([np.inf, -np.inf, np.nan], 0.0)
#     factors_mean = factors_data.cumsum() / np.arange(1, factors_data.shape[0] + 1)[:, np.newaxis]
#     factors_std = factors_data.expanding().std()
#     factor_value = (factors_data - factors_mean) / factors_std
#     factor_value = factor_value.replace([np.inf, -np.inf, np.nan], 0.0)
#     factor_value = factor_value.clip(-6, 6)
#     x = np.nan_to_num(factor_value['factor'].values)
#     return x
''' ========================== gp挖掘中所有funcs都调用norm() --- 除了常数 ===================== '''
X_params = cfg_mine.norm_X

def norm(x:np.ndarray) -> np.ndarray: # 替换为滚动norm函数
    x_norm = FeatPreprocessing.norm(x, **X_params)
    return x_norm

def _to_numpy(x):
    if isinstance(x, pd.Series):
        x1 = x.to_numpy()
    elif isinstance(x, np.ndarray):
        x1 = x
    return x1

def _flatten(x):
    if isinstance(x, pd.Series):
        x1 = x.to_numpy().flatten()
    elif isinstance(x, np.ndarray):
        x1 = x.flatten()
    return x1

''' =======================  下面正式开始func的集合映射 ==================== '''
def _ts_protected_division(x1, x2):
    """Closure of division (x1/x2) for zero denominator."""
    with np.errstate(divide='ignore', invalid='ignore'):
        return norm(np.where(np.abs(x2) > 0.001, np.divide(x1, x2), 1.))
    
    # np.log1p(x) log(e,(x + 1)) == np.nan = 0

def _ts_protected_sqrt(x1):
    """Closure of square root for negative arguments."""
    return norm(np.sqrt(np.abs(x1)))


def _ts_protected_log(x1):
    """Closure of log for zero and negative arguments."""
    with np.errstate(divide='ignore', invalid='ignore'):
        return norm(np.where(np.abs(x1) > 0.001, np.log(np.abs(x1)), 0.))


def _ts_protected_inverse(x1):
    """Closure of inverse for zero arguments."""
    with np.errstate(divide='ignore', invalid='ignore'):
        return norm(np.where(np.abs(x1) > 0.001, 1. / x1, 0.))

def _ts_sigmoid(x1):
    """Special case of logistic function to transform to probabilities."""
    with np.errstate(over='ignore', under='ignore'):
        return norm(1 / (1 + np.exp(-x1)))

def _ts_tanh(x1):
    with np.errstate(over='ignore', under='ignore'):
        return norm(np.tanh(x1))

def _ts_elu(x1):
    with np.errstate(over='ignore', under='ignore'):
        x = (np.where(x1 > 0, x1, 1 * (np.exp(x1) - 1)))
        return norm(x)

# def _ta_ht_trendline(x1):
#     x1 = _flatten(x1)
#     x = (np.nan_to_num(talib.HT_TRENDLINE(x1)))
#     return norm(x)

# def _ta_ht_dcperiod(x1):
#     x1 = _flatten(x1)
#     x = (np.nan_to_num(talib.HT_DCPERIOD(x1)))
#     return norm(x)

# def _ta_ht_dcphase(x1):
#     x1 = _flatten(x1)
#     x = (np.nan_to_num(talib.HT_DCPHASE(x1)))
#     return norm(x)

def _ta_sar(x1, x2):
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x = (np.nan_to_num(talib.SAR(x1, x2)))
    return norm(x)

def _ta_bop(x1, x2, x3, x4):
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x4 = _flatten(x4)
    x = (np.nan_to_num(talib.BOP(x1, x2, x3, x4)))
    return norm(x)

def _ta_bop_0(x1, x2, x3, x4):
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x4 = _flatten(x4)
    x = (np.nan_to_num(talib.BOP(x1, x2, x3, x4)))
    return norm(x)

def _ta_ad(x1, x2, x3, x4):
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x4 = _flatten(x4)
    x = (np.nan_to_num(talib.AD(x1, x2, x3, x4)))
    return norm(x)

# ma(df.close, 8)

# def _ta_obv(x1, x2):
#     x1 = _flatten(x1)
#     x2 = _flatten(x2)
#     x = (np.nan_to_num(talib.OBV(x1, x2)))
#     return norm(x)


def _ta_trange(x1, x2, x3):
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = (np.nan_to_num(talib.TRANGE(x1, x2, x3)))
    return norm(x)

# 截至20230522只有这些因子，需要把带有t的引进

# def _ts_cov_20(x1, x2):
#     t = 20
#     x1 = _flatten(x1)
#     x2 = _flatten(x2)
#     x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).cov(pd.Series(x2)))
#     return norm(x)

# def _ts_cov_40(x1, x2):
#     t = 40
#     x1 = _flatten(x1)
#     x2 = _flatten(x2)
#     x = (np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).cov(pd.Series(x2))))
#     return norm(x)

# def _ts_corr_20(x1, x2):
#     t = 20
#     x1 = _flatten(x1)
#     x2 = _flatten(x2)
#     x = (np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).corr(pd.Series(x2))))
#     return norm(x)

# def _ts_corr_40(x1, x2):
#     t = 40
#     x1 = _flatten(x1)
#     x2 = _flatten(x2)
#     x = (np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).corr(pd.Series(x2))))
#     return norm(x)

def _ts_day_min_10(x1):
    t = 10
    x1 = _flatten(x1)
    if len(x1) < t + 1:
        return np.full(len(x1), 0.0)
    
    x = np.lib.stride_tricks.sliding_window_view(x1, window_shape=t+1)
    min_indices = np.argmin(x[:, :-1], axis=1) # 计算每个窗口的最小值位置
    intervals = t - min_indices
	
    result = np.full(len(x1), np.nan)      # 将结果放到预分配的数组中
    result[t:] = intervals
    
    return norm(np.nan_to_num(result))

def _ts_day_min_20(x1):  # the i_th element is the interval between the min_value time point and t_i in the n-period time series from the past (t_i is not included)
    t = 20
    x1 = _flatten(x1)
    if len(x1) < t + 1:
        return np.full(len(x1), 0.0)
    
    x = np.lib.stride_tricks.sliding_window_view(x1, window_shape=t+1)
    min_indices = np.argmin(x[:, :-1], axis=1) # 计算每个窗口的最小值位置
    intervals = t - min_indices
	
    result = np.full(len(x1), np.nan)      # 将结果放到预分配的数组中
    result[t:] = intervals
    
    return norm(np.nan_to_num(result))

def _ts_day_min_40(x1):  # the i_th element is the interval between the min_value time point and t_i in the n-period time series from the past (t_i is not included)
    t = 40
    x1 = _flatten(x1)
    if len(x1) < t + 1:
        return np.full(len(x1), 0.0)
    
    x = np.lib.stride_tricks.sliding_window_view(x1, window_shape=t+1)
    min_indices = np.argmin(x[:, :-1], axis=1) # 计算每个窗口的最小值位置
    intervals = t - min_indices
	
    result = np.full(len(x1), np.nan)      # 将结果放到预分配的数组中
    result[t:] = intervals
    
    return norm(np.nan_to_num(result))

def _ts_day_max_10(x1):
    t = 10
    x1_flat = _flatten(x1)
    if len(x1_flat) < t + 1:
        return np.full(len(x1_flat), 0.0)
    
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t+1)
    max_indices = np.argmax(x[:, :-1], axis=1)
    intervals = t - max_indices

    result = np.full(len(x1_flat), np.nan)
    result[t:] = intervals
    
    return norm(np.nan_to_num(result))

def _ts_day_max_20(x1):  # the i_th element is the interval between the max_value time point and t_i in the n-period time series from the past (t_i is not included)
    t = 20
    x1_flat = _flatten(x1)
    if len(x1_flat) < t + 1:
        return np.full(len(x1_flat), 0.0)
    
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t+1)
    max_indices = np.argmax(x[:, :-1], axis=1)
    intervals = t - max_indices

    result = np.full(len(x1_flat), np.nan)
    result[t:] = intervals
    
    return norm(np.nan_to_num(result))

def _ts_day_max_40(x1):  # the i_th element is the interval between the max_value time point and t_i in the n-period time series from the past (t_i is not included)
    t = 40
    x1_flat = _flatten(x1)
    if len(x1_flat) < t + 1:
        return np.full(len(x1_flat), 0.0)
    
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t+1)
    max_indices = np.argmax(x[:, :-1], axis=1)
    intervals = t - max_indices

    result = np.full(len(x1_flat), np.nan)
    result[t:] = intervals
    
    return norm(np.nan_to_num(result))

def _ts_sma_8(x1):  # the i_th element is the simple moving average of the elements in the n-period time series from the past
    t = 8
    x1 = _flatten(x1)
    x = (np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).mean()))
    return norm(x)

def _ts_sma_21(x1):  # the i_th element is the simple moving average of the elements in the n-period time series from the past
    t = 21
    x1 = _flatten(x1)
    x = (np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).mean()))
    return norm(x)

def _ts_sma_55(x1):  # the i_th element is the simple moving average of the elements in the n-period time series from the past
    t = 55
    x1 = _flatten(x1)
    x = (np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).mean()))
    return norm(x)

def _ts_wma_8(x1): # the i_th element is the weighted moving average of the elements in the n-period time series from the past
    t = 8
    x1_flat = _flatten(x1)
    # 检查x1的长度是否足够
    if len(x1_flat) < t:
        return np.full(len(x1_flat), 0.0)
    
    weight_list = np.arange(1, t + 1) # 计算权重
    weight_list = weight_list / np.sum(weight_list)
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    wma = np.dot(x, weight_list) # 计算加权移动平均
    result = np.full(len(x1_flat), np.nan)
    result[t-1:] = wma
    
    return norm(np.nan_to_num(result))

def _ts_wma_21(x1):  # the i_th element is the weighted moving average of the elements in the n-period time series from the past
    t = 21
    x1_flat = _flatten(x1)
    # 检查x1的长度是否足够
    if len(x1_flat) < t:
        return np.full(len(x1_flat), 0.0)
    
    weight_list = np.arange(1, t + 1) # 计算权重
    weight_list = weight_list / np.sum(weight_list)
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    wma = np.dot(x, weight_list) # 计算加权移动平均
    result = np.full(len(x1_flat), np.nan)
    result[t-1:] = wma
    
    return norm(np.nan_to_num(result))

def _ts_wma_55(x1):  # the i_th element is the weighted moving average of the elements in the n-period time series from the past
    t = 55
    x1_flat = _flatten(x1)
    # 检查x1的长度是否足够
    if len(x1_flat) < t:
        return np.full(len(x1_flat), 0.0)
    
    weight_list = np.arange(1, t + 1) # 计算权重
    weight_list = weight_list / np.sum(weight_list)
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    wma = np.dot(x, weight_list) # 计算加权移动平均
    result = np.full(len(x1_flat), np.nan)
    result[t-1:] = wma
    
    return norm(np.nan_to_num(result))

def _ts_lag_3(x1):
    t = 3
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).shift(periods=t))
    return norm(x)

def _ts_lag_8(x1):
    t = 8
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).shift(periods=t))
    return norm(x)

def _ts_lag_17(x1):
    t = 17
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).shift(periods=t))
    return norm(x)

def _ts_delta_3(x1):
    t = 3
    x1 = _flatten(x1)
    x = np.nan_to_num(x1 - np.nan_to_num(pd.Series(x1).shift(periods=t)))
    return norm(x)

def _ts_delta_8(x1):
    t = 8
    x1 = _flatten(x1)
    x = np.nan_to_num(x1 - np.nan_to_num(pd.Series(x1).shift(periods=t)))
    return norm(x)

def _ts_delta_17(x1):
    t = 17
    x1 = _flatten(x1)
    x = np.nan_to_num(x1 - np.nan_to_num(pd.Series(x1).shift(periods=t)))
    return norm(x)

def _ts_sum_3(x1):  # 性能提升1700倍!
    t = 3
    x1_flat = _flatten(x1)
    # 检查x1的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    x1_flat = np.nan_to_num(x1_flat) # for this func only...
    result = np.full(len(x1_flat), np.nan) # 预先分配一个全是NaN的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t): # min_periods处理
        x_min_periods = x1_flat[:i]
        sums = np.sum(x_min_periods) # for sum
        result[i-1] = sums        
        
    sums = np.sum(x, axis=1)
    result[t-1:] = sums
    
    return norm(np.nan_to_num(result))

def _ts_sum_8(x1):  # the i_th element is the sum of the elements in the n-period time series from the past
    t = 8
    x1_flat = _flatten(x1)
    # 检查x1的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    x1_flat = np.nan_to_num(x1_flat) # for this func only...
    result = np.full(len(x1_flat), np.nan) # 预先分配一个全是NaN的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t): # min_periods处理
        x_min_periods = x1_flat[:i]
        sums = np.sum(x_min_periods) # for sum
        result[i-1] = sums        
        
    sums = np.sum(x, axis=1)
    result[t-1:] = sums
    
    return norm(np.nan_to_num(result))

def _ts_sum_17(x1):  # the i_th element is the sum of the elements in the n-period time series from the past
    t = 17
    x1_flat = _flatten(x1)
    # 检查x1的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    x1_flat = np.nan_to_num(x1_flat) # for this func only...
    result = np.full(len(x1_flat), np.nan) # 预先分配一个全是NaN的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t): # min_periods处理
        x_min_periods = x1_flat[:i]
        sums = np.sum(x_min_periods) # for sum
        result[i-1] = sums        
        
    sums = np.sum(x, axis=1)
    result[t-1:] = sums
    
    return norm(np.nan_to_num(result))

def _ts_prod_3(x1): # 提升1200倍!
    t = 3
    x1_flat = _flatten(x1)
    
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算乘积
        prods = np.prod(x_min_periods)
        result[i-1] = prods
    # 对滑动窗口内的元素计算乘积
    prods = np.prod(x, axis=1)
    result[t-1:] = prods
    
    return norm(np.nan_to_num(result))

def _ts_prod_8(x1):  # the i_th element is the production of the elements in the n-period time series from the past
    t = 8
    x1_flat = _flatten(x1)
    
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算乘积
        prods = np.prod(x_min_periods)
        result[i-1] = prods
    # 对滑动窗口内的元素计算乘积
    prods = np.prod(x, axis=1)
    result[t-1:] = prods
    
    return norm(np.nan_to_num(result))

def _ts_prod_17(x1):  # the i_th element is the production of the elements in the n-period time series from the past
    t = 17
    x1_flat = _flatten(x1)
    
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算乘积
        prods = np.prod(x_min_periods)
        result[i-1] = prods
    # 对滑动窗口内的元素计算乘积
    prods = np.prod(x, axis=1)
    result[t-1:] = prods
    
    return norm(np.nan_to_num(result))

def _ts_std_10(x1):  # the i_th element is the standard deviation of the elements in the n-period time series from the past
    t = 10
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).std())
    return norm(x)

def _ts_std_20(x1):  # the i_th element is the standard deviation of the elements in the n-period time series from the past
    t = 20
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).std())
    return norm(x)

def _ts_std_40(x1):  # the i_th element is the standard deviation of the elements in the n-period time series from the past
    t = 40
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).std())
    return norm(x)

def _ts_skew_10(x1):  # the i_th element is the skewness of the elements in the n-period time series from the past
    t = 10
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).skew())
    return norm(x)

def _ts_skew_20(x1):  # the i_th element is the skewness of the elements in the n-period time series from the past
    t = 20
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).skew())
    return norm(x)

def _ts_skew_40(x1):  # the i_th element is the skewness of the elements in the n-period time series from the past
    t = 40
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).skew())
    return norm(x)

def _ts_kurt_10(x1):  # the i_th element is the kurtosis of the elements in the n-period time series from the past
    t = 10
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).kurt())
    return norm(x)

def _ts_kurt_20(x1):  # the i_th element is the kurtosis of the elements in the n-period time series from the past
    t = 20
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).kurt())
    return norm(x)

def _ts_kurt_40(x1):  # the i_th element is the kurtosis of the elements in the n-period time series from the past
    t = 40
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).kurt())
    return norm(x)

def _ts_min_5(x1):  # the i_th element is the minimum value in the n-period time series from the past
    t = 5
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).min())
    return norm(x)

def _ts_min_10(x1):  # the i_th element is the minimum value in the n-period time series from the past
    t = 10
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).min())
    return norm(x)

def _ts_min_20(x1):  # the i_th element is the minimum value in the n-period time series from the past
    t = 20
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).min())
    return norm(x)

def _ts_max_5(x1):  # the i_th element is the maximum value in the n-period time series from the past
    t = 5
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).max())
    return norm(x)

def _ts_max_10(x1):  # the i_th element is the maximum value in the n-period time series from the past
    t = 10
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).max())
    return norm(x)

def _ts_max_20(x1):  # the i_th element is the maximum value in the n-period time series from the past
    t = 20
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).max())
    return norm(x)

def _ts_range_5(x1):
    t = 5
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).max()) - np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).min())
    return norm(x)

def _ts_range_10(x1):
    t = 10
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).max()) - np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).min())
    return norm(x)

def _ts_range_20(x1):
    t = 20
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).max()) - np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).min())
    return norm(x)

def _ts_argmin_5(x1): # 性能提升1000倍!
    t = 5
    x1_flat = _flatten(x1)
    if len(x1_flat) < t: # 检查 x1 的长度是否足够
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算最小值的位置
        argmins = np.argmin(x_min_periods) + 1  # +1 是因为 argmin 返回的是基于 0 的索引
        result[i-1] = argmins
    
    # 对滑动窗口内的元素计算最小值的位置
    argmins = np.argmin(x, axis=1) + 1  # +1 是因为 argmin 返回的是基于 0 的索引
    result[t-1:] = argmins
    
    return norm(np.nan_to_num(result))

def _ts_argmin_10(x1):  # the i_th element is the location of the minimum value in the n-period time series from the past
    t = 10
    x1_flat = _flatten(x1)
    if len(x1_flat) < t: # 检查 x1 的长度是否足够
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算最小值的位置
        argmins = np.argmin(x_min_periods) + 1  # +1 是因为 argmin 返回的是基于 0 的索引
        result[i-1] = argmins
    
    # 对滑动窗口内的元素计算最小值的位置
    argmins = np.argmin(x, axis=1) + 1  # +1 是因为 argmin 返回的是基于 0 的索引
    result[t-1:] = argmins
    
    return norm(np.nan_to_num(result))

def _ts_argmin_20(x1):  # the i_th element is the location of the minimum value in the n-period time series from the past
    t = 20
    x1_flat = _flatten(x1)
    if len(x1_flat) < t: # 检查 x1 的长度是否足够
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算最小值的位置
        argmins = np.argmin(x_min_periods) + 1  # +1 是因为 argmin 返回的是基于 0 的索引
        result[i-1] = argmins
    
    # 对滑动窗口内的元素计算最小值的位置
    argmins = np.argmin(x, axis=1) + 1  # +1 是因为 argmin 返回的是基于 0 的索引
    result[t-1:] = argmins
    
    return norm(np.nan_to_num(result))

def _ts_argmax_5(x1): # 提升1000倍!
    t = 5
    x1_flat = _flatten(x1)
    
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算最大值的位置
        argmaxs = np.argmax(x_min_periods) + 1  # +1 是因为 argmax 返回的是基于 0 的索引
        result[i-1] = argmaxs
    
    # 对滑动窗口内的元素计算最大值的位置
    argmaxs = np.argmax(x, axis=1) + 1  # +1 是因为 argmax 返回的是基于 0 的索引
    result[t-1:] = argmaxs
    
    return norm(np.nan_to_num(result))

def _ts_argmax_10(x1):  # the i_th element is the location of the maximum value in the n-period time series from the past
    t = 10
    x1_flat = _flatten(x1)
    
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算最大值的位置
        argmaxs = np.argmax(x_min_periods) + 1  # +1 是因为 argmax 返回的是基于 0 的索引
        result[i-1] = argmaxs
    
    # 对滑动窗口内的元素计算最大值的位置
    argmaxs = np.argmax(x, axis=1) + 1  # +1 是因为 argmax 返回的是基于 0 的索引
    result[t-1:] = argmaxs
    
    return norm(np.nan_to_num(result))

def _ts_argmax_20(x1):  # the i_th element is the location of the maximum value in the n-period time series from the past
    t = 20
    x1_flat = _flatten(x1)
    
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算最大值的位置
        argmaxs = np.argmax(x_min_periods) + 1  # +1 是因为 argmax 返回的是基于 0 的索引
        result[i-1] = argmaxs
    
    # 对滑动窗口内的元素计算最大值的位置
    argmaxs = np.argmax(x, axis=1) + 1  # +1 是因为 argmax 返回的是基于 0 的索引
    result[t-1:] = argmaxs
    
    return norm(np.nan_to_num(result))

def _ts_argrange_5(x1): # 性能提升1000倍!
    t = 5
    x1_flat = _flatten(x1)
    
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算最大值和最小值的位置
        argmaxs = np.argmax(x_min_periods) + 1  # +1 是因为 argmax 返回的是基于 0 的索引
        argmins = np.argmin(x_min_periods) + 1  # +1 是因为 argmin 返回的是基于 0 的索引
        result[i-1] = argmaxs - argmins
    
    # 对滑动窗口内的元素计算最大值和最小值的位置
    argmaxs = np.argmax(x, axis=1) + 1  # +1 是因为 argmax 返回的是基于 0 的索引
    argmins = np.argmin(x, axis=1) + 1  # +1 是因为 argmin 返回的是基于 0 的索引
    result[t-1:] = argmaxs - argmins
    
    return norm(np.nan_to_num(result))

def _ts_argrange_10(x1):
    t = 10
    x1_flat = _flatten(x1)
    
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算最大值和最小值的位置
        argmaxs = np.argmax(x_min_periods) + 1  # +1 是因为 argmax 返回的是基于 0 的索引
        argmins = np.argmin(x_min_periods) + 1  # +1 是因为 argmin 返回的是基于 0 的索引
        result[i-1] = argmaxs - argmins
    
    # 对滑动窗口内的元素计算最大值和最小值的位置
    argmaxs = np.argmax(x, axis=1) + 1  # +1 是因为 argmax 返回的是基于 0 的索引
    argmins = np.argmin(x, axis=1) + 1  # +1 是因为 argmin 返回的是基于 0 的索引
    result[t-1:] = argmaxs - argmins
    
    return norm(np.nan_to_num(result))

def _ts_argrange_20(x1):
    t = 20
    x1_flat = _flatten(x1)
    
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算最大值和最小值的位置
        argmaxs = np.argmax(x_min_periods) + 1  # +1 是因为 argmax 返回的是基于 0 的索引
        argmins = np.argmin(x_min_periods) + 1  # +1 是因为 argmin 返回的是基于 0 的索引
        result[i-1] = argmaxs - argmins
    
    # 对滑动窗口内的元素计算最大值和最小值的位置
    argmaxs = np.argmax(x, axis=1) + 1  # +1 是因为 argmax 返回的是基于 0 的索引
    argmins = np.argmin(x, axis=1) + 1  # +1 是因为 argmin 返回的是基于 0 的索引
    result[t-1:] = argmaxs - argmins
    
    return norm(np.nan_to_num(result))

def _ts_rank_5(x1): # 性能提升20倍!
    t = 5
    x1_flat = _flatten(x1)
    
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算最后一个元素的分位数
        ranks = np.sum(x_min_periods <= x_min_periods[-1]) / len(x_min_periods)
        result[i-1] = ranks
    
    # 对滑动窗口内的元素计算最后一个元素的分位数
    ranks = np.array([np.sum(window <= window[-1]) / len(window) for window in x])
    result[t-1:] = ranks
    
    return norm(np.nan_to_num(result))

def _ts_rank_10(x1):  # the i_th element is the quantile of the last element in the n-period time series from the past
    t = 10
    x1_flat = _flatten(x1)
    
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算最后一个元素的分位数
        ranks = np.sum(x_min_periods <= x_min_periods[-1]) / len(x_min_periods)
        result[i-1] = ranks
    
    # 对滑动窗口内的元素计算最后一个元素的分位数
    ranks = np.array([np.sum(window <= window[-1]) / len(window) for window in x])
    result[t-1:] = ranks
    
    return norm(np.nan_to_num(result))

def _ts_rank_20(x1):  # the i_th element is the quantile of the last element in the n-period time series from the past
    t = 20
    x1_flat = _flatten(x1)
    
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算最后一个元素的分位数
        ranks = np.sum(x_min_periods <= x_min_periods[-1]) / len(x_min_periods)
        result[i-1] = ranks
    
    # 对滑动窗口内的元素计算最后一个元素的分位数
    ranks = np.array([np.sum(window <= window[-1]) / len(window) for window in x])
    result[t-1:] = ranks
    
    return norm(np.nan_to_num(result))

def _ts_mean_return_5(x1): # 性能提升3000倍!
    x1 = x1.astype(np.float32)
    t = 5
    x1_flat = _flatten(x1)
    
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        x_min_periods = x_min_periods[~np.isnan(x_min_periods)]
        # 计算百分比变化的均值
        pct_changes = np.diff(x_min_periods) / np.where(x_min_periods[:-1] != 0, x_min_periods[:-1], 1e-10)
        mean_returns = np.mean(pct_changes)
        result[i-1] = mean_returns
    
    # 对滑动窗口内的元素计算百分比变化的均值
    pct_changes = np.diff(x, axis=1) / np.where(x[:, :-1] != 0, x[:, :-1], 1e-10)
    mean_returns = np.nanmean(pct_changes, axis=1)
    result[t-1:] = mean_returns
    
    return norm(np.nan_to_num(result))

def _ts_mean_return_10(x1):
    x1 = x1.astype(np.float32)
    t = 10
    x1_flat = _flatten(x1)
    
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        x_min_periods = x_min_periods[~np.isnan(x_min_periods)]
        # 计算百分比变化的均值
        pct_changes = np.diff(x_min_periods) / np.where(x_min_periods[:-1] != 0, x_min_periods[:-1], 1e-10)
        mean_returns = np.mean(pct_changes)
        result[i-1] = mean_returns
    
    # 对滑动窗口内的元素计算百分比变化的均值
    pct_changes = np.diff(x, axis=1) / np.where(x[:, :-1] != 0, x[:, :-1], 1e-10)
    mean_returns = np.nanmean(pct_changes, axis=1)
    result[t-1:] = mean_returns
    
    return norm(np.nan_to_num(result))

def _ts_mean_return_20(x1):
    x1 = x1.astype(np.float32)
    t = 20
    x1_flat = _flatten(x1)
    
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        x_min_periods = x_min_periods[~np.isnan(x_min_periods)]
        # 计算百分比变化的均值
        pct_changes = np.diff(x_min_periods) / np.where(x_min_periods[:-1] != 0, x_min_periods[:-1], 1e-10)
        mean_returns = np.mean(pct_changes)
        result[i-1] = mean_returns
    
    # 对滑动窗口内的元素计算百分比变化的均值
    pct_changes = np.diff(x, axis=1) / np.where(x[:, :-1] != 0, x[:, :-1], 1e-10)
    mean_returns = np.nanmean(pct_changes, axis=1)
    result[t-1:] = mean_returns
    
    return norm(np.nan_to_num(result))

def _ta_beta_5(x1, x2):
    t = 5
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x = np.nan_to_num(talib.BETA(x1, x2, timeperiod=t))
    return norm(x)

def _ta_beta_10(x1, x2):
    t = 10
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x = np.nan_to_num(talib.BETA(x1, x2, timeperiod=t))
    return norm(x)

def _ta_beta_20(x1, x2):
    t = 20
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x = np.nan_to_num(talib.BETA(x1, x2, timeperiod=t))
    return norm(x)

def _ta_lr_slope_5(x1):
    t = 5
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.LINEARREG_SLOPE(x1, timeperiod=t))
    return norm(x)

def _ta_lr_slope_10(x1):
    t = 10
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.LINEARREG_SLOPE(x1, timeperiod=t))
    return norm(x)

def _ta_lr_slope_20(x1):
    t = 20
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.LINEARREG_SLOPE(x1, timeperiod=t))
    return norm(x)

def _ta_lr_intercept_5(x1):
    t = 5
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.LINEARREG_INTERCEPT(x1, timeperiod=t))
    return norm(x)

def _ta_lr_intercept_10(x1):
    t = 10
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.LINEARREG_INTERCEPT(x1, timeperiod=t))
    return norm(x)

def _ta_lr_intercept_20(x1):
    t = 20
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.LINEARREG_INTERCEPT(x1, timeperiod=t))
    return norm(x)

def _ta_lr_angle_5(x1):
    t = 5
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.LINEARREG_ANGLE(x1, timeperiod=t))
    return norm(x)

def _ta_lr_angle_10(x1):
    t = 10
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.LINEARREG_ANGLE(x1, timeperiod=t))
    return norm(x)

def _ta_lr_angle_20(x1):
    t = 20
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.LINEARREG_ANGLE(x1, timeperiod=t))
    return norm(x)

def _ta_tsf_5(x1):
    t = 5
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.TSF(x1, timeperiod=t))
    return norm(x)

def _ta_tsf_10(x1):
    t = 10
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.TSF(x1, timeperiod=t))
    return norm(x)

def _ta_tsf_20(x1):
    t = 20
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.TSF(x1, timeperiod=t))
    return norm(x)

def _ta_ema_8(x1):
    t = 8
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.EMA(x1, timeperiod=t))
    return norm(x)

def _ta_ema_21(x1):
    t = 21
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.EMA(x1, timeperiod=t))
    return norm(x)

def _ta_ema_55(x1):
    t = 55
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.EMA(x1, timeperiod=t))
    return norm(x)

def _ta_dema_8(x1):
    t = 8
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.DEMA(x1, timeperiod=t))
    return norm(x)

def _ta_dema_21(x1):
    t = 21
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.DEMA(x1, timeperiod=t))
    return norm(x)

def _ta_dema_55(x1):
    t = 55
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.DEMA(x1, timeperiod=t))
    return norm(x)

def _ta_kama_8(x1):
    t = 8
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.KAMA(x1, timeperiod=t))
    return norm(x)

def _ta_kama_21(x1):
    t = 21
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.KAMA(x1, timeperiod=t))
    return norm(x)

def _ta_kama_55(x1):
    t = 55
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.KAMA(x1, timeperiod=t))
    return norm(x)

def _ta_tema_8(x1):
    t = 8
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.TEMA(x1, timeperiod=t))
    return norm(x)

def _ta_tema_21(x1):
    t = 21
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.TEMA(x1, timeperiod=t))
    return norm(x)

def _ta_tema_55(x1):
    t = 55
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.TEMA(x1, timeperiod=t))
    return norm(x)

def _ta_trima_8(x1):
    t = 8
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.TRIMA(x1, timeperiod=t))
    return norm(x)

def _ta_trima_21(x1):
    t = 21
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.TRIMA(x1, timeperiod=t))
    return norm(x)

def _ta_trima_55(x1):
    t = 55
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.TRIMA(x1, timeperiod=t))
    return norm(x)

def _ta_rsi_6(x1):
    t = 6
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.RSI(x1, timeperiod=t))
    return norm(x)

def _ta_rsi_12(x1):
    t = 12
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.RSI(x1, timeperiod=t))
    return norm(x)

def _ta_rsi_24(x1):
    t = 24
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.RSI(x1, timeperiod=t))
    return norm(x)

def _ta_cmo_14(x1):
    t = 14
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.CMO(x1, timeperiod=t))
    return norm(x)

def _ta_cmo_25(x1):
    t = 25
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.CMO(x1, timeperiod=t))
    return norm(x)

def _ta_mom_12(x1):
    t = 12
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.MOM(x1, timeperiod=t))
    return norm(x)

def _ta_mom_25(x1):
    t = 25
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.MOM(x1, timeperiod=t))
    return norm(x)

def _ta_rocp_14(x1):
    t = 14
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.ROCP(x1, timeperiod=t))
    return norm(x)

def _ta_rocp_25(x1):
    t = 25
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.ROCP(x1, timeperiod=t))
    return norm(x)

def _ta_rocr_14(x1):
    t = 14
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.ROCR(x1, timeperiod=t))
    return norm(x)

def _ta_rocr_25(x1):
    t = 25
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.ROCR(x1, timeperiod=t))
    return norm(x)

def _ta_trix_8(x1):
    t = 8
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.TRIX(x1, timeperiod=t))
    return norm(x)

def _ta_trix_21(x1):
    t = 21
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.TRIX(x1, timeperiod=t))
    return norm(x)

def _ta_trix_55(x1):
    t = 55
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.TRIX(x1, timeperiod=t))
    return norm(x)

def _ta_adx_14(x1, x2, x3):
    t = 14
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.ADX(x1, x2, x3, timeperiod=t))
    return norm(x)

def _ta_adx_25(x1, x2, x3):
    t = 25
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.ADX(x1, x2, x3, timeperiod=t))
    return norm(x)

def _ta_adxr_14(x1, x2, x3):
    t = 14
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.ADXR(x1, x2, x3, timeperiod=t))
    return norm(x)

def _ta_adxr_25(x1, x2, x3):
    t = 25
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.ADXR(x1, x2, x3, timeperiod=t))
    return norm(x)

def _ta_aroonosc_14(x1, x2):
    t = 14
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x = np.nan_to_num(talib.AROONOSC(x1, x2, timeperiod=t))
    return norm(x)

def _ta_aroonosc_25(x1, x2):
    t = 25
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x = np.nan_to_num(talib.AROONOSC(x1, x2, timeperiod=t))
    return norm(x)

def _ta_cci_14(x1, x2, x3):
    t = 14
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.CCI(x1, x2, x3, timeperiod=t))
    return norm(x)

def _ta_cci_25(x1, x2, x3):
    t = 25
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.CCI(x1, x2, x3, timeperiod=t))
    return norm(x)

def _ta_dx_14(x1, x2, x3):
    t = 14
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.DX(x1, x2, x3, timeperiod=t))
    return norm(x)

def _ta_dx_25(x1, x2, x3):
    t = 25
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.DX(x1, x2, x3, timeperiod=t))
    return norm(x)

def _ta_mfi_14(x1, x2, x3, x4):
    t = 14
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x4 = _flatten(x4)
    x = np.nan_to_num(talib.MFI(x1, x2, x3, x4, timeperiod=t))
    return norm(x)

def _ta_mfi_25(x1, x2, x3, x4):
    t = 25
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x4 = _flatten(x4)
    x = np.nan_to_num(talib.MFI(x1, x2, x3, x4, timeperiod=t))
    return norm(x)

def _ta_minus_di_14(x1, x2, x3):
    t = 14
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.MINUS_DI(x1, x2, x3, timeperiod=t))
    return norm(x)

def _ta_minus_di_25(x1, x2, x3):
    t = 25
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.MINUS_DI(x1, x2, x3, timeperiod=t))
    return norm(x)

def _ta_minus_dm_14(x1, x2):
    t = 14
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x = np.nan_to_num(talib.MINUS_DM(x1, x2, timeperiod=t))
    return norm(x)

def _ta_minus_dm_25(x1, x2):
    t = 25
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x = np.nan_to_num(talib.MINUS_DM(x1, x2, timeperiod=t))
    return norm(x)

def _ta_willr_14(x1, x2, x3):
    t = 14
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.WILLR(x1, x2, x3, timeperiod=t))
    return norm(x)

def _ta_willr_25(x1, x2, x3):
    t = 25
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.WILLR(x1, x2, x3, timeperiod=t))
    return norm(x)

def _ta_atr_14(x1, x2, x3):
    t = 14
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.ATR(x1, x2, x3, timeperiod=t))
    return norm(x)

def _ta_atr_25(x1, x2, x3):
    t = 25
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.ATR(x1, x2, x3, timeperiod=t))
    return norm(x)

def _ta_natr_14(x1, x2, x3):
    t = 14
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.NATR(x1, x2, x3, timeperiod=t))
    return norm(x)

def _ta_natr_25(x1, x2, x3):
    t = 25
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.NATR(x1, x2, x3, timeperiod=t))
    return norm(x)

# 已加入因子：_ts_cov_20，_ts_cov_40, _ts_corr_20, _ts_corr_40, _ts_day_min_10, _ts_day_min_20, _ts_day_min_40
# _ts_day_max_10, _ts_day_max_20, _ts_day_max_40, _ts_sma_8, _ts_sma_21, _ts_sma_55
# _ts_wma_8, _ts_wma_21, _ts_wma_55, _ts_lag_3, _ts_lag_8, _ts_lag_17
# _ts_delta_3, _ts_delta_8, _ts_delta_17, _ts_sum_3, _ts_sum_8, _ts_sum_17
# _ts_prod_3, _ts_prod_8, _ts_prod_17, _ts_std_10, _ts_std_20, _ts_std_40
# _ts_skew_10, _ts_skew_20, _ts_skew_40, _ts_kurt_10, _ts_kurt_20, _ts_kurt_40
# _ts_min_5, _ts_min_10, _ts_min_20, _ts_max_5, _ts_max_10, _ts_max_20
# _ts_range_5, _ts_range_10, _ts_range_20, _ts_argmin_5, _ts_argmin_10, _ts_argmin_20
# _ts_argmax_5, _ts_argmax_10, _ts_argmax_20, _ts_argrange_5, _ts_argrange_10, _ts_argrange_20
# _ts_rank_5, _ts_rank_10, _ts_rank_20, _ts_mean_return_5, _ts_mean_return_10, _ts_mean_return_20
# _ta_beta_5, _ta_beta_10, _ta_beta_20,_ta_lr_intercept_5, _ta_lr_intercept_10, _ta_lr_intercept_20
# _ta_tsf_5, _ta_tsf_10, _ta_tsf_20,_ta_ema_8, _ta_ema_21, _ta_ema_55
# _ta_dema_8, _ta_dema_21, _ta_dema_55, _ta_kama_8, _ta_kama_21, _ta_kama_55
# _ta_tema_8, _ta_tema_21, _ta_tema_55, _ta_trima_8, _ta_trima_21, _ta_trima_55
# _ta_rsi_6, _ta_rsi_12, _ta_rsi_24, _ta_cmo_14, _ta_cmo_25, _ta_mom_12, _ta_mom_25
# _ta_roc_14, _ta_roc_25, _ta_rocp_14, _ta_rocp_25, _ta_rocr_14, _ta_rocr_25
# _ta_trix_8, _ta_trix_21, _ta_trix_55, _ta_adx_14, _ta_adx_25
# _ta_adxr_14, _ta_adxr_25, _ta_aroonosc_14, _ta_aroonosc_25, _ta_cci_14, _ta_cci_25
# _ta_dx_14, _ta_dx_25, _ta_mfi_14, _ta_mfi_25, _ta_minus_di_14, _ta_minus_di_25
# _ta_minus_dm_14, _ta_minus_dm_25, _ta_willr_14, _ta_willr_25, 
# _ta_atr_14, _ta_atr_25, _ta_natr_14, _ta_natr_25

# ======================= JZAL: add with gpquant: 33 =============== 
def _ts_mean(x1, x2):
    return norm((x1 + x2) / 2)

def _ts_square(x1):
    return norm(x1**2)

def _ts_cube(x1):
    return norm(x1**3)

def _ts_cbrt(x1):
    return norm(np.cbrt(x1))

def _ts_sign(x1):
    return norm(np.sign(x1))

def _ts_clear_by_cond(x1, x2, x3):
    """if x1 < x2 (keep NaN if and only if both x1 and x2 are NaN), then 0, else x3"""
    return norm(np.where(x1 < x2, 0, np.where(~np.isnan(x1) | ~np.isnan(x2), x3, np.nan)))


def _ts_if_then_else(x1, x2, x3):
    """if x1 is nonzero (keep NaN), then x2, else x3"""
    return norm(np.where(x1, x2, np.where(~np.isnan(x1), x3, np.nan)))


def _ts_if_cond_then_else(x1, x2, x3, x4):
    """if x1 < x2 (keep NaN if and only if both x1 and x2 are NaN), then x3, else x4"""
    return norm(np.where(x1 < x2, x3, np.where(~np.isnan(x1) | ~np.isnan(x2), x4, np.nan)))

def _ts_mean_5(x1):
    """moving average"""
    d = 5
    x = np.array(pd.Series(x1).rolling(d, min_periods=int(d / 2)).mean().values)
    return norm(x)

def _ts_mean_10(x1):
    """moving average"""
    d = 10
    x = np.array(pd.Series(x1).rolling(d, min_periods=int(d / 2)).mean().values)
    return norm(x)

def _ts_mean_20(x1):
    """moving average"""
    d = 20
    x= np.array(pd.Series(x1).rolling(d, min_periods=int(d / 2)).mean().values)
    return norm(x)

def _ts_mean_40(x1):
    """moving average"""
    d = 40
    x = np.array(pd.Series(x1).rolling(d, min_periods=int(d / 2)).mean().values)
    return norm(x)
	
def _ts_median_5(x1):
    """moving median"""
    d = 5
    x = np.array(pd.Series(x1).rolling(d, min_periods=int(d / 2)).median().values)
    return norm(x)

def _ts_median_10(x1):
    """moving median"""
    d = 10
    x = np.array(pd.Series(x1).rolling(d, min_periods=int(d / 2)).median().values)
    return norm(x)

def _ts_median_20(x1):
    """moving median"""
    d = 20
    x = np.array(pd.Series(x1).rolling(d, min_periods=int(d / 2)).median().values)
    return norm(x)

def _ts_midpoint_5(x1):
    """moving midpoint: (ts_max + ts_min) / 2"""
    return norm(_ts_max_5(x1) + _ts_min_5(x1))

def _ts_midpoint_10(x1):
    """moving midpoint: (ts_max + ts_min) / 2"""
    return norm(_ts_max_10(x1) + _ts_min_10(x1))

def _ts_midpoint_20(x1):
    """moving midpoint: (ts_max + ts_min) / 2"""
    return norm(_ts_max_20(x1) + _ts_min_20(x1))
	
def _ts_inverse_cv_10(x1):
    """moving inverse of coefficient of variance"""
    return _ts_protected_division(_ts_mean_10(x1), _ts_std_10(x1))  # _ts_protected_division 本身已norm过了!

def _ts_inverse_cv_20(x1):
    """moving inverse of coefficient of variance"""
    return _ts_protected_division(_ts_mean_20(x1), _ts_std_20(x1))

def _ts_inverse_cv_40(x1):
    """moving inverse of coefficient of variance"""
    return _ts_protected_division(_ts_mean_40(x1), _ts_std_40(x1))
	
def _ts_autocorr_20_3(x1):
    """moving autocorrelation coefficient between x and x lag i period"""
    d = 20
    i = 3
    x = np.array(pd.Series(x1).rolling(d, min_periods=int(d / 2)).corr(pd.Series(x1).shift(i)).values)
    return norm(x)

def _ts_autocorr_40_3(x1):
    """moving autocorrelation coefficient between x and x lag i period"""
    d = 40
    i = 3
    x = np.array(pd.Series(x1).rolling(d, min_periods=int(d / 2)).corr(pd.Series(x1).shift(i)).values)
    return norm(x)

def _ts_autocorr_20_8(x1):
    """moving autocorrelation coefficient between x and x lag i period"""
    d = 20
    i = 8
    x = np.array(pd.Series(x1).rolling(d, min_periods=int(d / 2)).corr(pd.Series(x1).shift(i)).values)
    return norm(x)

def _ts_autocorr_40_8(x1):
    """moving autocorrelation coefficient between x and x lag i period"""
    d = 40
    i = 8
    x = np.array(pd.Series(x1).rolling(d, min_periods=int(d / 2)).corr(pd.Series(x1).shift(i)).values)
    return norm(x)

def _ts_autocorr_20_17(x1):
    """moving autocorrelation coefficient between x and x lag i period"""
    d = 20
    i = 17
    x = np.array(pd.Series(x1).rolling(d, min_periods=int(d / 2)).corr(pd.Series(x1).shift(i)).values)
    return norm(x)

def _ts_autocorr_40_17(x1):
    """moving autocorrelation coefficient between x and x lag i period"""
    d = 40
    i = 17
    x = np.array(pd.Series(x1).rolling(d, min_periods=int(d / 2)).corr(pd.Series(x1).shift(i)).values)
    return norm(x)
	
def _ts_maxmin_5(x1):
    """moving maxmin normalization"""
    ts_max, ts_min = _ts_max_5(x1), _ts_min_5(x1)
    return _ts_protected_division(x1 - ts_min, ts_max - ts_min)

def _ts_maxmin_10(x1):
    """moving maxmin normalization"""
    ts_max, ts_min = _ts_max_10(x1), _ts_min_10(x1)
    return _ts_protected_division(x1 - ts_min, ts_max - ts_min)

def _ts_maxmin_20(x1):
    """moving maxmin normalization"""
    ts_max, ts_min = _ts_max_20(x1), _ts_min_20(x1)
    return _ts_protected_division(x1 - ts_min, ts_max - ts_min)


def _ts_zscore_10(x1):
    """moving zscore standardization"""
    return _ts_protected_division(x1 - _ts_mean_10(x1), _ts_std_10(x1))

def _ts_zscore_20(x1):
    """moving zscore standardization"""
    return _ts_protected_division(x1 - _ts_mean_20(x1), _ts_std_20(x1))

def _ts_zscore_40(x1):
    """moving zscore standardization"""
    return _ts_protected_division(x1 - _ts_mean_40(x1), _ts_std_40(x1))
# ======================= JZAL: end of gpquant =============== 

# ======================= 修改内嵌np函数为自定义(带norm) =======
def _ts_add(x1,x2):
    return norm(np.add(x1,x2))
def _ts_sub(x1,x2): # x1 - x2
    return norm(np.subtract(x1,x2))
def _ts_mul(x1,x2): 
    return norm(np.multiply(x1,x2))
def _ts_neg(x1):
    return norm(np.negative(x1))
def _ts_abs(x1):
    return norm(np.abs(x1))
def _ts_max(x1,x2):
    x1 = _to_numpy(x1)
    x2 = _to_numpy(x2)
    return norm(np.maximum(x1,x2))
def _ts_min(x1,x2):
    x1 = _to_numpy(x1)
    x2 = _to_numpy(x2)
    return norm(np.minimum(x1,x2))
def _ts_sin(x1):
    return norm(np.sin(x1))
def _ts_cos(x1):
    return norm(np.cos(x1))
def _ts_tan(x1):
    return norm(np.tan(x1))

# ======================= JZAL: 常数不norm (240722) ===========
def _ts_constant(v: float) -> Callable[[np.ndarray], np.ndarray]:
    """
    返回一个与给定数组 x 长度相同的常数数组，每个元素都为 v
    """
    def constant_function(x: np.ndarray) -> np.ndarray:
        return np.full(x.shape, v)
    return constant_function

_ts_cons_n30 = _ts_constant(-30.0)
_ts_cons_n10 = _ts_constant(-10.0)
_ts_cons_n5 = _ts_constant(-5.0)
_ts_cons_n2 = _ts_constant(-2.0)
_ts_cons_n1 = _ts_constant(-1.0)
_ts_cons_n05 = _ts_constant(-0.5)
_ts_cons_n001 = _ts_constant(-0.01)
_ts_cons_30 = _ts_constant(30.0)
_ts_cons_10 = _ts_constant(10.0)
_ts_cons_5 = _ts_constant(5.0)
_ts_cons_2 = _ts_constant(2.0)
_ts_cons_1 = _ts_constant(1.0)
_ts_cons_05 = _ts_constant(0.5) # 0.5
_ts_cons_001 = _ts_constant(0.01) # 0.01
# ======================= JZAL: up/down difference(udd)
def _ts_udd_10(x1,x2):
    N = 10
    x_diff = np.diff(x1, prepend=x1[0])
    udvd = np.where(x_diff > 0, x2, -x2)
    # 使用 'same' 模式进行卷积计算，保持结果长度与原始数据一致
    udvd_windowed = np.convolve(udvd, np.ones(N), 'same')
    udvd_windowed[x2 == 0] = 0
    return norm(udvd_windowed)

def _ts_udd_20(x1,x2):
    N = 20
    x_diff = np.diff(x1, prepend=x1[0])
    udvd = np.where(x_diff > 0, x2, -x2)
    # 使用 'same' 模式进行卷积计算，保持结果长度与原始数据一致
    udvd_windowed = np.convolve(udvd, np.ones(N), 'same')
    udvd_windowed[x2 == 0] = 0
    return norm(udvd_windowed)

def _ts_udd_40(x1,x2):
    N = 40
    x_diff = np.diff(x1, prepend=x1[0])
    udvd = np.where(x_diff > 0, x2, -x2)
    # 使用 'same' 模式进行卷积计算，保持结果长度与原始数据一致
    udvd_windowed = np.convolve(udvd, np.ones(N), 'same')
    udvd_windowed[x2 == 0] = 0
    return norm(udvd_windowed)

def _ts_udd_60(x1,x2):
    N = 60
    x_diff = np.diff(x1, prepend=x1[0])
    udvd = np.where(x_diff > 0, x2, -x2)
    # 使用 'same' 模式进行卷积计算，保持结果长度与原始数据一致
    udvd_windowed = np.convolve(udvd, np.ones(N), 'same')
    udvd_windowed[x2 == 0] = 0
    return norm(udvd_windowed)
# ===== JZAL: rvi
def _ts_rvi_(period: int) -> Callable[[Union[np.ndarray, pd.Series]], np.ndarray]: # 闭包方式
    def _ts_rvi_func(x: Union[np.ndarray, pd.Series]) -> np.ndarray:
        if isinstance(x, pd.Series):
            x = x.values.astype(np.float64)
        elif isinstance(x, np.ndarray):
            x = x.astype(np.float64)
        else:
            raise TypeError("输入必须是 pd.Series 或 np.ndarray 类型")

        std = talib.STDDEV(x, timeperiod=period)
        # 计算价格的变化
        x_diff = np.diff(x, prepend=x[0])
        # 正的和负的标准差
        pos_std = np.where(x_diff > 0, std, 0)
        neg_std = np.where(x_diff <= 0, std, 0)
        
        # 使用 EMA 计算平均正标准差和负标准差
        pos_std = np.nan_to_num(pos_std, nan=0.0)
        neg_std = np.nan_to_num(neg_std, nan=0.0)
        avg_pos_std = talib.EMA(pos_std, timeperiod=period)
        avg_neg_std = talib.EMA(neg_std, timeperiod=period)
        # 计算 RVI
        avg_pos_std = np.nan_to_num(avg_pos_std, nan=0.0)
        avg_neg_std = np.nan_to_num(avg_neg_std, nan=0.0)
        epsilon = 1e-10
        denominator = avg_pos_std + avg_neg_std
        denominator[denominator == 0] = epsilon
        rvi = 100 * avg_pos_std / denominator
        return norm(rvi)
    return _ts_rvi_func

_ts_rvi_7 = _ts_rvi_(7)
_ts_rvi_14 = _ts_rvi_(14)
_ts_rvi_21 = _ts_rvi_(21)
_ts_rvi_34 = _ts_rvi_(34)
_ts_rvi_55 = _ts_rvi_(55)
_ts_rvi_89 = _ts_rvi_(89)

# ============  JZAL: RSRS - 240911
def _ts_slope_pair(N):
    def _ts_slope_pair(x1, x2): # 性能提升200倍!
        N = 9
        x1 = _flatten(x1)
        x2 = _flatten(x2)
        slopes = np.full(len(x1), np.nan)  # 预先分配一个全是NaN的数组
        if len(x1) < N:
            return slopes
        # 使用向量化操作一次计算所有可能的x和y
        x = np.lib.stride_tricks.sliding_window_view(x2, window_shape=N)
        y = np.lib.stride_tricks.sliding_window_view(x1, window_shape=N)
        # 对每一个窗口计算斜率
        x_mean = x.mean(axis=1)
        y_mean = y.mean(axis=1)
        xy_cov = np.sum((x - x_mean[:, None]) * (y - y_mean[:, None]), axis=1)
        xx_var = np.sum((x - x_mean[:, None])**2, axis=1)

        with np.errstate(divide='ignore', invalid='ignore'):
            slopes_valid = np.where(xx_var != 0, xy_cov / xx_var, np.nan)
        # 将计算结果放到预分配的数组中
        slopes[N-1:] = slopes_valid
        
        return norm(slopes)  # normed
    
    return _ts_slope_pair

_ts_slope_pair_9 = _ts_slope_pair(9)
_ts_slope_pair_18 = _ts_slope_pair(18)
_ts_slope_pair_34 = _ts_slope_pair(34)
_ts_slope_pair_55 = _ts_slope_pair(55)
_ts_slope_pair_89 = _ts_slope_pair(89)

''' ================================  Make Function Classes ================================ '''

ts_add2 = _Function(function=_ts_add, name='ts_add2', arity=2)
ts_sub2 = _Function(function=_ts_sub, name='ts_sub2', arity=2)
ts_mul2 = _Function(function=_ts_mul, name='ts_mul2', arity=2)
ts_div2 = _Function(function=_ts_protected_division, name='ts_div2', arity=2)
ts_sqrt1 = _Function(function=_ts_protected_sqrt, name='ts_sqrt1', arity=1)
ts_log1 = _Function(function=_ts_protected_log, name='ts_log1', arity=1)
ts_neg1 = _Function(function=_ts_neg, name='ts_neg1', arity=1)
ts_inv1 = _Function(function=_ts_protected_inverse, name='ts_inv1', arity=1)
ts_abs1 = _Function(function=_ts_abs, name='ts_abs1', arity=1)
ts_max2 = _Function(function=_ts_max, name='ts_max2', arity=2)
ts_min2 = _Function(function=_ts_min, name='ts_min2', arity=2)
ts_sin1 = _Function(function=_ts_sin, name='ts_sin1', arity=1)
ts_cos1 = _Function(function=_ts_cos, name='ts_cos1', arity=1)
ts_tan1 = _Function(function=_ts_tan, name='ts_tan1', arity=1)
ts_sig1 = _Function(function=_ts_sigmoid, name='ts_sig1', arity=1)
# ======================= JZAL: add with gpquant: 33  =============== 
ts_mean2 = _Function(function=_ts_mean, name="ts_mean2", arity=2)	
ts_square1 = _Function(function=_ts_square, name="ts_square1", arity=1)
ts_cube1 = _Function(function=_ts_cube, name="ts_cube1", arity=1)
ts_cbrt1 = _Function(function=_ts_cbrt, name="ts_cbrt1", arity=1)
ts_sign1 = _Function(function=_ts_sign, name="ts_sign1", arity=1)
# condition funcs.
ts_clear_by_cond3 = _Function(function=_ts_clear_by_cond, name="ts_clear_by_cond3", arity=3)
ts_if_then_else3 = _Function(function=_ts_if_then_else, name="ts_if_then_else3", arity=3)
ts_if_cond_then_else4 = _Function(function=_ts_if_cond_then_else, name="ts_if_cond_then_else4", arity=4)
# time series
ts_mean_5 = _Function(function=_ts_mean_5, name="ts_mean_5", arity=1)
ts_mean_10 = _Function(function=_ts_mean_10, name="ts_mean_10", arity=1)
ts_mean_20 = _Function(function=_ts_mean_20, name="ts_mean_20", arity=1)
ts_mean_40 = _Function(function=_ts_mean_40, name="ts_mean_40", arity=1)
ts_median_5 = _Function(function=_ts_median_5, name="ts_median_5", arity=1)
ts_median_10 = _Function(function=_ts_median_10, name="ts_median_10", arity=1)
ts_median_20 = _Function(function=_ts_median_20, name="ts_median_20", arity=1)
ts_midpoint_5 = _Function(function=_ts_midpoint_5, name="ts_midpoint_5", arity=1)
ts_midpoint_10 = _Function(function=_ts_midpoint_10, name="ts_midpoint_10", arity=1)
ts_midpoint_20 = _Function(function=_ts_midpoint_20, name="ts_midpoint_20", arity=1)
ts_inverse_cv_10 = _Function(function=_ts_inverse_cv_10, name="ts_inverse_cv_10", arity=1)
ts_inverse_cv_20 = _Function(function=_ts_inverse_cv_20, name="ts_inverse_cv_20", arity=1)
ts_inverse_cv_40 = _Function(function=_ts_inverse_cv_40, name="ts_inverse_cv_40", arity=1)
ts_autocorr_20_3 = _Function(function=_ts_autocorr_20_3, name="ts_autocorr_20_3", arity=1)
ts_autocorr_40_3 = _Function(function=_ts_autocorr_40_3, name="ts_autocorr_40_3", arity=1)
ts_autocorr_20_8 = _Function(function=_ts_autocorr_20_8, name="ts_autocorr_20_8", arity=1)
ts_autocorr_40_8 = _Function(function=_ts_autocorr_40_8, name="ts_autocorr_40_8", arity=1)
ts_autocorr_20_17 = _Function(function=_ts_autocorr_20_17, name="ts_autocorr_20_17", arity=1)
ts_autocorr_40_17 = _Function(function=_ts_autocorr_40_17, name="ts_autocorr_40_17", arity=1)
ts_maxmin_5 = _Function(function=_ts_maxmin_5, name="ts_maxmin_5", arity=1)
ts_maxmin_10 = _Function(function=_ts_maxmin_10, name="ts_maxmin_10", arity=1)
ts_maxmin_20 = _Function(function=_ts_maxmin_20, name="ts_maxmin_20", arity=1)
ts_zscore_10 = _Function(function=_ts_zscore_10, name="ts_zscore_10", arity=1)
ts_zscore_20 = _Function(function=_ts_zscore_20, name="ts_zscore_20", arity=1)
ts_zscore_40 = _Function(function=_ts_zscore_40, name="ts_zscore_40", arity=1)
# ======================= JZAL: end of gpquant =============== 

# 增加部分的函数
ts_tanh1 = _Function(function=_ts_tanh, name='ts_tanh1', arity=1)
ts_elu1 = _Function(function=_ts_elu, name='ts_elu1', arity=1)
# ta_ht_trendline = _Function(function=_ta_ht_trendline, name='ta_ht_trendline', arity=1)
# ta_ht_dcperiod = _Function(function=_ta_ht_dcperiod, name='ta_ht_dcperiod', arity=1)
# ta_ht_dcphase = _Function(function=_ta_ht_dcphase, name='ta_ht_dcphase', arity=1)
ta_sar = _Function(function=_ta_sar, name='ta_sar', arity=2)
ta_bop = _Function(function=_ta_bop, name='ta_bop', arity=4)
ta_ad = _Function(function=_ta_ad, name='ta_ad', arity=4)
# ta_obv = _Function(function=_ta_obv, name='ta_obv', arity=2)
ta_trange = _Function(function=_ta_trange, name='ta_trange', arity=3)

# 5月23日加入的因子：
# 1-10:
# ts_cov_20 = _Function(function=_ts_cov_20, name='ts_cov_20', arity=2)
# ts_cov_40 = _Function(function=_ts_cov_40, name='ts_cov_40', arity=2)
# ts_corr_20 = _Function(function=_ts_corr_20, name='ts_corr_20', arity=2)
# ts_corr_40 = _Function(function=_ts_corr_40, name='ts_corr_40', arity=2)
ts_day_min_10 = _Function(function=_ts_day_min_10, name='ts_day_min_10', arity=1)
ts_day_min_20 = _Function(function=_ts_day_min_20, name='ts_day_min_20', arity=1)
ts_day_min_40 = _Function(function=_ts_day_min_40, name='ts_day_min_40', arity=1)
ts_day_max_10 = _Function(function=_ts_day_max_10, name='ts_day_max_10', arity=1)
ts_day_max_20 = _Function(function=_ts_day_max_20, name='ts_day_max_20', arity=1)
ts_day_max_40 = _Function(function=_ts_day_max_40, name='ts_day_max_40', arity=1)

# 11-19:
ts_sma_8 = _Function(function=_ts_sma_8, name='ts_sma_8', arity=1)
ts_sma_21 = _Function(function=_ts_sma_21, name='ts_sma_21', arity=1)
ts_sma_55 = _Function(function=_ts_sma_55, name='ts_sma_55', arity=1)
ts_wma_8 = _Function(function=_ts_wma_8, name='ts_wma_8', arity=1)
ts_wma_21 = _Function(function=_ts_wma_21, name='ts_wma_21', arity=1)
ts_wma_55 = _Function(function=_ts_wma_55, name='ts_wma_55', arity=1)
ts_lag_3 = _Function(function=_ts_lag_3, name='ts_lag_3', arity=1)
ts_lag_8 = _Function(function=_ts_lag_8, name='ts_lag_8', arity=1)
ts_lag_17 = _Function(function=_ts_lag_17, name='ts_lag_17', arity=1)

# 20-32
ts_delta_3 = _Function(function=_ts_delta_3, name='ts_delta_3', arity=1)
ts_delta_8 = _Function(function=_ts_delta_8, name='ts_delta_8', arity=1)
ts_delta_17 = _Function(function=_ts_delta_17, name='ts_delta_17', arity=1)
ts_sum_3 = _Function(function=_ts_sum_3, name='ts_sum_3', arity=1)
ts_sum_8 = _Function(function=_ts_sum_8, name='ts_sum_8', arity=1)
ts_sum_17 = _Function(function=_ts_sum_17, name='ts_sum_17', arity=1)
ts_prod_3 = _Function(function=_ts_prod_3, name='ts_prod_3', arity=1)
ts_prod_8 = _Function(function=_ts_prod_8, name='ts_prod_8', arity=1)
ts_prod_17 = _Function(function=_ts_prod_17, name='ts_prod_17', arity=1)
ts_std_10 = _Function(function=_ts_std_10, name='ts_std_10', arity=1)
ts_std_20 = _Function(function=_ts_std_20, name='ts_std_20', arity=1)
ts_std_40 = _Function(function=_ts_std_40, name='ts_std_40', arity=1)

# 33-44
ts_skew_10 = _Function(function=_ts_skew_10, name='ts_skew_10', arity=1)
ts_skew_20 = _Function(function=_ts_skew_20, name='ts_skew_20', arity=1)
ts_skew_40 = _Function(function=_ts_skew_40, name='ts_skew_40', arity=1)
ts_kurt_10 = _Function(function=_ts_kurt_10, name='ts_kurt_10', arity=1)
ts_kurt_20 = _Function(function=_ts_kurt_20, name='ts_kurt_20', arity=1)
ts_kurt_40 = _Function(function=_ts_kurt_40, name='ts_kurt_40', arity=1)
ts_min_5 = _Function(function=_ts_min_5, name='ts_min_5', arity=1)
ts_min_10 = _Function(function=_ts_min_10, name='ts_min_10', arity=1)
ts_min_20 = _Function(function=_ts_min_20, name='ts_min_20', arity=1)
ts_max_5 = _Function(function=_ts_max_5, name='ts_max_5', arity=1)
ts_max_10 = _Function(function=_ts_max_10, name='ts_max_10', arity=1)
ts_max_20 = _Function(function=_ts_max_20, name='ts_max_20', arity=1)

# 45-56
ts_range_5 = _Function(function=_ts_range_5, name='ts_range_5', arity=1)
ts_range_10 = _Function(function=_ts_range_10, name='ts_range_10', arity=1)
ts_range_20 = _Function(function=_ts_range_20, name='ts_range_20', arity=1)
ts_argmin_5 = _Function(function=_ts_argmin_5, name='ts_argmin_5', arity=1)
ts_argmin_10 = _Function(function=_ts_argmin_10, name='ts_argmin_10', arity=1)
ts_argmin_20 = _Function(function=_ts_argmin_20, name='ts_argmin_20', arity=1)
ts_argmax_5 = _Function(function=_ts_argmax_5, name='ts_argmax_5', arity=1)
ts_argmax_10 = _Function(function=_ts_argmax_10, name='ts_argmax_10', arity=1)
ts_argmax_20 = _Function(function=_ts_argmax_20, name='ts_argmax_20', arity=1)
ts_argrange_5 = _Function(function=_ts_argrange_5, name='ts_argrange_5', arity=1)
ts_argrange_10 = _Function(function=_ts_argrange_10, name='ts_argrange_10', arity=1)
ts_argrange_20 = _Function(function=_ts_argrange_20, name='ts_argrange_20', arity=1)

# 57-68
ts_rank_5 = _Function(function=_ts_rank_5, name='ts_rank_5', arity=1)
ts_rank_10 = _Function(function=_ts_rank_10, name='ts_rank_10', arity=1)
ts_rank_20 = _Function(function=_ts_rank_20, name='ts_rank_20', arity=1)
ts_mean_return_5 = _Function(function=_ts_mean_return_5, name='ts_mean_return_5', arity=1)
ts_mean_return_10 = _Function(function=_ts_mean_return_10, name='ts_mean_return_10', arity=1)
ts_mean_return_20 = _Function(function=_ts_mean_return_20, name='ts_mean_return_20', arity=1)
ta_beta_5 = _Function(function=_ta_beta_5, name='ta_beta_5', arity=2)
ta_beta_10 = _Function(function=_ta_beta_10, name='ta_beta_10', arity=2)
ta_beta_20 = _Function(function=_ta_beta_20, name='ta_beta_20', arity=2)
ta_lr_slope_5 = _Function(function=_ta_lr_slope_5, name='ta_lr_slope_5', arity=1)
ta_lr_slope_10 = _Function(function=_ta_lr_slope_10, name='ta_lr_slope_10', arity=1)
ta_lr_slope_20 = _Function(function=_ta_lr_slope_20, name='ta_lr_slope_20', arity=1)

# 69-80
ta_lr_intercept_5 = _Function(function=_ta_lr_intercept_5, name='ta_lr_intercept_5', arity=1)
ta_lr_intercept_10 = _Function(function=_ta_lr_intercept_10, name='ta_lr_intercept_10', arity=1)
ta_lr_intercept_20 = _Function(function=_ta_lr_intercept_20, name='ta_lr_intercept_20', arity=1)
ta_lr_angle_5 = _Function(function=_ta_lr_angle_5, name='ta_lr_angle_5', arity=1)
ta_lr_angle_10 = _Function(function=_ta_lr_angle_10, name='ta_lr_angle_10', arity=1)
ta_lr_angle_20 = _Function(function=_ta_lr_angle_20, name='ta_lr_angle_20', arity=1)
ta_tsf_5 = _Function(function=_ta_tsf_5, name='ta_tsf_5', arity=1)
ta_tsf_10 = _Function(function=_ta_tsf_10, name='ta_tsf_10', arity=1)
ta_tsf_20 = _Function(function=_ta_tsf_20, name='ta_tsf_20', arity=1)
ta_ema_8 = _Function(function=_ta_ema_8, name='ta_ema_8', arity=1)
ta_ema_21 = _Function(function=_ta_ema_21, name='ta_ema_21', arity=1)
ta_ema_55 = _Function(function=_ta_ema_55, name='ta_ema_55', arity=1)

# 81-92
ta_dema_8 = _Function(function=_ta_dema_8, name='ta_dema_8', arity=1)
ta_dema_21 = _Function(function=_ta_dema_21, name='ta_dema_21', arity=1)
ta_dema_55 = _Function(function=_ta_dema_55, name='ta_dema_55', arity=1)
ta_kama_8 = _Function(function=_ta_kama_8, name='ta_dema_8', arity=1)
ta_kama_21 = _Function(function=_ta_kama_21, name='ta_dema_21', arity=1)
ta_kama_55 = _Function(function=_ta_kama_55, name='ta_kama_55', arity=1)
ta_tema_8 = _Function(function=_ta_tema_8, name='ta_tema_8', arity=1)
ta_tema_21 = _Function(function=_ta_tema_21, name='ta_tema_21', arity=1)
ta_tema_55 = _Function(function=_ta_tema_55, name='ta_tema_55', arity=1)
ta_trima_8 = _Function(function=_ta_trima_8, name='ta_trima_8', arity=1)
ta_trima_21 = _Function(function=_ta_trima_21, name='ta_trima_21', arity=1)
ta_trima_55 = _Function(function=_ta_trima_55, name='ta_trima_55', arity=1)

# 93-105
ta_rsi_6 = _Function(function=_ta_rsi_6, name='ta_rsi_6', arity=1)
ta_rsi_12 = _Function(function=_ta_rsi_12, name='ta_rsi_12', arity=1)
ta_rsi_24 = _Function(function=_ta_rsi_24, name='ta_rsi_24', arity=1)
ta_cmo_14 = _Function(function=_ta_cmo_14, name='ta_cmo_14', arity=1)
ta_cmo_25 = _Function(function=_ta_cmo_25, name='ta_cmo_25', arity=1)
ta_mom_12 = _Function(function=_ta_mom_12, name='ta_mom_12', arity=1)
ta_mom_25 = _Function(function=_ta_mom_25, name='ta_mom_25', arity=1)
ta_rocp_14 = _Function(function=_ta_rocp_14, name='ta_rocp_14', arity=1)
ta_rocp_25 = _Function(function=_ta_rocp_25, name='ta_rocp_25', arity=1)
ta_rocr_14 = _Function(function=_ta_rocr_14, name='ta_rocr_14', arity=1)
ta_rocr_25 = _Function(function=_ta_rocr_25, name='ta_rocr_25', arity=1)

# 106-116
ta_trix_8 = _Function(function=_ta_trix_8, name='ta_trix_8', arity=1)
ta_trix_21 = _Function(function=_ta_trix_21, name='ta_trix_21', arity=1)
ta_trix_55 = _Function(function=_ta_trix_55, name='ta_trix_55', arity=1)
ta_adx_14 = _Function(function=_ta_adx_14, name='ta_adx_14', arity=3)
ta_adx_25 = _Function(function=_ta_adx_25, name='ta_adx_25', arity=3)
ta_adxr_14 = _Function(function=_ta_adxr_14, name='ta_adxr_14', arity=3)
ta_adxr_25 = _Function(function=_ta_adxr_25, name='ta_adxr_25', arity=3)
ta_aroonosc_14 = _Function(function=_ta_aroonosc_14, name='ta_aroonosc_14', arity=2)
ta_aroonosc_25 = _Function(function=_ta_aroonosc_25, name='ta_aroonosc_25', arity=2)
ta_cci_14 = _Function(function=_ta_cci_14, name='ta_cci_14', arity=3)
ta_cci_25 = _Function(function=_ta_cci_25, name='ta_cci_25', arity=3)

# 117-130
ta_dx_14 = _Function(function=_ta_dx_14, name='ta_dx_14', arity=3)
ta_dx_25 = _Function(function=_ta_dx_25, name='ta_dx_25', arity=3)
ta_mfi_14 = _Function(function=_ta_mfi_14, name='ta_mfi_14', arity=4)
ta_mfi_25 = _Function(function=_ta_mfi_25, name='ta_mfi_25', arity=4)
ta_minus_di_14 = _Function(function=_ta_minus_di_14, name='ta_minus_di_14', arity=3)
ta_minus_di_25 = _Function(function=_ta_minus_di_25, name='ta_minus_di_25', arity=3)
ta_minus_dm_14 = _Function(function=_ta_minus_dm_14, name='ta_minus_dm_14', arity=2)
ta_minus_dm_25 = _Function(function=_ta_minus_dm_25, name='ta_minus_dm_25', arity=2)
ta_willr_14 = _Function(function=_ta_willr_14, name='ta_willr_14', arity=3)
ta_willr_25 = _Function(function=_ta_willr_25, name='ta_willr_25', arity=3)
ta_atr_14 = _Function(function=_ta_atr_14, name='ta_atr_14', arity=3)
ta_atr_25 = _Function(function=_ta_atr_25, name='ta_atr_25', arity=3)
ta_natr_14 = _Function(function=_ta_natr_14, name='ta_natr_14', arity=3)
ta_natr_25 = _Function(function=_ta_natr_25, name='ta_natr_25', arity=3)

# 130 - 143
ts_cons_n30 = _Function(function=_ts_cons_n30, name='ts_cons_n30', arity=1)
ts_cons_n10 = _Function(function=_ts_cons_n10, name='ts_cons_n10', arity=1)
ts_cons_n5 = _Function(function=_ts_cons_n5, name='ts_cons_n5', arity=1)
ts_cons_n2 = _Function(function=_ts_cons_n2, name='ts_cons_n2', arity=1)
ts_cons_n1 = _Function(function=_ts_cons_n1, name='ts_cons_n1', arity=1)
ts_cons_n05 = _Function(function=_ts_cons_n05, name='ts_cons_n05', arity=1)
ts_cons_n001 = _Function(function=_ts_cons_n001, name='ts_cons_n001', arity=1)
ts_cons_30 = _Function(function=_ts_cons_30, name='ts_cons_30', arity=1)
ts_cons_10 = _Function(function=_ts_cons_10, name='ts_cons_10', arity=1)
ts_cons_5 = _Function(function=_ts_cons_5, name='ts_cons_5', arity=1)
ts_cons_2 = _Function(function=_ts_cons_2, name='ts_cons_2', arity=1)
ts_cons_1 = _Function(function=_ts_cons_1, name='ts_cons_1', arity=1)
ts_cons_05 = _Function(function=_ts_cons_05, name='ts_cons_05', arity=1)
ts_cons_001 = _Function(function=_ts_cons_001, name='ts_cons_001', arity=1)
# 144-147
ts_udd_10 = _Function(function=_ts_udd_10, name='ts_udd_10', arity=2)
ts_udd_20 = _Function(function=_ts_udd_20, name='ts_udd_20', arity=2)
ts_udd_40 = _Function(function=_ts_udd_40, name='ts_udd_40', arity=2)
ts_udd_60 = _Function(function=_ts_udd_60, name='ts_udd_60', arity=2)
# 148-152
ts_rvi_7 = _Function(function=_ts_rvi_7, name='ts_rvi_7', arity=1)
ts_rvi_14 = _Function(function=_ts_rvi_14, name='ts_rvi_14', arity=1)
ts_rvi_21 = _Function(function=_ts_rvi_21, name='ts_rvi_21', arity=1)
ts_rvi_34 = _Function(function=_ts_rvi_34, name='ts_rvi_34', arity=1)
ts_rvi_55 = _Function(function=_ts_rvi_55, name='ts_rvi_55', arity=1)
ts_rvi_89 = _Function(function=_ts_rvi_89, name='ts_rvi_89', arity=1)

# 153-157
ts_slope_pair_9 = _Function(function=_ts_slope_pair_9, name='ts_slope_pair_9', arity=2)
ts_slope_pair_18 = _Function(function=_ts_slope_pair_18, name='ts_slope_pair_18', arity=2)
ts_slope_pair_34 = _Function(function=_ts_slope_pair_34, name='ts_slope_pair_34', arity=2)
ts_slope_pair_55 = _Function(function=_ts_slope_pair_55, name='ts_slope_pair_55', arity=2)
ts_slope_pair_89 = _Function(function=_ts_slope_pair_89, name='ts_slope_pair_89', arity=2)

''' =================================== 185 funcs - 240611 ============================== '''
_function_map = {'ts_add2': ts_add2,
                 'ts_sub2': ts_sub2,
                 'ts_mul2': ts_mul2,
                 'ts_div2': ts_div2,
                 'ts_sqrt1': ts_sqrt1,
                 'ts_log1': ts_log1,
                 'ts_abs1': ts_abs1,
                 'ts_neg1': ts_neg1,
                 'ts_inv1': ts_inv1,
                 'ts_max2': ts_max2,
                 'ts_min2': ts_min2,
                 'ts_sin1': ts_sin1,
                 'ts_cos1': ts_cos1,
                 'ts_tan1': ts_tan1,
                 'ts_sig1': ts_sig1,
                 # 下面对应的是增加部分
                 'ts_tanh1': ts_tanh1,
                 'ts_elu1': ts_elu1,
                #  'ta_ht_trendline': ta_ht_trendline,
                #  'ta_ht_dcperiod': ta_ht_dcperiod,
                #  'ta_ht_dcphase': ta_ht_dcphase,
                 'ta_sar': ta_sar,
                 'ta_bop': ta_bop,
                 'ta_ad': ta_ad,
                #  'ta_obv': ta_obv,
                 'ta_trange': ta_trange,
                 # 5月23日加入
                 # 1-10：
                #  'ts_cov_20' : ts_cov_20,
                #  'ts_cov_40' : ts_cov_40,
                #  'ts_corr_20' : ts_corr_20,
                #  'ts_corr_40' : ts_corr_40,
                 'ts_day_min_10' : ts_day_min_10,
                 'ts_day_min_20' : ts_day_min_20,
                 'ts_day_min_40' : ts_day_min_40,
                 'ts_day_max_10' : ts_day_max_10,
                 'ts_day_max_20' : ts_day_max_20,
                 'ts_day_max_40' : ts_day_max_40,
                 # 11-19:
                 'ts_sma_8' : ts_sma_8,
                 'ts_sma_21' : ts_sma_21,
                 'ts_sma_55' : ts_sma_55,
                 'ts_wma_8' : ts_wma_8,
                 'ts_wma_21' : ts_wma_21,
                 'ts_wma_55' : ts_wma_55,
                 'ts_lag_3' : ts_lag_3,
                 'ts_lag_8' : ts_lag_8,
                 'ts_lag_17' : ts_lag_17,
                 # 20-32
                 'ts_delta_3' : ts_delta_3,
                 'ts_delta_8' : ts_delta_8,
                 'ts_delta_17' : ts_delta_17,
                 'ts_sum_3' : ts_sum_3,
                 'ts_sum_8' : ts_sum_8,
                 'ts_sum_17' : ts_sum_17,
                 'ts_prod_3' : ts_prod_3,
                 'ts_prod_8' : ts_prod_8,
                 'ts_prod_17' : ts_prod_17,
                 'ts_std_10' : ts_std_10,
                 'ts_std_20' : ts_std_20,
                 'ts_std_40' : ts_std_40,
                 # 33-44 
                 'ts_skew_10' : ts_skew_10,
                 'ts_skew_20' : ts_skew_20,
                 'ts_skew_40' : ts_skew_40,
                 'ts_kurt_10' : ts_kurt_10,
                 'ts_kurt_20' : ts_kurt_20,
                 'ts_kurt_40' : ts_kurt_40,
                 'ts_min_5' : ts_min_5,
                 'ts_min_10' : ts_min_10,
                 'ts_min_20' : ts_min_20,
                 'ts_max_5' : ts_max_5,
                 'ts_max_10' : ts_max_10,
                 'ts_max_20' : ts_max_20,
                 # 45-56
                 'ts_range_5' : ts_range_5,
                 'ts_range_10' : ts_range_10,
                 'ts_range_20' : ts_range_20,
                 'ts_argmin_5' : ts_argmin_5,
                 'ts_argmin_10' : ts_argmin_10,
                 'ts_argmin_20' : ts_argmin_20,
                 'ts_argmax_5' : ts_argmax_5,
                 'ts_argmax_10' : ts_argmax_10,
                 'ts_argmax_20' : ts_argmax_20,
                 'ts_argrange_5' : ts_argrange_5,
                 'ts_argrange_10' : ts_argrange_10,
                 'ts_argrange_20' : ts_argrange_20,
                 # 57-68
                 'ts_rank_5' : ts_rank_5,
                 'ts_rank_10' : ts_rank_10,
                 'ts_rank_20' : ts_rank_20,
                 'ts_mean_return_5' : ts_mean_return_5,
                 'ts_mean_return_10' : ts_mean_return_10,
                 'ts_mean_return_20' : ts_mean_return_20,
                 'ta_beta_5' : ta_beta_5,
                 'ta_beta_10' : ta_beta_10,
                 'ta_beta_20' : ta_beta_20,
                 'ta_lr_slope_5' : ta_lr_slope_5,
                 'ta_lr_slope_10' : ta_lr_slope_10,
                 'ta_lr_slope_20' : ta_lr_slope_20,
                 # 69-80
                 'ta_lr_intercept_5' : ta_lr_intercept_5,
                 'ta_lr_intercept_10' : ta_lr_intercept_10,
                 'ta_lr_intercept_20' : ta_lr_intercept_20,
                 'ta_lr_angle_5' : ta_lr_angle_5,
                 'ta_lr_angle_10' : ta_lr_angle_10,
                 'ta_lr_angle_20' : ta_lr_angle_20,
                 'ta_tsf_5' : ta_tsf_5,
                 'ta_tsf_10' : ta_tsf_10,
                 'ta_tsf_20' : ta_tsf_20,
                 'ta_ema_8' : ta_ema_8,
                 'ta_ema_21' : ta_ema_21,
                 'ta_ema_55' : ta_ema_55,
                 # 81-92
                 'ta_dema_8': ta_dema_8,
                 'ta_dema_21': ta_dema_21,
                 'ta_dema_55': ta_dema_55,
                 'ta_kama_8' : ta_kama_8,
                 'ta_kama_21' : ta_kama_21,
                 'ta_kama_55' : ta_kama_55,
                 'ta_tema_8' : ta_tema_8,
                 'ta_tema_21' : ta_tema_21,
                 'ta_tema_55' : ta_tema_55,
                 'ta_trima_8' : ta_trima_8,
                 'ta_trima_21' : ta_trima_21,
                 'ta_trima_55' : ta_trima_55,
                 # 93-105
                 'ta_rsi_6' : ta_rsi_6,
                 'ta_rsi_12' : ta_rsi_12,
                 'ta_rsi_24' : ta_rsi_24,
                 'ta_cmo_14' : ta_cmo_14,
                 'ta_cmo_25' : ta_cmo_25,
                 'ta_mom_12' : ta_mom_12,
                 'ta_mom_25' : ta_mom_25,
                #  'ta_roc_14' : ta_roc_14,
                #  'ta_roc_25' : ta_roc_25,
                 'ta_rocp_14' : ta_rocp_14,
                 'ta_rocp_25' : ta_rocp_25,
                 'ta_rocr_14' : ta_rocr_14,
                 'ta_rocr_25' : ta_rocr_25,
                 # 106-116
                 'ta_trix_8' : ta_trix_8,
                 'ta_trix_21' : ta_trix_21,
                 'ta_trix_55' : ta_trix_55,
                 'ta_adx_14' : ta_adx_14,
                 'ta_adx_25' : ta_adx_25,
                 'ta_adxr_14' : ta_adxr_14,
                 'ta_adxr_25' : ta_adxr_25,
                 'ta_aroonosc_14' : ta_aroonosc_14,
                 'ta_aroonosc_25' : ta_aroonosc_25,
                 'ta_cci_14' : ta_cci_14,
                 'ta_cci_25' : ta_cci_25,
                 # 117-120
                 'ta_dx_14' : ta_dx_14,
                 'ta_dx_25' : ta_dx_25,
                 'ta_mfi_14' : ta_mfi_14,
                 'ta_mfi_25' : ta_mfi_25,
                 'ta_minus_di_14' : ta_minus_di_14,
                 'ta_minus_di_25' : ta_minus_di_25,
                 'ta_minus_dm_14' : ta_minus_dm_14,
                 'ta_minus_dm_25' : ta_minus_dm_25,
                 'ta_willr_14' : ta_willr_14,
                 'ta_willr_25' : ta_willr_25,
                 'ta_atr_14' : ta_atr_14,
                 'ta_atr_25' : ta_atr_25,
                 'ta_natr_14' : ta_natr_14,
                 'ta_natr_25' : ta_natr_25,
                 # ==================== JZAL: add with gpquant: 33 ===============
                 'ts_mean2' : ts_mean2,
                 'ts_square1' : ts_square1,
                 'ts_cube1' : ts_cube1,
                 'ts_cbrt1' : ts_cbrt1,
                 'ts_sign1' : ts_sign1,
                 "ts_clear_by_cond3": ts_clear_by_cond3,
                 "ts_if_then_else3": ts_if_then_else3,
                 "ts_if_cond_then_else4": ts_if_cond_then_else4,
                 "ts_mean_5" : ts_mean_5,
                 "ts_mean_10" : ts_mean_10,
                 "ts_mean_20" : ts_mean_20,
                 "ts_mean_40" : ts_mean_40,
                 "ts_median_5" : ts_median_5,
                 "ts_median_10" : ts_median_10,
                 "ts_median_20" : ts_median_20,
                 "ts_midpoint_5" : ts_midpoint_5,
                 "ts_midpoint_10" : ts_midpoint_10,
                 "ts_midpoint_20" : ts_midpoint_20,
                 "ts_inverse_cv_10" : ts_inverse_cv_10,
                 "ts_inverse_cv_20" : ts_inverse_cv_20,
                 "ts_inverse_cv_40" : ts_inverse_cv_40,
                 "ts_autocorr_20_3" : ts_autocorr_20_3,
                 "ts_autocorr_40_3" : ts_autocorr_40_3,
                 "ts_autocorr_20_8" : ts_autocorr_20_8,
                 "ts_autocorr_40_8" : ts_autocorr_40_8,
                 "ts_autocorr_20_17" : ts_autocorr_20_17,
                 "ts_autocorr_40_17" : ts_autocorr_40_17,
                 "ts_maxmin_5" : ts_maxmin_5,
                 "ts_maxmin_10" : ts_maxmin_10,
                 "ts_maxmin_20" : ts_maxmin_20,
                 "ts_zscore_10" : ts_zscore_10,
                 "ts_zscore_20" : ts_zscore_20,
                 "ts_zscore_40" : ts_zscore_40,
                 # ==================== JZAL: end of gpquant: 33 ===============
                 # ================ JZAL: 常数(240722) ==============
                "ts_cons_n30"  : ts_cons_n30 ,
                "ts_cons_n10" : ts_cons_n10 ,
                "ts_cons_n5"   : ts_cons_n5  ,
                "ts_cons_n2"   : ts_cons_n2  ,
                "ts_cons_n1"   : ts_cons_n1  ,
                "ts_cons_n05"  : ts_cons_n05 ,
                "ts_cons_n001" : ts_cons_n001,
                "ts_cons_30"   : ts_cons_30  ,
                "ts_cons_10"   : ts_cons_10  ,
                "ts_cons_5"    : ts_cons_5   ,
                "ts_cons_2"    : ts_cons_2   ,
                "ts_cons_1"    : ts_cons_1   ,
                "ts_cons_05"   : ts_cons_05  ,
                "ts_cons_001"  : ts_cons_001 ,
                # ================ JZAL: 知守溪 - 240825 ==============
                # 144-147
                "ts_udd_10"    : ts_udd_10,
                "ts_udd_20"    : ts_udd_20,
                "ts_udd_40"    : ts_udd_40,
                "ts_udd_60"    : ts_udd_60,
                # 148-153
                "ts_rvi_7"     : ts_rvi_7,
                "ts_rvi_14"     : ts_rvi_14,
                "ts_rvi_21"     : ts_rvi_21,
                "ts_rvi_34"     : ts_rvi_34,
                "ts_rvi_55"     : ts_rvi_55,
                "ts_rvi_89"     : ts_rvi_89,
                # 154-158
                "ts_slope_pair_9"  : ts_slope_pair_9,
                "ts_slope_pair_18"  : ts_slope_pair_18,
                "ts_slope_pair_34"  : ts_slope_pair_34,
                "ts_slope_pair_55"  : ts_slope_pair_55,
                "ts_slope_pair_89"  : ts_slope_pair_89
                 }

if __name__ == '__main__':
    np.random.seed(42)
    # se_np = np.random.uniform(low=-1.1, high=55, size=(50000,1))
    se_np = np.random.uniform(low=-15, high=55, size=50000)
    se_np2 = np.random.uniform(low=-3.1, high=105, size=50000)
    res = _ts_neg(se_np)
    print(res)
    import matplotlib.pyplot as plt
    plt.hist(res, bins=50, color='blue', alpha=0.7)  # bins参数可以调整直方图的柱子数量
    plt.title('Histogram of Res Data Distribution')
    plt.xlabel('Value')
    plt.ylabel('Frequency')
    plt.grid(axis='y', alpha=0.75)
    # 显示图形
    plt.show()
    
