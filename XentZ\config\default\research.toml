# ============================== 命名约定 ============================== #
[label.single]
tdelay = 1
names = ['label_1']

[label.multiple]
tdelay = [1, 5, 10, 20]
names = ['label_1', 'label_5', 'label_10', 'label_20']

# ============================== 数据预处理 ============================== #
[missing]
mode = "ffill" # bfill, mean, median, mode, value, interpolate

[outliers]
mode = 'mad_clip'
mad_clip = {k = 3} #

[norm]
window = 2000 # [1000,2000]
logmode = 0 # [0,1,2]
# —— Norm 参数字典 —— #
[norm.linear] # 线性模型
X     = { algomode = 0, n_clip = 6, smooth = 0, demean = true }
label = { algomode = 0, n_clip = 2, smooth = 0, demean = false }
pos   = { algomode = 3, n_clip = 2, smooth = 1, demean = false }
[norm.robust] # 鲁棒模型, 不进行norm和正态处理, 如树模型
X     = 'skip'
label = 'skip'
pos   = { algomode = 3, n_clip = 2, smooth = 1, demean = false }
[norm.neutral] # 神经网络等算法, 进行norm和强制正态
X     = { algomode = 5, n_clip = 6, smooth = 0, demean = false }
label = { algomode = 5, n_clip = 2, smooth = 0, demean = false }
pos   = { algomode = 3, n_clip = 2, smooth = 1, demean = false }

[norm.cols]
base2keep = ['open','close','high','low']
base2norm = ['volume','amount']

[norm.cols.prefix]
feat2keep = '_ts_cons_'
featlabel = 'label_'

# ============================== 特征配置 ============================== #
[feat.norm]
model = 'robust' # 可选 "linear" 或 "robust" 或 "neutral"; 决定使用哪种模型算法
[feat.drop]
names = ['volume','amount']
[feat.selected]
names =  ['JZ007_9', 'b_vwap_512', 'avg_amount_1_avg_amount_5', 'JZ006_9', 'ROC60', 'JZ001_20_30', 'ROC5', 'JZ014_trends_mul3_21', 'MAX20', 'JZ009_14']

# ============================== 挖掘配置 ============================== #
[mine.norm]
model = 'linear' # 挖掘metric用线性模型

[mine.run]
mode = 'test' # test, live
nextbar = true # 是否延迟到下一bar开盘open, 否则用close
jobnum = -1 # 挖掘时, 用-1表示使用所有可用核心
runnum = 3 # 每个目标挖掘3轮，利用随机性
fee = 0.002 # 千2
free = 0.03 # 年化3%
[mine.run.perf]
daybars = 1 # 表示用的日线
anndays = 252

[mine.filter]
skewthresh = 0.5
kurtthresh = 5
corrthresh = 0.3
metric2use = 'sic' # 'sic', 'sr'

[mine.filter.sr]
srthresh = 0.8
pjsrthresh = 0.2

[mine.filter.rankic]
tdelay = 1 # 挖掘后筛选时, 用t期收益率算ic
window = 240 # 滚动窗口天数
minperiod = 120 # 最小所需样本数
toppct = 0.2 # 前20%
pjpct = 0.2 # 剩余前20%