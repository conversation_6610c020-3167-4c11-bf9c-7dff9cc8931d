#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FactorZoo 数据库连接测试脚本
验证数据库创建和基本功能
"""

import sqlite3
import json
from pathlib import Path
from datetime import datetime

# 数据库路径配置
FACTOR_ZOO_DIR = Path("D:/myquant/FZoo/")
FACTOR_ZOO_DB = FACTOR_ZOO_DIR / "database" / "factorzoo.sqlite"

def test_database_connection():
    """测试数据库连接"""
    print("=" * 60)
    print("测试 FactorZoo 数据库连接")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect(str(FACTOR_ZOO_DB))
        cursor = conn.cursor()
        
        # 检查数据库版本
        cursor.execute("SELECT tag_value FROM factor_tags WHERE factor_id='SYSTEM' AND tag_name='schema_version'")
        version = cursor.fetchone()
        print(f"✅ 数据库连接成功！")
        print(f"📋 Schema 版本: {version[0] if version else '未知'}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def show_database_summary():
    """显示数据库概要信息"""
    print("\n" + "=" * 60)
    print("数据库结构概要")
    print("=" * 60)
    
    conn = sqlite3.connect(str(FACTOR_ZOO_DB))
    cursor = conn.cursor()
    
    # 表统计
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    print(f"📊 数据表数量: {len(tables)}")
    
    for table in tables:
        table_name = table[0]
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        print(f"   - {table_name}: {count} 条记录")
    
    # 视图统计
    cursor.execute("SELECT name FROM sqlite_master WHERE type='view'")
    views = cursor.fetchall()
    print(f"\n🔍 视图数量: {len(views)}")
    for view in views:
        print(f"   - {view[0]}")
    
    # 索引统计
    cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND sql IS NOT NULL")
    indexes = cursor.fetchall()
    print(f"\n🗂️ 索引数量: {len(indexes)}")
    
    conn.close()

def show_categories():
    """显示因子分类体系"""
    print("\n" + "=" * 60)
    print("因子分类体系")
    print("=" * 60)
    
    conn = sqlite3.connect(str(FACTOR_ZOO_DB))
    cursor = conn.cursor()
    
    # 主分类
    cursor.execute("""
        SELECT category_id, category_name, description 
        FROM factor_categories 
        WHERE category_level = 1 
        ORDER BY sort_order
    """)
    main_categories = cursor.fetchall()
    
    print("🏷️ 主分类:")
    for cat_id, cat_name, desc in main_categories:
        print(f"   {cat_id}: {cat_name} - {desc}")
        
        # 子分类
        cursor.execute("""
            SELECT category_id, category_name, description 
            FROM factor_categories 
            WHERE parent_category = ? AND category_level = 2 
            ORDER BY sort_order
        """, (cat_id,))
        sub_categories = cursor.fetchall()
        
        for sub_id, sub_name, sub_desc in sub_categories:
            print(f"     └── {sub_id}: {sub_name}")
    
    conn.close()

def show_universes():
    """显示股票池信息"""
    print("\n" + "=" * 60)
    print("预置股票池")
    print("=" * 60)
    
    conn = sqlite3.connect(str(FACTOR_ZOO_DB))
    cursor = conn.cursor()
    
    cursor.execute("""
        SELECT universe_id, universe_name, universe_type, description 
        FROM universes 
        WHERE is_current = 1 
        ORDER BY universe_id
    """)
    universes = cursor.fetchall()
    
    print("🌍 股票池列表:")
    for univ_id, univ_name, univ_type, desc in universes:
        print(f"   {univ_id}: {univ_name} ({univ_type}) - {desc}")
    
    conn.close()

def test_basic_operations():
    """测试基本的CRUD操作"""
    print("\n" + "=" * 60)
    print("测试基本操作")
    print("=" * 60)
    
    conn = sqlite3.connect(str(FACTOR_ZOO_DB))
    cursor = conn.cursor()
    
    try:
        # 1. 创建测试批次
        test_batch_id = f"TEST_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        cursor.execute("""
            INSERT INTO factor_batches (
                batch_id, batch_name, creation_tool, total_generated,
                created_at
            ) VALUES (?, ?, ?, ?, ?)
        """, (test_batch_id, "测试批次", "manual", 1, datetime.now()))
        
        print(f"✅ 创建测试批次: {test_batch_id}")
        
        # 2. 创建测试因子
        test_factor_id = f"F_TEST_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        cursor.execute("""
            INSERT INTO factors (
                factor_id, factor_name, factor_expression, factor_type,
                data_source_type, symbols, frequencies, date_ranges,
                creation_method, batch_id, creation_date, primary_category
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            test_factor_id, "测试因子", "close / ts_mean(close, 20) - 1", 
            "time_series", "single_symbol", '["510050.SH"]', '["1d"]',
            '{"start": "2020-01-01", "end": "2024-12-01"}',
            "manual_creation", test_batch_id, datetime.now().date(), "PRICE_VOLUME"
        ))
        
        print(f"✅ 创建测试因子: {test_factor_id}")
        
        # 3. 查询测试
        cursor.execute("""
            SELECT f.factor_id, f.factor_name, c.category_name
            FROM factors f
            LEFT JOIN factor_categories c ON f.primary_category = c.category_id
            WHERE f.factor_id = ?
        """, (test_factor_id,))
        
        result = cursor.fetchone()
        if result:
            print(f"✅ 查询测试成功: {result[0]} - {result[1]} ({result[2]})")
        
        # 4. 清理测试数据
        cursor.execute("DELETE FROM factors WHERE factor_id = ?", (test_factor_id,))
        cursor.execute("DELETE FROM factor_batches WHERE batch_id = ?", (test_batch_id,))
        
        conn.commit()
        print("✅ 测试数据清理完成")
        
    except Exception as e:
        print(f"❌ 操作测试失败: {e}")
        conn.rollback()
    
    finally:
        conn.close()

def main():
    """主函数"""
    print("🚀 FactorZoo 数据库测试")
    print(f"📍 数据库路径: {FACTOR_ZOO_DB}")
    
    # 检查数据库文件是否存在
    if not FACTOR_ZOO_DB.exists():
        print(f"❌ 数据库文件不存在: {FACTOR_ZOO_DB}")
        return
    
    # 执行测试
    if test_database_connection():
        show_database_summary()
        show_categories()
        show_universes()
        test_basic_operations()
        
        print("\n" + "=" * 60)
        print("🎉 FactorZoo 数据库测试完成！")
        print("数据库已成功创建并可以正常使用。")
        print("=" * 60)

if __name__ == "__main__":
    main() 