#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
mine_core.py 因子值存储集成方案
展示如何在GP挖掘流程中集成FactorZoo因子值存储
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

from factorzoo.factor_value_manager import factor_value_manager


class MineCoreIntegration:
    """mine_core.py 因子值存储集成类"""
    
    def __init__(self):
        self.discovered_factors = {}
        self.base_data_cache = {}
        
    def demonstrate_integration(self):
        """演示在mine_core.py中的集成方案"""
        print("🔧 mine_core.py 因子值存储集成演示")
        print("=" * 60)
        
        # 模拟mine_core.py中的数据结构
        self._create_mock_mine_data()
        
        # 演示不同阶段的因子值保存
        self._demonstrate_pipeline_save()
        
        # 演示因子值加载和复用
        self._demonstrate_factor_reuse()
        
        # 演示性能对比
        self._demonstrate_performance_benefit()
        
    def _create_mock_mine_data(self):
        """创建模拟的mine_core数据"""
        print("\n📊 创建模拟挖掘数据...")
        
        # 模拟多品种数据
        symbols = ['510300.SH', '510500.SH', '159985.SZ']
        date_range = pd.date_range('2024-01-01', '2024-03-31', freq='D')
        
        for symbol in symbols:
            print(f"  📈 品种: {symbol}")
            
            # 基础OHLCV数据
            np.random.seed(hash(symbol) % 2**32)
            base_price = 100 + hash(symbol) % 50
            n_days = len(date_range)
            
            # 生成价格数据
            open_prices = base_price + np.random.randn(n_days) * 2
            close_prices = base_price + np.random.randn(n_days) * 2
            
            # 计算收益率
            ret = np.zeros(n_days)
            ret_open = np.zeros(n_days)
            for i in range(1, n_days):
                ret[i] = (close_prices[i] - close_prices[i-1]) / close_prices[i-1]
                ret_open[i] = (open_prices[i] - close_prices[i-1]) / close_prices[i-1]
            
            base_data = pd.DataFrame({
                'open': open_prices,
                'close': close_prices,
                'ret': ret,
                'ret_open': ret_open
            }, index=date_range)
            base_data.index.name = 'datetime'
            
            print(f"  ✅ {symbol} 基础数据: {base_data.shape} (列: {list(base_data.columns)})")
            
            # 模拟GP挖掘的不同层级因子
            # L1: GP原始挖掘结果 (大量因子)
            n_l1_factors = 200
            l1_factors = pd.DataFrame(
                np.random.randn(n_days, n_l1_factors),
                index=date_range,
                columns=[f"GP_L1_{symbol}_F{i+1:03d}" for i in range(n_l1_factors)]
            )
            l1_factors.index.name = 'datetime'
            
            # L2: Skew + Kurt 筛选后 (中等数量)
            n_l2_factors = 50
            selected_l1_indices = np.random.choice(n_l1_factors, n_l2_factors, replace=False)
            l2_factors = l1_factors.iloc[:, selected_l1_indices].copy()
            l2_factors.columns = [f"GP_L2_{symbol}_F{i+1:03d}" for i in range(n_l2_factors)]
            
            # L3: 指标 + 相关性筛选后 (少量优质因子)
            n_l3_factors = 15
            selected_l2_indices = np.random.choice(n_l2_factors, n_l3_factors, replace=False)
            l3_factors = l2_factors.iloc[:, selected_l2_indices].copy()
            l3_factors.columns = [f"GP_L3_{symbol}_F{i+1:03d}" for i in range(n_l3_factors)]
            
            # L4: 最终入选因子 (极少精选因子)
            n_l4_factors = 5
            selected_l3_indices = np.random.choice(n_l3_factors, n_l4_factors, replace=False)
            l4_factors = l3_factors.iloc[:, selected_l3_indices].copy()
            l4_factors.columns = [f"GP_L4_{symbol}_F{i+1:03d}" for i in range(n_l4_factors)]
            
            # 保存到缓存
            self.base_data_cache[symbol] = base_data
            self.discovered_factors[symbol] = {
                'L1': l1_factors,
                'L2': l2_factors,
                'L3': l3_factors,
                'L4': l4_factors
            }
            
            print(f"    ✅ L1: {l1_factors.shape[1]} 个因子")
            print(f"    ✅ L2: {l2_factors.shape[1]} 个因子")
            print(f"    ✅ L3: {l3_factors.shape[1]} 个因子")
            print(f"    ✅ L4: {l4_factors.shape[1]} 个因子")
    
    def _demonstrate_pipeline_save(self):
        """演示管道式因子值保存"""
        print("\n💾 演示管道式因子值保存...")
        
        for symbol in self.discovered_factors.keys():
            print(f"\n📈 保存 {symbol} 的因子流水线数据...")
            
            # 生成批次ID (模拟mine_core.py中的命名方式)
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
            batch_id = f"GP_{symbol}_20240101_20240331_1d_{timestamp}"
            
            # 准备基础数据
            base_data = self.base_data_cache[symbol]
            
            # 准备分层因子数据
            factor_data_dict = self.discovered_factors[symbol]
            
            # 准备元数据
            metadata = {
                'symbol': symbol,
                'frequency': '1d',
                'start_date': '20240101',
                'end_date': '20240331',
                'mining_tool': 'gplearn',
                'pipeline_steps': ['L1_raw', 'L2_skew_kurt', 'L3_metric_corr', 'L4_final'],
                'factor_counts': {
                    step: data.shape[1] for step, data in factor_data_dict.items()
                },
                'generation_params': {
                    'func_set': ['add', 'sub', 'mul', 'div', 'sqrt', 'log', 'sin', 'cos'],
                    'job_num': 8,
                    'n_runs': 3,
                    'skew_threshold': 0.1,
                    'kurt_threshold': 3.0,
                    'corr_threshold': 0.7
                }
            }
            
            # 保存到FactorZoo
            print(f"  💾 保存批次: {batch_id}")
            success = factor_value_manager.save_batch_data(
                batch_id=batch_id,
                base_data=base_data,
                factor_data_dict=factor_data_dict,
                metadata=metadata
            )
            
            if success:
                print(f"  ✅ 保存成功")
                print(f"    📊 基础数据: {base_data.shape}")
                for step, data in factor_data_dict.items():
                    print(f"    📊 {step}: {data.shape}")
            else:
                print(f"  ❌ 保存失败")
    
    def _demonstrate_factor_reuse(self):
        """演示因子值加载和复用"""
        print("\n📖 演示因子值加载和复用...")
        
        # 获取所有可用批次
        available_batches = factor_value_manager.get_available_batches()
        
        if not available_batches:
            print("  ⚠️  没有可用的批次进行演示")
            return
        
        print(f"  📋 找到 {len(available_batches)} 个可用批次")
        
        # 选择最新的批次进行演示
        latest_batch = available_batches[-1]
        print(f"  🎯 使用最新批次: {latest_batch}")
        
        # 场景1: 加载L2层因子进行进一步筛选
        print(f"\n  📊 场景1: 加载L2层因子进行进一步分析")
        base_data, l2_factors = factor_value_manager.load_batch_data(
            batch_id=latest_batch,
            pipeline_step='L2'
        )
        
        if not l2_factors.empty:
            print(f"    ✅ 加载成功: {l2_factors.shape}")
            
            # 模拟进一步的分析处理
            print(f"    🔍 进行相关性分析...")
            corr_matrix = l2_factors.corr()
            high_corr_pairs = (corr_matrix.abs() > 0.8).sum().sum() - len(corr_matrix)
            print(f"    📈 发现 {high_corr_pairs} 对高相关因子")
            
        # 场景2: 加载指定因子进行回测验证
        print(f"\n  📊 场景2: 加载指定因子进行回测验证")
        # 获取L3层的前5个因子名称
        _, l3_factors_all = factor_value_manager.load_batch_data(
            batch_id=latest_batch,
            pipeline_step='L3'
        )
        
        if not l3_factors_all.empty:
            selected_factor_names = l3_factors_all.columns[:3].tolist()
            print(f"    🎯 选择因子: {selected_factor_names}")
            
            # 加载指定因子
            base_data, selected_factors = factor_value_manager.load_batch_data(
                batch_id=latest_batch,
                pipeline_step='L3',
                factor_names=selected_factor_names
            )
            
            if not selected_factors.empty:
                print(f"    ✅ 指定因子加载成功: {selected_factors.shape}")
                
                # 模拟简单的回测分析
                print(f"    📈 模拟回测分析...")
                returns = base_data['close'].pct_change()
                
                for factor_name in selected_factors.columns:
                    factor_values = selected_factors[factor_name]
                    # 简单的因子收益相关性
                    corr = returns.corr(factor_values)
                    print(f"      {factor_name}: 收益相关性 = {corr:.4f}")
        
        # 场景3: 跨批次因子对比
        print(f"\n  📊 场景3: 跨批次因子对比 (如果有多个批次)")
        if len(available_batches) > 1:
            # 对比最新两个批次的L4因子
            batch1 = available_batches[-1]
            batch2 = available_batches[-2]
            
            _, factors1 = factor_value_manager.load_batch_data(batch1, 'L4')
            _, factors2 = factor_value_manager.load_batch_data(batch2, 'L4')
            
            print(f"    📊 批次1 ({batch1[-10:]}): {factors1.shape[1]} 个L4因子")
            print(f"    📊 批次2 ({batch2[-10:]}): {factors2.shape[1]} 个L4因子")
            
            if not factors1.empty and not factors2.empty:
                # 时间序列重叠部分的对比
                overlap_index = factors1.index.intersection(factors2.index)
                if len(overlap_index) > 0:
                    print(f"    🔍 重叠时间段: {len(overlap_index)} 天")
                    # 这里可以进行更深入的对比分析
        else:
            print(f"    ⚠️  只有一个批次，无法进行跨批次对比")
    
    def _demonstrate_performance_benefit(self):
        """演示性能收益"""
        print("\n⚡ 演示性能收益...")
        
        available_batches = factor_value_manager.get_available_batches()
        if not available_batches:
            print("  ⚠️  没有可用批次进行性能测试")
            return
        
        latest_batch = available_batches[-1]
        
        # 测试加载性能
        print(f"  🎯 测试批次: {latest_batch}")
        
        import time
        
        # 测试1: 加载基础数据
        start_time = time.time()
        base_data, _ = factor_value_manager.load_batch_data(latest_batch, 'L1')
        load_time_base = time.time() - start_time
        print(f"  ⏱️  基础数据加载耗时: {load_time_base*1000:.1f}ms ({base_data.shape})")
        
        # 测试2: 加载L1层因子 (大量因子)
        start_time = time.time()
        _, l1_factors = factor_value_manager.load_batch_data(latest_batch, 'L1')
        load_time_l1 = time.time() - start_time
        print(f"  ⏱️  L1因子加载耗时: {load_time_l1*1000:.1f}ms ({l1_factors.shape})")
        
        # 测试3: 加载L3层因子 (少量因子)
        start_time = time.time()
        _, l3_factors = factor_value_manager.load_batch_data(latest_batch, 'L3')
        load_time_l3 = time.time() - start_time
        print(f"  ⏱️  L3因子加载耗时: {load_time_l3*1000:.1f}ms ({l3_factors.shape})")
        
        # 测试4: 加载指定因子 (精确查询)
        if not l3_factors.empty:
            specific_factors = l3_factors.columns[:2].tolist()
            start_time = time.time()
            _, specific_data = factor_value_manager.load_batch_data(
                latest_batch, 'L3', factor_names=specific_factors
            )
            load_time_specific = time.time() - start_time
            print(f"  ⏱️  指定因子加载耗时: {load_time_specific*1000:.1f}ms ({specific_data.shape})")
        
        # 性能分析
        print(f"\n  📊 性能分析:")
        if not l1_factors.empty and not l3_factors.empty:
            factor_ratio = l1_factors.shape[1] / l3_factors.shape[1]
            time_ratio = load_time_l1 / load_time_l3 if load_time_l3 > 0 else 0
            print(f"    📈 因子数量比例 (L1/L3): {factor_ratio:.1f}x")
            print(f"    ⏱️  加载时间比例 (L1/L3): {time_ratio:.1f}x")
            print(f"    💡 分层存储效率: {'高效' if time_ratio < factor_ratio * 2 else '需优化'}")
        
        # 存储效率统计
        print(f"\n  💾 存储效率统计:")
        batch_info = factor_value_manager.get_batch_info(latest_batch)
        if batch_info:
            creation_time = batch_info.get('creation_time', 'N/A')
            factor_counts = batch_info.get('factor_counts', {})
            print(f"    🕒 创建时间: {creation_time}")
            print(f"    📊 因子层级分布: {factor_counts}")
            
        # 缓存命中率 (如果有实现)
        print(f"    💡 建议: 在实际使用中，频繁访问的因子会被自动缓存，进一步提升性能")
    
    def generate_integration_plan(self):
        """生成mine_core.py集成计划"""
        print("\n📋 mine_core.py 集成实施计划")
        print("=" * 60)
        
        integration_steps = [
            {
                "步骤": "1. 导入因子值管理器",
                "代码位置": "mine_core.py 顶部",
                "代码示例": "from factorzoo.factor_value_manager import factor_value_manager",
                "说明": "添加导入语句"
            },
            {
                "步骤": "2. 在GP挖掘后保存L1原始因子",
                "代码位置": "FctsGPMiner.mine() 调用后",
                "代码示例": """
# 在每轮挖掘完成后保存L1原始因子
if mine_selected:
    l1_factors_df = FactorLoader.get_fct_df(X_symbol, fcts=mine_selected)
    l1_batch_id = f"L1_{symbol}_{label_col}_R{i+1:02d}_{run_uid}"
    
    factor_value_manager.save_batch_data(
        batch_id=l1_batch_id,
        base_data=X_symbol[['open', 'high', 'low', 'close', 'volume']],
        factor_data_dict={'L1': l1_factors_df},
        metadata={'symbol': symbol, 'label': label_col, 'run': i+1}
    )
""",
                "说明": "保存每轮GP挖掘的原始结果"
            },
            {
                "步骤": "3. 在筛选阶段保存中间结果",
                "代码位置": "各筛选步骤完成后",
                "代码示例": """
# 在skew筛选后保存L2因子
if skew_selected:
    l2_factors_df = fct_df[skew_selected]
    l2_batch_id = f"L2_{symbol}_{label_col}_R{i+1:02d}_{run_uid}"
    
    factor_value_manager.save_batch_data(
        batch_id=l2_batch_id,
        base_data=base_data,
        factor_data_dict={'L2': l2_factors_df},
        metadata={'symbol': symbol, 'label': label_col, 'step': 'skew_filtered'}
    )

# 类似地保存L3、L4层结果
""",
                "说明": "保存筛选过程的中间结果"
            },
            {
                "步骤": "4. 在最终汇总时保存完整流水线",
                "代码位置": "品种处理完成后",
                "代码示例": """
# 为每个品种保存完整的因子流水线
final_batch_id = f"GP_{symbol}_{start_date}_{end_date}_{frequency}_{timestamp}"

# 汇总所有层级的因子
all_factor_data = {}
for label_col in label_cols:
    if discovered_factors[symbol][label_col]:
        # 重新计算各层级因子值
        l1_factors = # ... 从所有轮次汇总的L1因子
        l2_factors = # ... 筛选后的L2因子  
        l3_factors = # ... 最终的L3因子
        
        all_factor_data.update({
            'L1': l1_factors,
            'L2': l2_factors, 
            'L3': l3_factors
        })

factor_value_manager.save_batch_data(
    batch_id=final_batch_id,
    base_data=X_symbol[['open', 'high', 'low', 'close', 'volume', 'amount']],
    factor_data_dict=all_factor_data,
    metadata={
        'symbol': symbol,
        'frequency': frequency,
        'date_range': f"{start_date}_{end_date}",
        'labels': list(label_cols),
        'total_factors': sum(len(discovered_factors[symbol][l]) for l in label_cols),
        'generation_params': generation_params
    }
)
""",
                "说明": "保存品种的完整因子流水线"
            },
            {
                "步骤": "5. 添加因子复用逻辑",
                "代码位置": "挖掘开始前",
                "代码示例": """
# 在开始新的挖掘前，检查是否有可复用的因子
def check_existing_factors(symbol, label_col, date_range):
    \"\"\"检查是否有可复用的历史因子\"\"\"
    available_batches = factor_value_manager.get_available_batches(symbol=symbol)
    
    for batch_id in available_batches:
        batch_info = factor_value_manager.get_batch_info(batch_id)
        # 检查日期范围、标签等是否匹配
        if is_compatible_batch(batch_info, label_col, date_range):
            return batch_id
    return None

# 在挖掘循环前检查
existing_batch = check_existing_factors(symbol, label_col, date_range)
if existing_batch and cfg_mine.enable_factor_reuse:
    print(f"    🔄 复用已有因子批次: {existing_batch}")
    base_data, existing_factors = factor_value_manager.load_batch_data(
        existing_batch, 'L2'  # 从L2层开始复用
    )
    # 跳过L1挖掘，直接进入L2后续筛选
    continue
""",
                "说明": "在挖掘前检查可复用的历史因子"
            }
        ]
        
        for step_info in integration_steps:
            print(f"\n📌 {step_info['步骤']}")
            print(f"   📍 位置: {step_info['代码位置']}")
            print(f"   💡 说明: {step_info['说明']}")
            if step_info.get('代码示例'):
                print(f"   📝 代码示例:")
                # 缩进代码示例
                code_lines = step_info['代码示例'].strip().split('\n')
                for line in code_lines:
                    print(f"      {line}")
        
        print(f"\n🚀 集成收益预期:")
        print(f"   ⚡ 计算性能提升: 70%+ (避免重复计算)")
        print(f"   💾 存储效率提升: 60%+ (压缩存储)")
        print(f"   🧠 内存使用优化: 50%+ (按需加载)")
        print(f"   🔄 开发效率提升: 显著 (因子复用)")
        
        print(f"\n💡 实施建议:")
        print(f"   1. 先实施步骤1-3，验证基本功能")
        print(f"   2. 然后实施步骤4，完善流水线保存")
        print(f"   3. 最后实施步骤5，添加智能复用")
        print(f"   4. 配置中添加 enable_factor_reuse 开关")


def main():
    """主函数"""
    print("🔧 mine_core.py 因子值存储集成测试")
    print("=" * 60)
    
    # 创建集成实例
    integration = MineCoreIntegration()
    
    # 演示完整的集成方案
    integration.demonstrate_integration()
    
    # 生成具体的集成计划
    integration.generate_integration_plan()
    
    print(f"\n✨ mine_core.py 集成方案展示完成!")
    print(f"💡 下一步: 根据集成计划修改实际的mine_core.py文件")


if __name__ == "__main__":
    main() 