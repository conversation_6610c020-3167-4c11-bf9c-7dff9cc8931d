# Sprint #1: ETF交易数量优化 - 新增ETF资金管理策略

## 需求描述
有些ETF价格非常低(如价格<1元), 在回测时容易超过股票最大买入数量限制(默认100万股)。
需要新增专门的ETF资金管理策略类，在买入数量超过限制时允许取最大可交易数量继续执行。

## 实现思路
还是用wrapper的方式, 传入参数只是max_trade_number和warning_on_overflow, 提供3种能力扩展:
1. max_trade_number: 自定义最大交易数量，如果超过限制，调整为最大可交易数量; 注意是wrapper方式, 最终生效的是MM_Fixed_Percent
2. warning_on_overflow: 是否在超过限制时输出警告信息

## 技术方案

### 1. 目录结构
```
mystar/
├── part/
│   └── mm/           # 资金管理策略目录
│       └── etf.py    # ETF专用资金管理策略
└── tests/
    └── test_mm_etf.py  # 单元测试
```

### 2. 功能实现
在 `mystar/part/mm/etf.py` 中实现：

```python
#!/usr/bin/python
# -*- coding: utf-8 -*-

from hikyuu.trade_sys.moneymanager import MoneyManagerBase
from hikyuu import Datetime, Stock, MARKETID

class MM_ETF(MoneyManagerBase):
    """ETF专用资金管理策略，处理ETF交易数量超限问题
    
    :param MoneyManager base_mm: 基础资金管理策略
    :param int max_trade_number: 最大交易数量，默认None表示使用股票默认值
    :param bool warning_on_overflow: 是否在超过限制时输出警告信息，默认True
    """
    
    def __init__(self, base_mm, max_trade_number=None, warning_on_overflow=True):
        super(MM_ETF, self).__init__("MM_ETF")
        self.base_mm = base_mm
        self.max_trade_number = max_trade_number
        self.warning_on_overflow = warning_on_overflow
        
    def _getBuyNumber(self, datetime, stock, price, risk):
        """获取指定交易对象可买入的数量"""
        # 先获取基础策略计算的数量
        number = self.base_mm.getBuyNumber(datetime, stock, price, risk)
        
        # 获取最大可交易数量
        max_num = self.max_trade_number if self.max_trade_number else stock.maxTradeNumber
        
        # 如果超过限制，调整为最大可交易数量
        if number > max_num:
            if self.warning_on_overflow:
                print(f"[WARNING] {stock.market_code} 买入数量 {number} 超过限制，已调整为 {max_num}")
            return max_num
            
        return number
        
    def _getSellNumber(self, datetime, stock, price, risk):
        """获取指定交易对象可卖出的数量"""
        # 卖出数量直接使用基础策略的结果
        return self.base_mm.getSellNumber(datetime, stock, price, risk)

    def clone(self):
        """克隆操作"""
        return MM_ETF(
            self.base_mm.clone(),
            self.max_trade_number,
            self.warning_on_overflow
        )
```

### 3. 使用示例
在策略中使用：

```python
from hikyuu import *
from mystar.part.mm.etf import MM_ETF

# 创建基础策略
base_mm = MM_FixedPercent(0.1)  # 使用固定比例策略

# 创建ETF专用策略
etf_mm = MM_ETF(
    base_mm=base_mm,
    max_trade_number=2000000,  # 可选：自定义最大交易数量
    warning_on_overflow=True    # 可选：是否显示警告信息
)

# 在交易系统中使用
sys = SYS_Simple(
    tm=TM_Nothing(), 
    sg=SG_Cross(fast=5, slow=10),
    mm=etf_mm,
    pg=PG_NoLimit()
)

# 运行回测
sm = StockManager.instance()
stk = sm.getStock("sz159915")  # 创业板ETF
sys.run(stk, Query(-100))
```

### 4. 测试用例
在 `tests/test_mm_etf.py` 中实现：

```python
import unittest
from hikyuu import *
from mystar.part.mm.etf import MM_ETF

class TestMMETF(unittest.TestCase):
    def setUp(self):
        self.base_mm = MM_FixedPercent(0.1)
        self.etf_mm = MM_ETF(self.base_mm)
        self.stock = sm.getStock("sz159915")
        
    def test_basic_functionality(self):
        """测试基本功能"""
        datetime = Datetime.now()
        price = 1.0
        risk = 0.0
        
        # 测试正常情况
        number = self.etf_mm.getBuyNumber(datetime, self.stock, price, risk)
        self.assertLessEqual(number, self.stock.maxTradeNumber)
        
    def test_custom_max_number(self):
        """测试自定义最大交易数量"""
        max_num = 2000000
        mm = MM_ETF(self.base_mm, max_trade_number=max_num)
        number = mm.getBuyNumber(Datetime.now(), self.stock, 1.0, 0.0)
        self.assertLessEqual(number, max_num)
        
    def test_warning_message(self):
        """测试警告信息"""
        # 这里需要捕获打印输出进行验证
        pass
```

## 验收标准
1. MM_ETF 类实现完整且测试通过
2. 在实际ETF策略中验证可用性
3. 提供完整的单元测试
4. 代码风格符合 Python 规范

## 后续规划
1. 考虑支持更多ETF特有的交易规则
2. 考虑根据ETF成交量来动态调整最大交易数量
3. 可以考虑添加其他ETF特定的资金管理策略

## 技术债务
无，纯 Python 实现，不涉及框架修改