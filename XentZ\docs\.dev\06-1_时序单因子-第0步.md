---
**第零步：样本内（IS）动态稳健性检验 (WFA) - 工程设计**
---

此部分详细阐述了将WFA（Walk-Forward Analysis）方法论，即 `docs/.dev/06_时序单因子质检.md` 中定义的"第零步"，转化为稳健、可维护、可扩展的工程代码所需遵循的结构和规范。

### **1. 核心设计哲学：库与脚本分离**

为了保证项目的长期健康，我们必须严格区分两类代码：

*   **核心库 (Library/Framework)**:
    *   **位置**: `core/`, `factor/`, `datafeed/` 等目录。
    *   **职责**: 提供稳定、可复用、与具体业务逻辑解耦的类和函数（例如：绩效计算、滚动执行器、数据加载）。
    *   **规范**: 采用 **英文** 命名，遵循 **PEP-8** 规范，提供清晰的API接口。它们是项目的"引擎"。
    *   **调用方式**: 被"应用脚本"导入和调用，自身不作为执行入口。

*   **应用脚本 (Application/Scripts)**:
    *   **位置**: `script/投研_因子挖掘集成/`
    *   **职责**: 作为具体任务的执行入口，负责解析配置、编排任务流程、调用核心库、处理输入输出。它们是项目的"驾驶舱"。
    *   **规范**: 命名可灵活，建议使用中文（如 `V0_WFA动态稳健性检验.py`），以清晰反映工作流为首要目标。
    *   **调用方式**: 通过命令行直接执行。

### **2. 文件与目录结构规划**

为实现"第零步：WFA动态稳健性检验"，项目需包含下列关键文件：

```plaintext
XentZ/
├── config/
│   └── tasks/
│       └── wfa_validation.toml
├── factor/
│   └── validation_utils.py
├── script/
│   └── 投研_因子挖掘集成/
│       └── V0_WFA动态稳健性检验.py
├── REPORTS/
│   └── wfa_results/
│       └── .gitkeep
```

### **3. 各模块职责详解**

*   **`config/tasks/wfa_validation.toml`**:
    *   **定位**: WFA任务的 **任务参数** 文件。
    *   **内容**: 定义`symbol`, `is_start_date`, `is_end_date`, WFA窗口参数 (`training_window`, `testing_window`, `step_size`)，以及通过标准 (`pass_criteria`)。

*   **`script/投研_因子挖掘集成/V0_WFA动态稳健性检验.py`**:
    *   **定位**: WFA流程的 **总控制器**。
    *   **职责**:
        1.  接收 `wfa_validation.toml` 路径作为输入。
        2.  调用 `factorzoo.connector`，查询需要进行WFA验证的因子（例如，状态为 `L2_PASSED`）。
        3.  对每个因子，调用 `factor.validation_utils.run_wfa()` 执行完整的滚动分析。
        4.  根据返回的WFA整体性能和 `pass_criteria`，判断该因子是否通过。
        5.  调用 `factorzoo.connector`，将通过的因子状态更新为 `WFA_PASSED`，未通过的更新为 `WFA_FAILED`。
        6.  将详细的WFA报告（净值曲线、逐周期性能）保存到 `REPORTS/wfa_results/`。

*   **`factor/validation_utils.py` (新增 `run_wfa` 函数)**:
    *   **定位**: **可复用的WFA算法核心**。
    *   **`run_wfa(factor_series, price_series, wfa_params)`**:
        1.  接收一个因子的时间序列、价格序列和WFA参数字典。
        2.  实现 `docs/.dev/06_时序单因子质检.md` 中描述的滚动执行算法：循环切分训练/测试集 -> 在训练集上学习分布和方向 -> 在测试集上映射仓位并计算PnL。
        3.  拼接所有测试期的PnL，计算整体绩效指标（Sharpe, MDD等）。
        4.  返回包含整体绩效和详细报告数据的字典。

### **4. 配置管理策略：分离"应用配置"与"任务参数"**

本任务严格遵循此策略，执行脚本 (`V0_WFA动态稳健性检验.py`) 通过命令行接收一个位于 `config/tasks/` 下的 `.toml` 配置文件路径，并为其创建一个独立的 `Dynaconf` 实例作为任务参数，与全局配置分离。

**实施方案:**

*   **`script/.../V0_WFA动态稳健性检验.py` (执行脚本) 的逻辑**:
    *   脚本将通过命令行参数接收任务配置文件的路径。
        ```bash
        python -m script.投研_因子挖掘集成.V0_WFA动态稳健性检验 --config config/tasks/wfa_validation.toml
        ```
    *   脚本内部将可以访问两个配置对象：
        1.  从 `config.settings` 导入的全局 `settings` 对象，用于获取数据库连接等应用配置。
        2.  一个新建的 `Dynaconf` 实例 `task_params`，专门用于加载命令行传入的 `.toml` 文件，获取WFA的特定参数。

### **5. `run_wfa` 算法流程**

下列伪代码展示了 `factor.validation_utils.run_wfa` 的核心逻辑，便于在任何 Python 环境中快速落地：

```python
from typing import Dict, Tuple
import pandas as pd

def run_wfa(factor: pd.Series, price: pd.Series, params: Dict) -> Dict:
    """执行 Walk-Forward Analysis 并返回绩效摘要。"""
    tr_win = params["training_window"]
    te_win = params["testing_window"]
    step   = params.get("step_size", te_win)
    k      = params.get("tanh_k", 5)
    hold_n = params.get("hold_n", 1)  # 未来收益期数

    pnl_segments, metrics = [], []
    cursor = 0
    while cursor + tr_win + te_win <= len(factor):
        
        # 1. 切分窗口
        f_train = factor.iloc[cursor:cursor + tr_win]
        f_test  = factor.iloc[cursor + tr_win: cursor + tr_win + te_win]
        p_test  = price.iloc [cursor + tr_win: cursor + tr_win + te_win]

        # 2. 训练阶段：估计 ECDF & 方向
        ecdf = f_train.rank(pct=True)
        direction = 1 if f_train.corr(price.shift(-hold_n).loc[f_train.index]) > 0 else -1

        # 3. 测试阶段：映射仓位
        pct = f_test.apply(lambda x: (ecdf < x).mean())
        base_pos = np.tanh(k * (pct - 0.5))
        final_pos = base_pos * direction

        # 4. 计算 PnL
        ret = p_test.pct_change(hold_n).shift(-hold_n)
        pnl = final_pos * ret
        pnl_segments.append(pnl)

        # 5. 度量（可选：Sharpe/MDD 等）
        metrics.append(calc_metrics(pnl))

        # 6. 前移光标
        cursor += step

    # 7. 拼接净值曲线
    pnl_all = pd.concat(pnl_segments)
    return {
        "pnl": pnl_all,
        "metrics": aggregate_metrics(metrics)
    }
```

> 以上代码仅为演示思路，实际实现需加入异常处理、性能优化（如向量化）及 I/O 封装。

### **6. 通过标准与结果输出**

`pass_criteria` 建议以 TOML 形式定义，例如：

```toml
[pass_criteria]
min_sharpe = 0.5
max_mdd    = 0.3        # 30%
min_win_pct = 0.55      # 盈利周期占比
```

`run_wfa` 返回的 `metrics` 字典建议包含：`sharpe`, `mdd`, `win_pct`, `calmar`, `turnover` 等键。

`V0_WFA动态稳健性检验.py` 根据 `pass_criteria` 字段自动做布尔判断，并写入 `FactorZoo.status` 字段（`WFA_PASSED` / `WFA_FAILED`）。

所有原始时间序列（因子、仓位、净值）与摘要指标应序列化为：
* `REPORTS/wfa_results/{factor_id}/pnl.csv`
* `REPORTS/wfa_results/{factor_id}/metrics.json`
* `REPORTS/wfa_results/{factor_id}/curve.png`

### **7. 日志与可视化**

* 采用 `logging` 标准库，日志级别默认为 `INFO`，关键事件（窗口边界、Sharpe、MDD）使用 `DEBUG`。
* 失败因子应在日志中记录失败原因（不满足哪项门槛）。
* 净值曲线绘图建议使用 `matplotlib` 或 `plotly`，保存为 `png`，方便快速浏览。

---

> 如需进一步了解全局配置加载与环境变量注入，请参阅独立文档 `docs/.dev/配置管理指南.md`。