---
**第零步：样本内（IS）动态稳健性检验 (WFA) - 工程设计**
---

此部分详细阐述了将WFA（Walk-Forward Analysis）方法论，即 `docs/.dev/06_时序单因子质检.md` 中定义的"第零步"，转化为稳健、可维护、可扩展的工程代码所需遵循的结构和规范。

### **1. 核心设计哲学：清爽优雅的过程式架构**

基于项目现有代码风格和用户偏好，WFA验证模块采用**过程式编程**风格，强调：

*   **简洁性**: 避免过度抽象，函数职责单一明确，减少不必要的类封装
*   **可读性**: 代码逻辑清晰，注释详细，便于理解和维护
*   **效率性**: 优先考虑执行效率，复用现有方法，避免重复造轮子
*   **一致性**: 与项目现有的 `L1总体相关筛选.py`、`L2总体重要筛选.py` 等模块保持风格一致

### **2. 模块分工：核心库与应用脚本**

*   **核心库 (factor/validation_utils.py)**:
    *   **职责**: 提供纯粹的WFA算法实现，不依赖具体业务逻辑
    *   **风格**: 过程式函数，英文命名，专注算法核心
    *   **复用性**: 可被多个应用脚本调用，支持不同参数配置

*   **应用脚本 (script/投研_因子挖掘集成/L3动态稳健性检验.py)**:
    *   **职责**: 业务流程编排，因子查询、批量处理、结果入库
    *   **风格**: 过程式主流程，中文命名，业务逻辑清晰
    *   **集成性**: 与FactorZoo、数据加载、报告生成等模块集成

### **3. 文件与目录结构规划**

为实现"第零步：WFA动态稳健性检验"，项目需包含下列关键文件：

```plaintext
XentZ/
├── config/
│   └── tasks/
│       └── wfa_validation.toml          # WFA任务参数配置
├── factor/
│   └── validation_utils.py              # WFA核心算法库
├── script/
│   └── 投研_因子挖掘集成/
│       └── L3动态稳健性检验.py          # WFA业务流程脚本
├── reports/                             # 报告输出目录 (D:/myquant/reports/XentZ)
│   └── wfa_validation/                  # WFA验证结果目录
│       ├── factor_reports/              # 按因子ID组织的详细报告
│       │   ├── F_L3_WFA_510050.SH_001/
│       │   │   ├── pnl_curve.csv       # 净值曲线数据
│       │   │   ├── metrics.json        # 绩效指标摘要
│       │   │   └── wfa_report.png      # 可视化报告
│       │   └── F_L3_WFA_510300.SH_002/
│       └── batch_summary/               # 批次汇总报告
│           └── wfa_batch_20250703.json
```

### **4. 各模块职责详解**

#### **4.1 配置文件 (config/tasks/wfa_validation.toml)**
```toml
# WFA验证任务参数配置
[basic]
symbols = ["510050.SH", "510300.SH"]   # 目标品种列表，支持多品种
is_start_date = "2020-01-01"           # 样本内起始日期
is_end_date = "2023-12-31"             # 样本内结束日期

[wfa_params]
training_window = 750                   # 训练窗口：750个交易日(约3年)
testing_window = 60                     # 测试窗口：60个交易日(约3个月)
step_size = 60                         # 步进大小：等于测试窗口
tanh_k = 5                             # S型曲线陡峭度参数
hold_n = 1                             # 持有期：1个交易日

[pass_criteria]
min_sharpe = 0.5                       # 最小夏普比率
max_mdd = 0.30                         # 最大回撤上限
min_win_pct = 0.55                     # 最小盈利周期占比
min_calmar = 0.8                       # 最小卡玛比率
```

#### **4.2 应用脚本职责**
**`script/投研_因子挖掘集成/L3动态稳健性检验.py`**:
*   **流程编排**: 查询L2阶段通过的因子 → 批量WFA验证 → 结果判定 → 状态更新
*   **数据管理**: 因子值加载、价格数据获取、结果持久化
*   **业务逻辑**: 通过标准判定、FactorZoo状态更新、报告生成
*   **错误处理**: 异常因子跳过、失败日志记录、进度监控

#### **4.3 核心算法库职责**
**`factor/validation_utils.py`**:
*   **纯算法实现**: 专注WFA核心逻辑，不涉及业务流程
*   **高效计算**: 向量化操作，优化内存使用，支持大规模因子验证
*   **参数化设计**: 支持灵活的窗口配置、映射参数、评估指标
*   **结果标准化**: 统一的返回格式，便于下游处理

### **5. 配置管理策略：任务参数与全局配置分离**

遵循项目配置管理原则，WFA验证采用双配置模式：

#### **5.1 配置加载机制**
```python
# 脚本启动方式
python -m script.投研_因子挖掘集成.L3动态稳健性检验 --config config/tasks/wfa_validation.toml

# 脚本内配置访问
from config import settings              # 全局应用配置
from dynaconf import Dynaconf           # 任务参数配置

# 加载任务特定参数
task_params = Dynaconf(settings_files=[config_path])
```

#### **5.2 配置职责分工**
*   **全局配置 (settings)**: 数据库连接、文件路径、日志配置等基础设施
*   **任务配置 (task_params)**: WFA特定参数、通过标准、目标品种等业务参数
*   **隔离原则**: 任务参数变更不影响全局配置，支持多任务并行执行

### **6. WFA核心算法设计**

#### **6.1 算法流程概览**
WFA算法采用**滚动窗口**方式，在样本内数据上模拟真实交易环境：

1. **窗口切分**: 按时间顺序切分训练集和测试集
2. **分布学习**: 在训练集上学习因子值的经验分布函数(ECDF)
3. **方向判定**: 计算因子与未来收益的相关性，确定交易方向
4. **仓位映射**: 使用S型曲线将因子值映射为交易仓位
5. **PnL计算**: 在测试集上计算策略损益
6. **滚动前进**: 移动窗口，重复上述过程
7. **绩效评估**: 拼接所有测试期PnL，计算整体绩效指标

#### **6.2 关键技术设计要点**

**A. 时序分布学习 (避免未来信息泄露)**
```python
# 训练阶段：学习因子分布
factor_ranks = factor_train.rank(pct=True)  # 计算百分位排名
ecdf_func = lambda x: (factor_train <= x).mean()  # 构建ECDF函数

# 测试阶段：应用学到的分布
test_percentiles = factor_test.apply(ecdf_func)  # 映射为百分位
```

**B. S型仓位映射 (稳健的信号转换)**
```python
# 核心映射公式：tanh函数实现S型曲线
base_position = np.tanh(k * (percentile - 0.5))
final_position = base_position * direction

# 参数k的影响：
# k=3: 温和映射，仓位变化平缓
# k=5: 中性映射，平衡噪声过滤与信号响应
# k=10: 激进映射，接近阶梯函数
```

**C. 方向自适应 (动态因子方向)**
```python
# 使用Spearman秩相关，对异常值更稳健
future_returns = price.pct_change(hold_n).shift(-hold_n)
correlation = factor_train.corr(future_returns.loc[factor_train.index], method='spearman')
direction = 1 if correlation > 0 else -1
```

**D. 向量化优化 (提升计算效率)**
- 避免循环计算，使用pandas向量化操作
- 预分配数组空间，减少内存重分配
- 批量处理多个测试窗口的PnL计算

### **7. 绩效评估与通过标准**

#### **7.1 核心评估指标**
```python
# WFA绩效指标计算
def calculate_wfa_metrics(pnl_series: pd.Series) -> Dict:
    """计算WFA绩效指标"""
    returns = pnl_series.dropna()

    # 核心指标
    sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252)  # 年化夏普
    max_drawdown = calculate_max_drawdown(returns.cumsum())       # 最大回撤
    win_rate = (returns > 0).mean()                              # 胜率
    calmar_ratio = returns.mean() * 252 / abs(max_drawdown)      # 卡玛比率

    # 补充指标
    total_return = returns.sum()                                 # 总收益
    volatility = returns.std() * np.sqrt(252)                   # 年化波动率
    skewness = returns.skew()                                    # 偏度
    kurtosis = returns.kurtosis()                                # 峰度

    return {
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown': max_drawdown,
        'win_rate': win_rate,
        'calmar_ratio': calmar_ratio,
        'total_return': total_return,
        'volatility': volatility,
        'skewness': skewness,
        'kurtosis': kurtosis
    }
```

#### **7.2 多层次通过标准**
```python
# 严格的主要标准 (必须全部满足)
primary_criteria = {
    'min_sharpe': 0.5,      # 夏普比率 > 0.5
    'max_mdd': 0.30,        # 最大回撤 < 30%
    'min_win_rate': 0.55,   # 胜率 > 55%
}

# 优选的次要标准 (用于排序)
secondary_criteria = {
    'min_calmar': 0.8,      # 卡玛比率 > 0.8
    'max_volatility': 0.25, # 年化波动率 < 25%
    'min_skewness': -0.5,   # 偏度 > -0.5 (避免极端负偏)
}
```

#### **7.3 结果输出标准化**
```python
# 标准化的结果结构
wfa_result = {
    'factor_id': 'F_L3_WFA_510050.SH_20250703_001',
    'symbol': '510050.SH',
    'wfa_params': {...},
    'metrics': {...},
    'pass_status': 'L3_PASSED',   # L3_PASSED / L3_FAILED
    'fail_reasons': [],           # 未通过的具体原因
    'pnl_series': pd.Series,      # 完整PnL序列
    'position_series': pd.Series, # 仓位序列
    'test_periods': [...],        # 各测试期详情
}
```

### **8. 可视化与监控设计**

#### **8.1 WFA报告可视化**
```python
# 标准WFA报告图表
def generate_wfa_report(factor_id: str, wfa_result: Dict):
    """生成WFA验证报告"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # 子图1: 净值曲线
    pnl_cumsum = wfa_result['pnl_series'].cumsum()
    axes[0,0].plot(pnl_cumsum.index, pnl_cumsum.values)
    axes[0,0].set_title('WFA净值曲线')

    # 子图2: 回撤曲线
    drawdown = calculate_drawdown_series(pnl_cumsum)
    axes[0,1].fill_between(drawdown.index, 0, drawdown.values, alpha=0.3, color='red')
    axes[0,1].set_title('回撤曲线')

    # 子图3: 仓位分布
    position_series = wfa_result['position_series']
    axes[1,0].hist(position_series.values, bins=50, alpha=0.7)
    axes[1,0].set_title('仓位分布')

    # 子图4: 滚动夏普比率
    rolling_sharpe = calculate_rolling_sharpe(wfa_result['pnl_series'], window=60)
    axes[1,1].plot(rolling_sharpe.index, rolling_sharpe.values)
    axes[1,1].axhline(y=0.5, color='red', linestyle='--', label='通过线')
    axes[1,1].set_title('滚动夏普比率(60日)')

    plt.tight_layout()

    # 保存到项目报告目录
    from config import REPORTS_DIR
    report_dir = REPORTS_DIR / 'wfa_validation' / 'factor_reports' / factor_id
    report_dir.mkdir(parents=True, exist_ok=True)
    plt.savefig(report_dir / 'wfa_report.png', dpi=300)
```

#### **8.2 批量处理监控**
```python
# 进度监控与异常处理
def monitor_wfa_batch_progress(total_factors: int):
    """WFA批量处理进度监控"""
    processed = 0
    passed = 0
    failed = 0

    def update_progress(factor_id: str, status: str, metrics: Dict = None):
        nonlocal processed, passed, failed
        processed += 1

        if status == 'L3_PASSED':
            passed += 1
            print(f"✅ {factor_id}: 通过 (Sharpe={metrics['sharpe_ratio']:.3f})")
        else:
            failed += 1
            print(f"❌ {factor_id}: 失败")

        # 进度报告
        if processed % 10 == 0:
            progress_pct = processed / total_factors * 100
            pass_rate = passed / processed * 100 if processed > 0 else 0
            print(f"📊 进度: {progress_pct:.1f}% | 通过率: {pass_rate:.1f}%")

    return update_progress
```

#### **8.3 日志记录规范**
- **INFO级别**: 批次开始/结束、因子处理进度、通过率统计
- **DEBUG级别**: 窗口切分详情、中间计算结果、参数配置
- **WARNING级别**: 数据质量问题、计算异常、边界情况
- **ERROR级别**: 严重错误、数据缺失、系统异常

---

### **9. 实施优先级与里程碑**

#### **Phase 1: 核心算法实现** (优先级: 高)
- [ ] 实现 `factor/validation_utils.py` 中的 `run_wfa` 函数
- [ ] 完成S型仓位映射、ECDF学习、PnL计算等核心逻辑
- [ ] 单因子WFA验证功能验证

#### **Phase 2: 业务流程集成** (优先级: 高)
- [ ] 实现 `L3动态稳健性检验.py` 主流程脚本
- [ ] 集成FactorZoo查询、状态更新、批量处理功能
- [ ] 配置文件解析与参数验证

#### **Phase 3: 可视化与监控** (优先级: 中)
- [ ] WFA报告图表生成
- [ ] 批量处理进度监控
- [ ] 异常处理与日志记录

#### **Phase 4: 性能优化** (优先级: 低)
- [ ] 向量化计算优化
- [ ] 内存使用优化
- [ ] 并行处理支持

---

> 本设计文档遵循项目现有的过程式编程风格，强调简洁、高效、可维护的代码实现。核心算法与业务流程分离，便于测试和复用。