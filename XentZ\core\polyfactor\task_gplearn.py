
from dataclasses import dataclass, field
from typing import List

from core.task_base import Task
from datafeed.features.feature_utils import FeatPreprocessing
from datafeed.hku_dataloader import HKUDataloader
from datafeed.features.alphas import AlphaBase, AlphaOrigin, Alpha158, AlphaLite, AlphaJZAL

@dataclass
class TaskGPlearn(Task):
    # ============ 训练集定义 ==========
    train_symbols: List[str] = field(default_factory=list)
    train_freq: str = 'D'
    train_start_date: str = '20100101'
    train_end_date: str = None
    # ============ 目标集(测试集)定义 ==========
    target_symbols: List[str] = field(default_factory=list)
    target_freq: str = 'D'
    target_start_date: str = '20100101'
    target_end_date: str = None
    # ============ 特征定义 ==========
    feature_names: list = field(default_factory=list)
    feature_exprs: list = field(default_factory=list)
    func_set: list = field(default_factory=list)
    
    def load_train_and_calc_exprs(self):
        ''' Return: [datetime] symbol ohlc amount volume feat1..featn '''
        df_all = HKUDataloader.load_df_all(self.train_symbols, 
                                  set_index=True, 
                                  start_date=self.train_start_date, 
                                  end_date=self.train_end_date,
                                  freq = self.train_freq, 
                                  recover=self.recover)
        df_all = FeatPreprocessing.calc_expr_df_all(df_all, self.feature_exprs, self.feature_names)
        df_all.dropna(inplace=True)  # 对结果影响非常重要! 会删掉一些新品种的数据
        return df_all
    
    def load_target_and_calc_exprs(self):
        ''' Return: [datetime] symbol ohlc amount volume feat1..featn '''
        df_all = HKUDataloader.load_df_all(self.target_symbols, 
                                  set_index=True, 
                                  start_date=self.target_start_date, 
                                  end_date=self.target_end_date,
                                  freq = self.target_freq, 
                                  recover=self.recover)
        df_all = FeatPreprocessing.calc_expr_df_all(df_all, self.feature_exprs, self.feature_names)
        df_all.dropna(inplace=True)  # 对结果影响非常重要! 会删掉一些新品种的数据
        return df_all
    
def get_task_gplearn() -> TaskGPlearn:
    task = TaskGPlearn()
    task.name = '因子挖掘'
    
    # 单symbol测试 (目前配置)
    task.train_symbols = ['510050.SH']
    
    # 多symbol配置建议 (可选，启用后有更好的因子挖掘效果)
    # task.train_symbols = ['510050.SH', '510300.SH', '000016.SH', '159915.SZ']
    task.train_start_date = '20200101'
    task.train_end_date = None
    task.train_freq = 'D'
    
    task.target_symbols = [ "159915.SZ", "159934.SZ", "510880.SH", "513100.SH", "513520.SH",]
    task.target_start_date = '20210101'
    task.target_end_date = None
    task.target_freq = 'D'
    
    a0 = AlphaBase()
    exprs, names = a0.get_a_label()
    task.feature_exprs += exprs
    task.feature_names += names    
    a1 = AlphaOrigin()
    exprs, names = a1.get_exprs_names()
    task.feature_exprs += exprs
    task.feature_names += names
    a2 = Alpha158()
    exprs, names = a2.get_exprs_names()
    task.feature_exprs += exprs
    task.feature_names += names
    a3 = AlphaLite()
    exprs, names = a3.get_exprs_names()
    task.feature_exprs += exprs
    task.feature_names += names
    a4 = AlphaJZAL()
    exprs, names = a4.get_exprs_names()
    task.feature_exprs += exprs
    task.feature_names += names

    # # 添加TSFresh特征
    # a_tsfresh = AlphaTSFresh(
    #     feature_set='efficient',
    #     columns=['close', 'volume', 'high', 'low'],
    #     prefix='tsfsh_'
    # )
    # tsfresh_exprs, tsfresh_names = a_tsfresh.get_exprs_names()
    # task.feature_exprs += tsfresh_exprs
    # task.feature_names += tsfresh_names
    
    from core.polyfactor.gplearn.functions import _function_map
    task.func_set = list(_function_map.keys())
    
    return task


