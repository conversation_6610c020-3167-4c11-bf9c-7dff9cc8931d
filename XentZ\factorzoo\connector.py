#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FactorZoo 数据库连接器
负责与FactorZoo数据库的所有交互操作
"""

import sqlite3
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Any
import contextlib

# 路径配置
FACTOR_ZOO_DIR = Path("D:/myquant/FZoo/")
FACTOR_ZOO_DB = FACTOR_ZOO_DIR / "database" / "factorzoo.sqlite"

class FactorZooConnector:
    """FactorZoo 数据库连接器"""
    
    def __init__(self, db_path: str = None):
        """初始化连接器
        
        Args:
            db_path: 数据库路径，默认使用配置路径
        """
        self.db_path = db_path or str(FACTOR_ZOO_DB)
        
    @contextlib.contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 允许按列名访问
        try:
            yield conn
        finally:
            conn.close()
    
    def get_categories(self, level: int = None) -> List[Dict]:
        """获取因子分类列表
        
        Args:
            level: 分类层级，None表示所有层级
            
        Returns:
            分类列表
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            if level is not None:
                cursor.execute("SELECT * FROM factor_categories WHERE category_level = ? ORDER BY sort_order", (level,))
            else:
                cursor.execute("SELECT * FROM factor_categories ORDER BY category_level, sort_order")
            
            return [dict(row) for row in cursor.fetchall()]
    
    def get_universes(self, current_only: bool = True) -> List[Dict]:
        """获取股票池列表
        
        Args:
            current_only: 是否只返回当前版本
            
        Returns:
            股票池列表
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            if current_only:
                cursor.execute("SELECT * FROM universes WHERE is_current = 1 ORDER BY universe_id")
            else:
                cursor.execute("SELECT * FROM universes ORDER BY universe_id, version")
            
            return [dict(row) for row in cursor.fetchall()]
    
    def create_batch(self, batch_id: str, batch_name: str, creation_tool: str,
                    source_symbols: List[str] = None, source_frequencies: List[str] = None,
                    source_date_ranges: Dict = None, generation_params: Dict = None) -> bool:
        """创建因子批次
        
        Args:
            batch_id: 批次ID
            batch_name: 批次名称
            creation_tool: 创建工具
            source_symbols: 数据源标的
            source_frequencies: 数据频率
            source_date_ranges: 日期范围
            generation_params: 生成参数
            
        Returns:
            是否创建成功
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO factor_batches (
                        batch_id, batch_name, creation_tool,
                        source_symbols, source_frequencies, source_date_ranges,
                        generation_params, start_time, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    batch_id, batch_name, creation_tool,
                    json.dumps(source_symbols) if source_symbols else None,
                    json.dumps(source_frequencies) if source_frequencies else None,
                    json.dumps(source_date_ranges) if source_date_ranges else None,
                    json.dumps(generation_params) if generation_params else None,
                    datetime.now(), datetime.now()
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            print(f"创建批次失败: {e}")
            return False
    
    def add_factor(self, factor_id: str, factor_name: str, factor_expression: str,
                  factor_type: str, data_source_type: str, symbols: List[str],
                  frequencies: List[str], date_ranges: Dict, creation_method: str,
                  primary_category: str, batch_id: str = None, **kwargs) -> bool:
        """添加因子 - 实现UPSERT功能，处理业务唯一约束
        
        Args:
            factor_id: 因子ID
            factor_name: 因子名称
            factor_expression: 因子表达式
            factor_type: 因子类型 ('time_series', 'cross_section', 'panel')
            data_source_type: 数据源类型
            symbols: 标的列表
            frequencies: 频率列表
            date_ranges: 日期范围
            creation_method: 创建方法
            primary_category: 主分类
            batch_id: 批次ID（可选）
            **kwargs: 其他参数
            
        Returns:
            是否操作成功
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 准备基础参数
                base_params = {
                    'factor_id': factor_id,
                    'factor_name': factor_name,
                    'factor_expression': factor_expression,
                    'factor_type': factor_type,
                    'data_source_type': data_source_type,
                    'symbols': json.dumps(symbols),
                    'frequencies': json.dumps(frequencies),
                    'date_ranges': json.dumps(date_ranges),
                    'creation_method': creation_method,
                    'primary_category': primary_category
                }
                
                # 添加可选参数
                if batch_id:
                    base_params['batch_id'] = batch_id
                
                # 添加其他参数，确保JSON序列化
                for key, value in kwargs.items():
                    if isinstance(value, (dict, list)):
                        base_params[key] = json.dumps(value)
                    else:
                        base_params[key] = value
                
                # 获取关键字段（用于业务唯一性检查）
                pipeline_step = kwargs.get('pipeline_step', 'L0')
                pipeline_mode = kwargs.get('pipeline_mode', 'auto_pipeline')
                
                # 检查业务唯一约束：表达式+数据源+流水线上下文
                cursor.execute("""
                    SELECT factor_id FROM factors 
                    WHERE factor_expression = ? 
                      AND symbols = ? 
                      AND frequencies = ? 
                      AND date_ranges = ?
                      AND pipeline_step = ?
                      AND pipeline_mode = ?
                      AND creation_method = ?
                      AND status != 'deprecated'
                """, (
                    factor_expression,
                    json.dumps(symbols),
                    json.dumps(frequencies), 
                    json.dumps(date_ranges),
                    pipeline_step,
                    pipeline_mode,
                    creation_method
                ))
                
                existing_factor = cursor.fetchone()
                
                if existing_factor:
                    # 存在相同业务语义的因子，更新现有记录
                    existing_factor_id = existing_factor[0]
                    print(f"🔄 发现相同业务语义因子 {existing_factor_id}，执行更新")
                    
                    # 更新现有记录，但保持原factor_id
                    base_params['factor_id'] = existing_factor_id
                    success = self._update_record(cursor, base_params.copy())
                    operation = f"更新 (原ID: {existing_factor_id})"
                    final_factor_id = existing_factor_id
                else:
                    # 检查factor_id是否被占用
                    cursor.execute("SELECT factor_id FROM factors WHERE factor_id = ?", (factor_id,))
                    id_exists = cursor.fetchone() is not None
                    
                    if id_exists:
                        # factor_id被占用，但业务语义不同，这是冲突情况
                        print(f"⚠️  因子ID {factor_id} 已被占用但业务语义不同，生成新ID")
                        # 生成新的factor_id
                        import time
                        timestamp = str(int(time.time() * 1000))[-8:]
                        new_factor_id = f"{factor_id}_{timestamp}"
                        base_params['factor_id'] = new_factor_id
                        final_factor_id = new_factor_id
                    else:
                        final_factor_id = factor_id
                    
                    # 插入新记录
                    base_params['creation_date'] = datetime.now().date()
                    base_params['created_at'] = datetime.now()
                    success = self._insert_record(cursor, base_params)
                    operation = "插入"
                
                if success:
                    conn.commit()
                    print(f"✅ 因子 {final_factor_id} {operation}成功")
                    return True
                else:
                    print(f"❌ 因子 {final_factor_id} {operation}失败")
                    return False
                
        except Exception as e:
            print(f"❌ 因子操作失败: {e}")
            return False
    
    def _insert_record(self, cursor, params: Dict) -> bool:
        """插入新记录的内部方法"""
        try:
            columns = ', '.join(params.keys())
            placeholders = ', '.join(['?' for _ in params])
            
            cursor.execute(f"""
                INSERT INTO factors ({columns})
                VALUES ({placeholders})
            """, list(params.values()))
            
            return True
        except Exception as e:
            print(f"插入记录失败: {e}")
            return False
    
    def _update_record(self, cursor, params: Dict) -> bool:
        """更新现有记录的内部方法"""
        try:
            # 移除factor_id，因为它是WHERE条件
            factor_id = params.pop('factor_id')
            
            # 添加更新时间
            params['updated_at'] = datetime.now()
            
            # 构建SET子句
            set_clause = ', '.join([f"{k} = ?" for k in params.keys()])
            
            cursor.execute(f"""
                UPDATE factors SET {set_clause}
                WHERE factor_id = ?
            """, list(params.values()) + [factor_id])
            
            return cursor.rowcount > 0
        except Exception as e:
            print(f"更新记录失败: {e}")
            return False

    def update_factor_performance(self, factor_id: str, computation_time_ms: int = None,
                                 memory_usage_mb: float = None, **kwargs) -> bool:
        """更新因子性能数据 - 顺带填充性能字段

        Args:
            factor_id: 因子ID
            computation_time_ms: 计算耗时(毫秒)
            memory_usage_mb: 内存使用(MB)
            **kwargs: 其他性能参数

        Returns:
            是否更新成功
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 构建更新参数
                update_params = {}
                if computation_time_ms is not None:
                    update_params['last_computation_time_ms'] = computation_time_ms
                if memory_usage_mb is not None:
                    update_params['max_memory_usage_mb'] = memory_usage_mb

                # 添加其他性能参数
                for key, value in kwargs.items():
                    if key in ['avg_computation_time_ms', 'resource_intensity', 'performance_notes']:
                        update_params[key] = value

                if not update_params:
                    return True  # 无需更新

                # 添加更新时间
                update_params['last_performance_update'] = datetime.now()

                # 构建SQL
                set_clause = ', '.join([f"{k} = ?" for k in update_params.keys()])

                cursor.execute(f"""
                    UPDATE factors SET {set_clause}
                    WHERE factor_id = ?
                """, list(update_params.values()) + [factor_id])

                conn.commit()
                return cursor.rowcount > 0

        except Exception as e:
            print(f"更新因子性能数据失败: {e}")
            return False
    
    def search_factors(self, filters: Dict = None, limit: int = 100) -> List[Dict]:
        """搜索因子
        
        Args:
            filters: 过滤条件字典
            limit: 结果限制数量
            
        Returns:
            因子列表
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            base_sql = """
                SELECT f.*, c.category_name as primary_category_name
                FROM factors f
                LEFT JOIN factor_categories c ON f.primary_category = c.category_id
                WHERE f.status = 'active'
            """
            
            params = []
            
            if filters:
                if 'batch_id' in filters:
                    base_sql += " AND f.batch_id = ?"
                    params.append(filters['batch_id'])
                    
                if 'creation_method' in filters:
                    base_sql += " AND f.creation_method = ?"
                    params.append(filters['creation_method'])
                    
                if 'primary_category' in filters:
                    base_sql += " AND f.primary_category = ?"
                    params.append(filters['primary_category'])
                    
                if 'pipeline_step' in filters:
                    base_sql += " AND f.pipeline_step = ?"
                    params.append(filters['pipeline_step'])
                
                # 添加因子表达式搜索
                if 'factor_expression' in filters:
                    base_sql += " AND f.factor_expression = ?"
                    params.append(filters['factor_expression'])
                
                # 添加symbols搜索 - JSON数组比较
                if 'symbols' in filters:
                    import json
                    symbols_json = json.dumps(filters['symbols'])
                    base_sql += " AND f.symbols = ?"
                    params.append(symbols_json)
                
                # 添加目标标签搜索
                if 'target_label' in filters:
                    base_sql += " AND f.target_label = ?"
                    params.append(filters['target_label'])
                
                # 添加因子类型搜索
                if 'factor_type' in filters:
                    base_sql += " AND f.factor_type = ?"
                    params.append(filters['factor_type'])
                
                # 添加数据源类型搜索
                if 'data_source_type' in filters:
                    base_sql += " AND f.data_source_type = ?"
                    params.append(filters['data_source_type'])
            
            base_sql += f" ORDER BY f.created_at DESC LIMIT {limit}"
            
            cursor.execute(base_sql, params)
            return [dict(row) for row in cursor.fetchall()]
    
    def get_factor_stats(self) -> Dict:
        """获取因子统计信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            stats = {}
            
            # 总因子数
            cursor.execute("SELECT COUNT(*) FROM factors WHERE status = 'active'")
            stats['total_factors'] = cursor.fetchone()[0]
            
            # 按创建方法统计
            cursor.execute("""
                SELECT creation_method, COUNT(*) 
                FROM factors 
                WHERE status = 'active' 
                GROUP BY creation_method
            """)
            stats['by_creation_method'] = dict(cursor.fetchall())
            
            # 按分类统计
            cursor.execute("""
                SELECT primary_category, COUNT(*) 
                FROM factors 
                WHERE status = 'active' 
                GROUP BY primary_category
            """)
            stats['by_category'] = dict(cursor.fetchall())
            
            # 按流水线步骤统计
            cursor.execute("""
                SELECT pipeline_step, COUNT(*) 
                FROM factors 
                WHERE status = 'active' 
                GROUP BY pipeline_step
            """)
            stats['by_pipeline_step'] = dict(cursor.fetchall())
            
            return stats 