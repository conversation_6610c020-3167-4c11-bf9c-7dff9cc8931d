import pandas as pd
import numpy as np
from common.cls_base import BaseObj
from datafeed.expr_funcs.expr import calc_expr
from datafeed.features.feature_utils import FeatPreprocessing
from config.settings import get_norm_params

class FactorLoader(BaseObj):
    def __init__(self):
        pass

    @staticmethod
    def get_fct_df(base_df: pd.DataFrame,
                   model_type: str = 'robust', norm_type: str = 'X', fcts: list = None):
        """
        加载因子数据，基于给定的因子表达式列表
        
        Args:
            base_df: 基础数据DataFrame，包含OHLCV等基础数据
            model_type: 归一化模型类型，'robust'或'linear'等
            norm_type: 归一化类型，'X'或'label'或'pos'
            fcts: 因子表达式列表，可以是GP挖掘结果或手动编写
            
        Returns:
            pd.DataFrame: 因子值DataFrame，包含计算出的因子
        """
        if base_df is None or base_df.empty:
            FactorLoader.log("输入的base_df为空", level="ERROR")
            return pd.DataFrame()
        
        # 参数处理
        fcts = fcts or []
        
        if not fcts:
            FactorLoader.log("因子表达式列表为空", level="WARNING")
            return base_df.copy()
        
        # 获取归一化参数
        norm_params = get_norm_params(model_type, norm_type)
        
        # 使用base_df作为计算基础
        df = base_df.copy()
        
        # 删除缺失值的行
        df.dropna(axis=0, how='any', inplace=True)
        cols_selected = []
        new_features = []
        
        # 处理因子表达式
        if fcts:
            FactorLoader.log(f"开始计算{len(fcts)}个因子表达式", level="INFO")
            for fct in fcts:
                try:
                    if not fct or len(str(fct).strip()) == 0:
                        continue
                        
                    # 检查因子是否已存在于df中
                    if fct in df.columns:
                        # 因子已存在，直接添加到选中列表
                        cols_selected.append(fct)
                    else:
                        # 需要计算因子表达式
                        se_result = calc_expr(df, fct)
                        
                        # 提取数值数组，统一处理
                        if isinstance(se_result, np.ndarray):
                            values = se_result
                        elif isinstance(se_result, pd.Series):
                            values = se_result.values
                        else:
                            values = np.array(se_result)
                        
                        # 归一化处理（如果不是'skip'模式）
                        if norm_params != 'skip':
                            values = FeatPreprocessing.norm(values, **norm_params)
                        
                        # 转换为float32并创建Series
                        se = pd.Series(np.float32(values), index=df.index, name=fct)
                        
                        new_features.append(se)
                        cols_selected.append(fct)
                        
                except Exception as e:
                    FactorLoader.log(f'计算因子 {fct} 时出现异常: {e}', level="ERROR")
                    continue
            
            # 合并新特征
            if new_features:
                # 使用 pd.concat 一次性合并所有新特征，避免DataFrame碎片化
                new_features_df = pd.concat(new_features, axis=1)
                df = pd.concat([df, new_features_df], axis=1)
        
        # 最终清理异常值
        df = df.replace([np.inf, np.nan, -np.inf], 0.0)
        
        # 构建最终返回的列：只返回fcts中指定的因子
        if cols_selected:
            # 筛选出存在的列
            valid_cols = [col for col in cols_selected if col in df.columns]
            
            if valid_cols:
                # 直接使用列索引选择，避免逐个构建字典
                result_df = df[valid_cols].copy()
            else:
                FactorLoader.log("没有成功处理任何因子", level="WARNING")
                return pd.DataFrame()
        else:
            FactorLoader.log("没有成功处理任何因子", level="WARNING")
            return pd.DataFrame()
        
        # 处理重复列名（如果存在）
        if len(set(result_df.columns)) != len(result_df.columns):
            # 只有在存在重复列名时才进行处理
            final_cols_unique = []
            seen_cols = set()
            for col in valid_cols:
                if col not in seen_cols:
                    final_cols_unique.append(col)
                    seen_cols.add(col)
                else:
                    # 重复的列名，添加后缀
                    counter = 1
                    new_col = f"{col}_dup{counter}"
                    while new_col in seen_cols:
                        counter += 1
                        new_col = f"{col}_dup{counter}"
                    final_cols_unique.append(new_col)
                    seen_cols.add(new_col)
                    # 复制数据到新列名
                    result_df[new_col] = result_df[col]
            
            # 只保留最终的列
            result_df = result_df[final_cols_unique]
            FactorLoader.log(f"返回结果: {len(final_cols_unique)}个指定因子（处理了重复列名）", level="INFO")
        else:
            FactorLoader.log(f"返回结果: {len(valid_cols)}个指定因子", level="INFO")
        
        return result_df