# L2Lasso筛选脚本使用说明

## 📋 功能概述

`l2_lasso_filter.py` 是基于原库L2Lasso方法实现的因子筛选脚本，使用sklearn的LassoCV进行特征选择。

### 主要功能
- ✅ 基于LassoCV的自动特征选择和正则化
- ✅ 支持多批次因子的统一筛选处理
- ✅ 支持训练集分割、交叉验证、并行计算
- ✅ 集成运行时监控和统计记录功能
- ✅ 支持筛选后因子的自动入库到FactorZoo
- ✅ 支持因子值的持久化存储
- ✅ 采用简洁优雅的过程式编程风格，注重执行效率

## 🚀 使用方法

### 1. 直接运行（推荐）
```bash
cd XentZ/core/polyfactor
python l2_lasso_filter.py
```

### 2. 命令行参数运行
```bash
# 指定训练集比例
python l2_lasso_filter.py --split 0.8

# 指定交叉验证折数
python l2_lasso_filter.py --cv 3

# 指定最大迭代次数
python l2_lasso_filter.py --iter 50000

# 指定收敛容忍度
python l2_lasso_filter.py --tol 1e-3

# 指定输入管道步骤
python l2_lasso_filter.py --stage L0

# 组合参数
python l2_lasso_filter.py --split 0.7 --cv 5 --iter 100000 --tol 1e-4 --stage L0
```

### 3. 程序化调用
```python
from core.polyfactor.l2_lasso_filter import run_l2_lasso_filter_pipeline

# 使用默认参数
results = run_l2_lasso_filter_pipeline()

# 指定参数
results = run_l2_lasso_filter_pipeline(
    split_perc=0.7,
    cv_folds=5,
    max_iter=100000,
    tol=1e-4,
    pipeline_step='L0'
)

# 指定特定批次
results = run_l2_lasso_filter_pipeline(
    batch_ids=['GP_510050.SH_20241229_L0_abc123', 'GP_510300.SH_20241229_L0_def456']
)
```

## ⚙️ 配置参数

### LassoCV参数

#### 训练集比例 (split_perc)
- **默认值**: 0.7
- **说明**: 用于训练LassoCV模型的数据比例
- **建议值**: 0.6-0.8之间

#### 交叉验证折数 (cv_folds)
- **默认值**: 5
- **说明**: LassoCV交叉验证的折数
- **建议值**: 3-10之间，折数越多越稳定但计算越慢

#### 最大迭代次数 (max_iter)
- **默认值**: 100000
- **说明**: Lasso算法的最大迭代次数
- **建议值**: 10000-100000，复杂数据需要更多迭代

#### 收敛容忍度 (tol)
- **默认值**: 1e-4
- **说明**: 算法收敛的容忍度
- **建议值**: 1e-3到1e-5之间

#### 输入管道步骤 (pipeline_step)
- **默认值**: 'L0'
- **可选值**: 'L0', 'L1', 'L2', 'L3'
- **说明**: 指定从哪个管道步骤加载因子数据

## 📊 输出结果

### 1. 控制台输出
```
🚀 开始L2Lasso筛选流程...
📋 将处理以下批次: 5 个
🎯 LassoCV参数:
    训练集比例: 0.7
    交叉验证折数: 5
    最大迭代次数: 100000
    收敛容忍度: 0.0001

==================================================
🎯 处理品种: 510050.SH
==================================================
开始对 510050.SH 的 25 个因子进行LassoCV筛选...
训练集样本数: 730, 特征数: 25
lasso.alpha_: 0.0123456789
len(lasso.coef_): 8
lasso.intercept_: 0.001234
lasso.n_iter_: 45
✅ 510050.SH 筛选完成: 8 个因子保留

🎉 L2Lasso筛选完成!
📊 处理统计:
    输入因子: 25 个
    输出因子: 8 个
    筛选效率: 32.0%
    处理品种: 1 个
    处理耗时: 15.2 秒
```

### 2. 文件输出
- **筛选结果**: `reports/l2_lasso_filtered_factors_YYYYMMDD_UID.csv`
- **运行统计**: `reports/l2_lasso_filter_stats_YYYYMMDD_UID.json`

### 3. 数据库存储
- **FactorZoo**: 筛选后的因子自动入库，批次ID格式为 `L2_LASSO_SYMBOL_YYYYMMDD_UID`
- **FactorValueManager**: 因子值持久化存储，支持后续快速加载

## 🔧 核心算法

### LassoCV筛选逻辑
1. **数据准备**: 从指定批次加载因子数据和基础数据
2. **训练集分割**: 按指定比例分割训练集和测试集
3. **缺失值处理**: 将NaN值替换为0（遵循原库逻辑）
4. **LassoCV训练**: 使用交叉验证自动选择最优正则化参数
5. **特征选择**: 保留非零系数对应的因子
6. **结果输出**: 返回筛选后的因子列表

### LassoCV参数设置（基于原库）
```python
lasso = LassoCV(
    cv=5,                # 交叉验证折数
    random_state=0,      # 随机状态，确保结果可复现
    max_iter=100000,     # 最大迭代次数
    tol=1e-4,            # 收敛容忍度
    n_jobs=-1,           # 使用所有可用处理器并行计算
    selection='cyclic'   # 选择坐标轴下降法类型
)
```

### 关键特性
- **自动正则化**: LassoCV自动选择最优的alpha参数
- **特征选择**: 自动过滤掉系数为0的无关特征
- **并行计算**: 支持多核并行加速计算
- **交叉验证**: 提高模型的泛化能力和稳定性

## 📈 性能优化

### 算法优化
- 使用sklearn的高效LassoCV实现
- 支持多核并行计算（n_jobs=-1）
- 智能的坐标下降算法（selection='cyclic'）

### 内存优化
- 分批处理大数据集
- 及时释放不需要的中间变量
- 使用numpy数组进行高效计算

## 🔍 故障排除

### 常见问题

1. **因子数量不足**
   ```
   品种 XXX 有效因子数量不足，跳过筛选
   ```
   **解决方案**: LassoCV需要至少2个因子，确保输入数据包含足够的特征

2. **收敛问题**
   ```
   ConvergenceWarning: Objective did not converge
   ```
   **解决方案**: 增加max_iter参数或放宽tol参数

3. **内存不足**
   ```
   MemoryError: Unable to allocate array
   ```
   **解决方案**: 减少cv_folds参数或分批处理数据

### 调试模式
设置更详细的日志输出：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 🔗 相关文件

- **主脚本**: `core/polyfactor/l2_lasso_filter.py`
- **测试脚本**: `core/polyfactor/test_l2_lasso_logic.py`
- **配置文件**: `config/settings.py` (cfg_mine部分)
- **原库参考**: `原库/polyfactorX/engine/gp_engine.py` (L2Lasso方法)
- **因子管理**: `factorzoo/factor_value_manager.py`

## 📝 更新日志

### v1.0.0 (2024-12-29)
- ✅ 实现基础L2Lasso筛选功能
- ✅ 基于原库L2Lasso方法，使用sklearn.LassoCV
- ✅ 支持多批次因子处理
- ✅ 集成FactorZoo入库功能
- ✅ 支持因子值持久化
- ✅ 添加运行时监控和统计
- ✅ 完善错误处理和日志记录
- ✅ 通过逻辑测试验证，能正确筛选有效因子
