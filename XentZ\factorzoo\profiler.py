#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FactorZoo 性能监控和告警系统
实时监控系统性能指标，提供告警和优化建议
"""

import time
import threading
import psutil
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass, field
from collections import deque, defaultdict
import json
from pathlib import Path

from .config_cache import get_performance_config


@dataclass
class PerformanceMetric:
    """性能指标数据"""
    timestamp: float
    metric_name: str
    value: float
    unit: str
    category: str = "general"
    metadata: Dict = field(default_factory=dict)


@dataclass
class PerformanceAlert:
    """性能告警数据"""
    alert_id: str
    timestamp: float
    level: str  # INFO, WARNING, ERROR, CRITICAL
    metric_name: str
    current_value: float
    threshold: float
    message: str
    category: str = "performance"
    resolved: bool = False
    resolve_timestamp: Optional[float] = None


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self, collection_interval: int = 30):
        self.collection_interval = collection_interval
        self.metrics_history = defaultdict(deque)
        self.max_history_size = 1000
        self._collecting = False
        self._collector_thread = None
        self._lock = threading.RLock()
    
    def start_collection(self):
        """开始指标收集"""
        if self._collecting:
            return
        
        self._collecting = True
        self._collector_thread = threading.Thread(
            target=self._collection_worker,
            daemon=True
        )
        self._collector_thread.start()
        logging.info("性能指标收集已开始")
    
    def stop_collection(self):
        """停止指标收集"""
        self._collecting = False
        if self._collector_thread:
            self._collector_thread.join(timeout=5.0)
        logging.info("性能指标收集已停止")
    
    def add_metric(self, metric: PerformanceMetric):
        """添加性能指标"""
        with self._lock:
            history = self.metrics_history[metric.metric_name]
            history.append(metric)
            
            # 保持历史记录在合理大小
            if len(history) > self.max_history_size:
                history.popleft()
    
    def get_metric_history(self, metric_name: str, 
                          time_range_minutes: int = 60) -> List[PerformanceMetric]:
        """获取指标历史"""
        with self._lock:
            if metric_name not in self.metrics_history:
                return []
            
            cutoff_time = time.time() - (time_range_minutes * 60)
            history = self.metrics_history[metric_name]
            
            return [
                metric for metric in history 
                if metric.timestamp >= cutoff_time
            ]
    
    def get_latest_metric(self, metric_name: str) -> Optional[PerformanceMetric]:
        """获取最新指标值"""
        with self._lock:
            if metric_name not in self.metrics_history:
                return None
            
            history = self.metrics_history[metric_name]
            return history[-1] if history else None
    
    def _collection_worker(self):
        """指标收集工作线程"""
        while self._collecting:
            try:
                # 收集系统指标
                self._collect_system_metrics()
                
                # 收集应用指标（如果有的话）
                self._collect_application_metrics()
                
                time.sleep(self.collection_interval)
                
            except Exception as e:
                logging.error(f"指标收集出错: {e}")
                time.sleep(5)  # 出错后短暂休息
    
    def _collect_system_metrics(self):
        """收集系统指标"""
        current_time = time.time()
        
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            self.add_metric(PerformanceMetric(
                timestamp=current_time,
                metric_name="cpu_usage_percent",
                value=cpu_percent,
                unit="%",
                category="system"
            ))
            
            # 内存使用率
            memory = psutil.virtual_memory()
            self.add_metric(PerformanceMetric(
                timestamp=current_time,
                metric_name="memory_usage_percent",
                value=memory.percent,
                unit="%",
                category="system"
            ))
            
            # 内存使用量（MB）
            memory_mb = memory.used / (1024 * 1024)
            self.add_metric(PerformanceMetric(
                timestamp=current_time,
                metric_name="memory_usage_mb",
                value=memory_mb,
                unit="MB",
                category="system"
            ))
            
            # 磁盘IO
            disk_io = psutil.disk_io_counters()
            if disk_io:
                self.add_metric(PerformanceMetric(
                    timestamp=current_time,
                    metric_name="disk_read_mb_per_sec",
                    value=disk_io.read_bytes / (1024 * 1024),
                    unit="MB/s",
                    category="system"
                ))
                
                self.add_metric(PerformanceMetric(
                    timestamp=current_time,
                    metric_name="disk_write_mb_per_sec",
                    value=disk_io.write_bytes / (1024 * 1024),
                    unit="MB/s",
                    category="system"
                ))
            
        except Exception as e:
            logging.warning(f"系统指标收集失败: {e}")
    
    def _collect_application_metrics(self):
        """收集应用指标"""
        # 这里可以集成FactorZoo的特定指标
        # 例如缓存命中率、加载时间等
        pass


class AlertManager:
    """告警管理器"""
    
    def __init__(self):
        self.alert_rules = {}
        self.active_alerts = {}
        self.alert_history = []
        self.alert_handlers = []
        self._lock = threading.RLock()
        
        # 设置默认告警规则
        self._setup_default_rules()
    
    def add_alert_rule(self, rule_name: str, metric_name: str, 
                      threshold: float, comparison: str = "greater",
                      level: str = "WARNING", message_template: str = None):
        """添加告警规则"""
        with self._lock:
            self.alert_rules[rule_name] = {
                'metric_name': metric_name,
                'threshold': threshold,
                'comparison': comparison,  # greater, less, equal
                'level': level,
                'message_template': message_template or f"{metric_name} 异常"
            }
        
        logging.info(f"添加告警规则: {rule_name}")
    
    def check_metrics(self, metrics: List[PerformanceMetric]):
        """检查指标是否触发告警"""
        for metric in metrics:
            self._check_single_metric(metric)
    
    def _check_single_metric(self, metric: PerformanceMetric):
        """检查单个指标"""
        for rule_name, rule in self.alert_rules.items():
            if rule['metric_name'] != metric.metric_name:
                continue
            
            # 检查是否触发告警
            triggered = self._evaluate_rule(metric.value, rule)
            
            if triggered:
                # 创建告警
                alert_id = f"{rule_name}_{int(metric.timestamp)}"
                
                if alert_id not in self.active_alerts:
                    alert = PerformanceAlert(
                        alert_id=alert_id,
                        timestamp=metric.timestamp,
                        level=rule['level'],
                        metric_name=metric.metric_name,
                        current_value=metric.value,
                        threshold=rule['threshold'],
                        message=rule['message_template'].format(
                            value=metric.value,
                            threshold=rule['threshold']
                        )
                    )
                    
                    self._trigger_alert(alert)
            else:
                # 检查是否需要解除告警
                self._check_alert_resolution(metric, rule_name)
    
    def _evaluate_rule(self, value: float, rule: Dict) -> bool:
        """评估告警规则"""
        threshold = rule['threshold']
        comparison = rule['comparison']
        
        if comparison == "greater":
            return value > threshold
        elif comparison == "less":
            return value < threshold
        elif comparison == "equal":
            return abs(value - threshold) < 0.001
        else:
            return False
    
    def _trigger_alert(self, alert: PerformanceAlert):
        """触发告警"""
        with self._lock:
            self.active_alerts[alert.alert_id] = alert
            self.alert_history.append(alert)
            
            # 保持历史记录在合理大小
            if len(self.alert_history) > 1000:
                self.alert_history = self.alert_history[-500:]
        
        # 调用告警处理器
        for handler in self.alert_handlers:
            try:
                handler(alert)
            except Exception as e:
                logging.error(f"告警处理器执行失败: {e}")
        
        logging.warning(f"触发告警: {alert.message}")
    
    def _check_alert_resolution(self, metric: PerformanceMetric, rule_name: str):
        """检查告警解除"""
        # 查找相关的活跃告警
        for alert_id, alert in list(self.active_alerts.items()):
            if (alert.metric_name == metric.metric_name and 
                rule_name in alert_id):
                
                # 检查条件是否已经恢复正常
                rule = self.alert_rules[rule_name]
                if not self._evaluate_rule(metric.value, rule):
                    # 解除告警
                    self._resolve_alert(alert_id, metric.timestamp)
    
    def _resolve_alert(self, alert_id: str, resolve_timestamp: float):
        """解除告警"""
        with self._lock:
            if alert_id in self.active_alerts:
                alert = self.active_alerts.pop(alert_id)
                alert.resolved = True
                alert.resolve_timestamp = resolve_timestamp
                
                logging.info(f"告警已解除: {alert.message}")
    
    def add_alert_handler(self, handler: Callable[[PerformanceAlert], None]):
        """添加告警处理器"""
        self.alert_handlers.append(handler)
    
    def get_active_alerts(self) -> List[PerformanceAlert]:
        """获取活跃告警"""
        with self._lock:
            return list(self.active_alerts.values())
    
    def get_alert_history(self, hours: int = 24) -> List[PerformanceAlert]:
        """获取告警历史"""
        cutoff_time = time.time() - (hours * 3600)
        
        with self._lock:
            return [
                alert for alert in self.alert_history
                if alert.timestamp >= cutoff_time
            ]
    
    def _setup_default_rules(self):
        """设置默认告警规则"""
        # CPU使用率过高
        self.add_alert_rule(
            "high_cpu_usage",
            "cpu_usage_percent",
            threshold=80.0,
            comparison="greater",
            level="WARNING",
            message_template="CPU使用率过高: {value:.1f}% (阈值: {threshold}%)"
        )
        
        # 内存使用率过高
        self.add_alert_rule(
            "high_memory_usage",
            "memory_usage_percent",
            threshold=85.0,
            comparison="greater",
            level="WARNING",
            message_template="内存使用率过高: {value:.1f}% (阈值: {threshold}%)"
        )
        
        # 内存使用率极高
        self.add_alert_rule(
            "critical_memory_usage",
            "memory_usage_percent",
            threshold=95.0,
            comparison="greater",
            level="CRITICAL",
            message_template="内存使用率极高: {value:.1f}% (阈值: {threshold}%)"
        )


class PerformanceReporter:
    """性能报告生成器"""
    
    def __init__(self, metrics_collector: MetricsCollector, 
                 alert_manager: AlertManager):
        self.metrics_collector = metrics_collector
        self.alert_manager = alert_manager
    
    def generate_summary_report(self, time_range_minutes: int = 60) -> Dict:
        """生成性能摘要报告"""
        report = {
            'report_time': datetime.now().isoformat(),
            'time_range_minutes': time_range_minutes,
            'system_metrics': {},
            'alerts_summary': {},
            'recommendations': []
        }
        
        # 系统指标摘要
        system_metrics = ['cpu_usage_percent', 'memory_usage_percent', 'memory_usage_mb']
        
        for metric_name in system_metrics:
            history = self.metrics_collector.get_metric_history(metric_name, time_range_minutes)
            
            if history:
                values = [m.value for m in history]
                report['system_metrics'][metric_name] = {
                    'current': values[-1],
                    'average': sum(values) / len(values),
                    'max': max(values),
                    'min': min(values),
                    'unit': history[-1].unit
                }
        
        # 告警摘要
        active_alerts = self.alert_manager.get_active_alerts()
        alert_history = self.alert_manager.get_alert_history(hours=time_range_minutes//60)
        
        report['alerts_summary'] = {
            'active_count': len(active_alerts),
            'total_count_in_period': len(alert_history),
            'by_level': self._group_alerts_by_level(active_alerts + alert_history)
        }
        
        # 生成建议
        report['recommendations'] = self._generate_recommendations(report)
        
        return report
    
    def _group_alerts_by_level(self, alerts: List[PerformanceAlert]) -> Dict:
        """按级别分组告警"""
        grouped = defaultdict(int)
        for alert in alerts:
            grouped[alert.level] += 1
        return dict(grouped)
    
    def _generate_recommendations(self, report: Dict) -> List[str]:
        """生成性能优化建议"""
        recommendations = []
        
        # 检查系统指标
        system_metrics = report.get('system_metrics', {})
        
        # CPU相关建议
        cpu_data = system_metrics.get('cpu_usage_percent', {})
        if cpu_data.get('average', 0) > 70:
            recommendations.append("CPU使用率较高，建议优化计算密集型操作或增加并行度")
        
        # 内存相关建议
        memory_data = system_metrics.get('memory_usage_percent', {})
        if memory_data.get('average', 0) > 80:
            recommendations.append("内存使用率较高，建议优化缓存策略或增加内存")
        
        # 告警相关建议
        alerts_summary = report.get('alerts_summary', {})
        if alerts_summary.get('active_count', 0) > 0:
            recommendations.append("存在活跃告警，请及时处理")
        
        if not recommendations:
            recommendations.append("系统运行状态良好")
        
        return recommendations
    
    def export_report(self, report: Dict, file_path: Path):
        """导出报告到文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            logging.info(f"性能报告已导出: {file_path}")
            
        except Exception as e:
            logging.error(f"报告导出失败: {e}")


class PerformanceMonitor:
    """性能监控主类"""
    
    def __init__(self):
        self.config = get_performance_config()
        
        # 初始化组件
        self.metrics_collector = MetricsCollector(
            collection_interval=self.config.monitoring.metrics_collection_interval
        )
        
        self.alert_manager = AlertManager()
        
        self.reporter = PerformanceReporter(
            self.metrics_collector,
            self.alert_manager
        )
        
        # 监控状态
        self._monitoring = False
        self._monitor_thread = None
        
        # 设置默认告警处理器
        self._setup_default_handlers()
    
    def start_monitoring(self):
        """开始性能监控"""
        if self._monitoring:
            return
        
        self._monitoring = True
        
        # 启动指标收集
        self.metrics_collector.start_collection()
        
        # 启动监控线程
        self._monitor_thread = threading.Thread(
            target=self._monitoring_worker,
            daemon=True
        )
        self._monitor_thread.start()
        
        logging.info("性能监控已启动")
    
    def stop_monitoring(self):
        """停止性能监控"""
        self._monitoring = False
        
        # 停止指标收集
        self.metrics_collector.stop_collection()
        
        # 等待监控线程结束
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5.0)
        
        logging.info("性能监控已停止")
    
    def _monitoring_worker(self):
        """监控工作线程"""
        while self._monitoring:
            try:
                # 获取最近的指标并检查告警
                for metric_name in ['cpu_usage_percent', 'memory_usage_percent', 'memory_usage_mb']:
                    latest_metric = self.metrics_collector.get_latest_metric(metric_name)
                    if latest_metric:
                        self.alert_manager.check_metrics([latest_metric])
                
                time.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                logging.error(f"监控工作线程出错: {e}")
                time.sleep(10)
    
    def _setup_default_handlers(self):
        """设置默认告警处理器"""
        def console_handler(alert: PerformanceAlert):
            """控制台告警处理器"""
            level_icon = {
                'INFO': 'ℹ️',
                'WARNING': '⚠️',
                'ERROR': '❌',
                'CRITICAL': '🚨'
            }
            
            icon = level_icon.get(alert.level, '❓')
            timestamp_str = datetime.fromtimestamp(alert.timestamp).strftime('%H:%M:%S')
            
            print(f"{icon} [{alert.level}] {timestamp_str} - {alert.message}")
        
        self.alert_manager.add_alert_handler(console_handler)
    
    def add_custom_metric(self, metric_name: str, value: float, 
                         unit: str = "", category: str = "custom"):
        """添加自定义指标"""
        metric = PerformanceMetric(
            timestamp=time.time(),
            metric_name=metric_name,
            value=value,
            unit=unit,
            category=category
        )
        
        self.metrics_collector.add_metric(metric)
    
    def get_status_dashboard(self) -> Dict:
        """获取状态仪表板数据"""
        return {
            'monitoring_status': 'running' if self._monitoring else 'stopped',
            'recent_metrics': self._get_recent_metrics_summary(),
            'active_alerts': self.alert_manager.get_active_alerts(),
            'system_status': self._get_system_status()
        }
    
    def _get_recent_metrics_summary(self) -> Dict:
        """获取最近指标摘要"""
        summary = {}
        
        for metric_name in ['cpu_usage_percent', 'memory_usage_percent']:
            latest = self.metrics_collector.get_latest_metric(metric_name)
            if latest:
                summary[metric_name] = {
                    'value': latest.value,
                    'unit': latest.unit,
                    'timestamp': latest.timestamp
                }
        
        return summary
    
    def _get_system_status(self) -> str:
        """获取系统状态"""
        active_alerts = self.alert_manager.get_active_alerts()
        
        if not active_alerts:
            return "healthy"
        
        levels = [alert.level for alert in active_alerts]
        
        if "CRITICAL" in levels:
            return "critical"
        elif "ERROR" in levels:
            return "error"
        elif "WARNING" in levels:
            return "warning"
        else:
            return "info"


# 全局监控实例
_global_monitor = None
_monitor_lock = threading.Lock()


def get_performance_monitor() -> PerformanceMonitor:
    """获取全局性能监控实例"""
    global _global_monitor
    
    if _global_monitor is None:
        with _monitor_lock:
            if _global_monitor is None:
                _global_monitor = PerformanceMonitor()
    
    return _global_monitor


# 便捷函数
def start_monitoring():
    """启动性能监控"""
    monitor = get_performance_monitor()
    monitor.start_monitoring()


def stop_monitoring():
    """停止性能监控"""
    monitor = get_performance_monitor()
    monitor.stop_monitoring()


def get_performance_dashboard() -> Dict:
    """获取性能仪表板"""
    monitor = get_performance_monitor()
    return monitor.get_status_dashboard()


if __name__ == "__main__":
    # 性能监控演示
    print("🎯 性能监控和告警系统演示")
    print("=" * 50)
    
    monitor = get_performance_monitor()
    
    # 启动监控
    print("🚀 启动性能监控...")
    monitor.start_monitoring()
    
    # 等待一段时间收集指标
    print("📊 收集性能指标...")
    time.sleep(5)
    
    # 添加自定义指标
    monitor.add_custom_metric("cache_hit_rate", 0.85, "%", "factorzoo")
    monitor.add_custom_metric("avg_load_time", 250.0, "ms", "factorzoo")
    
    # 获取状态仪表板
    dashboard = monitor.get_status_dashboard()
    print(f"\n📈 当前状态: {dashboard['system_status']}")
    print(f"监控状态: {dashboard['monitoring_status']}")
    
    if dashboard['recent_metrics']:
        print("\n📊 最近指标:")
        for name, data in dashboard['recent_metrics'].items():
            print(f"  {name}: {data['value']:.1f}{data['unit']}")
    
    if dashboard['active_alerts']:
        print(f"\n⚠️  活跃告警: {len(dashboard['active_alerts'])} 个")
        for alert in dashboard['active_alerts']:
            print(f"  - {alert.level}: {alert.message}")
    else:
        print("\n✅ 无活跃告警")
    
    # 生成性能报告
    print("\n📋 生成性能报告...")
    report = monitor.reporter.generate_summary_report(time_range_minutes=30)
    
    print(f"报告时间: {report['report_time']}")
    if report['recommendations']:
        print("💡 优化建议:")
        for rec in report['recommendations']:
            print(f"  - {rec}")
    
    # 停止监控
    print("\n🔚 停止性能监控...")
    monitor.stop_monitoring()
    
    print("\n✨ 性能监控演示完成!")
    print("💡 实际使用中，监控会持续运行并提供实时告警") 