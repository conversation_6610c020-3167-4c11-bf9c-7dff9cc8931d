# L2相关性筛选脚本使用说明

## 📋 功能概述

`l2_corr_filter.py` 是基于原库L2Corr方法实现的因子筛选脚本，用于对GP挖掘结果进行进一步的相关性筛选。

### 主要功能
- ✅ 支持多批次因子的统一筛选处理
- ✅ 基于相关性阈值去除高度相关的冗余因子
- ✅ 集成运行时监控和统计记录功能
- ✅ 支持筛选后因子的自动入库到FactorZoo
- ✅ 支持因子值的持久化存储
- ✅ 采用简洁优雅的过程式编程风格，注重执行效率

## 🚀 使用方法

### 1. 直接运行（推荐）
```bash
cd XentZ/core/polyfactor
python l2_corr_filter.py
```

### 2. 命令行参数运行
```bash
# 指定相关性阈值
python l2_corr_filter.py --threshold 0.25

# 指定输入管道步骤
python l2_corr_filter.py --stage L0

# 组合参数
python l2_corr_filter.py --threshold 0.3 --stage L0
```

### 3. 程序化调用
```python
from core.polyfactor.l2_corr_filter import run_l2_corr_filter_pipeline

# 使用默认参数
results = run_l2_corr_filter_pipeline()

# 指定参数
results = run_l2_corr_filter_pipeline(
    corr_threshold=0.25,
    pipeline_step='L0'
)

# 指定特定批次
results = run_l2_corr_filter_pipeline(
    batch_ids=['GP_510050.SH_20241229_L0_abc123', 'GP_510300.SH_20241229_L0_def456']
)
```

## ⚙️ 配置参数

### 相关性阈值 (corr_threshold)
- **默认值**: 0.3 (来自cfg_mine.corr_thresh)
- **说明**: 两个因子间相关性超过此阈值时，保留与收益率相关性更强的因子
- **建议值**: 0.2-0.4之间

### 输入管道步骤 (pipeline_step)
- **默认值**: 'L0'
- **可选值**: 'L0', 'L1', 'L2', 'L3'
- **说明**: 指定从哪个管道步骤加载因子数据

## 📊 输出结果

### 1. 控制台输出
```
🚀 开始L2相关性筛选流程...
📋 将处理以下批次: 3 个
    1. GP_510050.SH_20241229_L0_abc123
    2. GP_510300.SH_20241229_L0_def456
    3. GP_000300.SH_20241229_L0_ghi789
🎯 相关性阈值: 0.3

==================================================
🎯 处理品种: 510050.SH
==================================================
✅ 510050.SH 筛选完成: 15 个因子保留

🎉 L2相关性筛选完成!
📊 处理结果:
    品种数量: 3 个
    因子总数: 42 个
    平均每品种: 14.0 个因子
```

### 2. 文件输出
- **筛选结果**: `reports/l2_corr_filtered_factors_YYYYMMDD_UID.csv`
- **运行统计**: `reports/l2_corr_filter_stats_YYYYMMDD_UID.json`
- **相关性矩阵**: `reports/l2_corr_matrix_SYMBOL_YYYYMMDD.csv`

### 3. 数据库存储
- **FactorZoo**: 筛选后的因子自动入库，批次ID格式为 `L2_CORR_SYMBOL_YYYYMMDD_UID`
- **FactorValueManager**: 因子值持久化存储，支持后续快速加载

## 🔧 核心算法

### L2相关性筛选逻辑
1. **加载多批次数据**: 从指定批次加载因子数据和基础数据
2. **构建完整数据集**: 合并因子值和收益率数据
3. **计算相关性矩阵**: 计算因子间的Pearson相关系数
4. **筛选高相关因子**: 对于相关性超过阈值的因子对，保留与收益率相关性更强的因子
5. **输出筛选结果**: 返回去重后的因子列表

### 关键特性
- **内存优化**: 分批处理大量因子，避免内存溢出
- **去重机制**: 基于表达式字符串去重，避免重复因子
- **监控集成**: 全程监控处理进度和性能指标
- **错误处理**: 完善的异常处理机制，确保流程稳定性

## 📈 性能优化

### 执行效率优化
- 使用numpy向量化计算相关性矩阵
- 仅计算上三角矩阵，避免重复计算
- 分品种并行处理（可扩展）
- 智能缓存机制

### 内存使用优化
- 分批加载大数据集
- 及时释放不需要的中间变量
- 使用生成器减少内存占用

## 🔍 故障排除

### 常见问题

1. **未找到可用批次**
   ```
   ❌ 未找到可用的L0批次，请先运行mine_core.py生成因子
   ```
   **解决方案**: 先运行 `mine_core.py` 生成L0因子数据

2. **因子数据为空**
   ```
   ⚠️ 品种 XXX 有效因子数量不足，跳过筛选
   ```
   **解决方案**: 检查输入批次的数据质量，确保包含有效的数值型因子

3. **持久化失败**
   ```
   ❌ 因子值持久化失败
   ```
   **解决方案**: 检查磁盘空间和权限，确保FactorValueManager配置正确

### 调试模式
在代码中设置日志级别为DEBUG可获得更详细的执行信息：
```python
l2_filter = L2CorrFilter()
l2_filter.set_log_level('DEBUG')
```

## 🔗 相关文件

- **主脚本**: `core/polyfactor/l2_corr_filter.py`
- **配置文件**: `config/settings.py` (cfg_mine部分)
- **原库参考**: `原库/polyfactorX/engine/miners/gplearn_miner.py`
- **因子管理**: `factorzoo/factor_value_manager.py`

## 📝 更新日志

### v1.0.0 (2024-12-29)
- ✅ 实现基础L2相关性筛选功能
- ✅ 支持多批次因子处理
- ✅ 集成FactorZoo入库功能
- ✅ 支持因子值持久化
- ✅ 添加运行时监控和统计
- ✅ 完善错误处理和日志记录
