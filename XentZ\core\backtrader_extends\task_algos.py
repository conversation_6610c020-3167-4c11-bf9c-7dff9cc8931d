import importlib
from dataclasses import dataclass, field
from typing import List
from loguru import logger
# 添加项目根目录到 Python 路径
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from core.task_base import Task
from core.backtrader_extends.algos import *

@dataclass
class TaskAlgo(Task):
    # ================== 轮动周期参数 ================== 
    algo_period: str = 'RunDaily'
    algo_period_days: int = 20

    # ================== 仓位分配，默认等权 ==================
    algo_weight: str = 'WeightEqually'
    algo_weight_fix: list = field(default_factory=list)  # 当WeightFix时需要
    algo_weight_target_vol: float = 0.07
    algo_weight_target_vol_exclude: list = field(default_factory=list)  # TargetVol时需要

    # ================== 规则信号 ========================
    rules_buy: List[str] = field(default_factory=list)
    at_least_buy: int = 1
    rules_sell: List[str] = field(default_factory=list)
    at_least_sell: int = 1

    # ================== 机器模型 ======================
    model_name: str = 'model.pk'

    # ================== 排序参数 ======================  
    order_by: str = 'order_by'
    topK: int = 1
    dropN: int = 0
    b_ascending: bool = False
    # =================================================
    def __str__(self):
        return self.name

    def _parse_period(self):
        module = importlib.import_module('core.backtrader_extends.algos')
        if self.algo_period == 'RunDays':
            algo_period = getattr(module, self.algo_period)(self.algo_period_days)
        else:
            if self.algo_period in ['RunWeekly', 'RunOnce', 'RunMonthly', 'RunQuarterly', 'RunYearly']:
                algo_period = getattr(module, self.algo_period)()
            else:
                algo_period = getattr(module, 'RunAlways')()
        return algo_period

    def _parse_weights(self):
        module = importlib.import_module('core.backtrader_extends.algos')
        if self.algo_weight == 'WeightEqually':
            return module.WeightEqually()
        if self.algo_weight == 'WeightFix':
            if len(self.symbols) != len(self.algo_weight_fix):
                logger.error('固定权重 != symbols数 ')
                return
            return module.WeightFix(self.algo_weight_fix)
        if self.algo_weight == 'WeightERC':
            return module.WeightERC()
        if self.algo_weight == 'TargetVol':
            return module.TargetVol(target_volatility=self.algo_weight_target_vol, exclude=self.algo_weight_target_vol_exclude)
        logger.error('{}不存在'.format(self.algo_weight))
        return None

    def get_algos(self):
        pass


@dataclass
class TaskAssetsAllocation(TaskAlgo):  # 资产配置模板
    def get_algos(self):
        return [
            self._parse_period(),
            SelectAll(),
            self._parse_weights(),
            Rebalance()
        ]

@dataclass
class TaskRolling(TaskAlgo):  # 轮动策略模板
    def get_algos(self):
        return [
            RunAlways(),
            V2SelectBySignal(rules_buy=self.rules_buy,
                           buy_at_least_count=self.at_least_buy,
                           rules_sell=self.rules_sell,
                           sell_at_least_count=self.at_least_sell,
                           b_before_rank = True
                           ),
            V2SelectTopK(factor_name=self.order_by, K=self.topK, drop_top_n=self.dropN,
                       b_ascending=self.b_ascending),
            self._parse_weights(),
            Rebalance()
        ]
'''
@dataclass
class TaskRolling_Model(TaskAlgo):  # 轮动策略模板
    def get_algos(self):
        return [
            RunAlways(),
            SelectBySignal(rules_buy=self.rules_buy,
                           buy_at_least_count=self.at_least_buy,
                           rules_sell=self.rules_sell,
                           sell_at_least_count=self.at_least_sell
                           ),
            SelectByModel(model_name=self.model_name,feature_names=self.fct_names),
            SelectTopK(factor_name=self.order_by, K=self.topK, drop_top_n=self.dropN,
                       b_ascending=self.b_ascending),
            self._parse_weights(),
            Rebalance()
        ]
'''
@dataclass
class TaskPickTime(TaskAlgo):  # 择时策略模板
    def get_algos(self):
        return [
            RunAlways(),
            V2SelectBySignal(rules_buy=self.rules_buy,
                           buy_at_least_count=self.at_least_buy,
                           rules_sell=self.rules_sell,
                           sell_at_least_count=self.at_least_sell,
                           b_before_rank = True
                           ),
            WeightEqually(),
            Rebalance()
        ]
 