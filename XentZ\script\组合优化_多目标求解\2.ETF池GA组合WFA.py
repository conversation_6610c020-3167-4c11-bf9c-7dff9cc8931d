import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import time
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from functools import partial
from multiprocessing import Pool
from deap import base, creator, tools, algorithms
import numba as nb
import matplotlib.pyplot as plt

from config import REPORTS_DIR

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

#  DEAP Base
if "FitnessMulti" not in dir(creator):
    creator.create("FitnessMulti", base.Fitness, weights=(1.0, -1.0))
    creator.create("Individual", list, fitness=creator.FitnessMulti)

''' ==================== 4.脚本核心参数集中配置 ==================== '''
ETF_SELECT_DIR = ["etf_select_wfa_20250514_1050_精",  # 这个目录
                  "etf_select_wfa_20250514_1055_中",
                  "etf_select_wfa_20250514_1100_粗"]
#  参数
PREF = 2  # 1-收益率优先; 2-夏普优先
MAX_DD_LIMIT = -0.05
# WEIGHT_MODE = 'ga' # ga, equal(此时GLOBAL_CAP无效)
WEIGHT_MODE = 'equal' # ga, equal(此时GLOBAL_CAP无效)
GLOBAL_CAP = 0.25 # 避免某个品种超过40% weight
TRAIN_DAYS = 90  # 2 年
STEP_DAYS = 10  # 3 周（约1个月）
OPT_FREQ = 10  # 每3个月优化一次
GRID_MIN_W = [0.00]
# GRID_MIN_W = [0.03,0.05,0.08]
# GRID_MIN_W = [i / 100 for i in range(0, 11)]
# EXCLUDE_TICKERS = {"SH", "IEF"}
ASSET_BOUNDS = {"SH518850": (0.10, 0.30),
                "VOO": (0.05, 0.25),
                "2823.HK": (0.05, 0.20)}

TRADING_DAYS = 252 # 一年多少天
POP_SIZE, NGEN = 800, 120 # GA
CX_PB, MUT_PB = 0.7, 0.2 # GA
STOP_LOSS = -0.90  # 固定止损点为-10%

TC_RATE = 0.002  # 0.02% 交易成本
MIN_TRADE = 0.01  # 调仓阈值；设 0.01 降换手

#  NUMBA JIT 函数
@nb.njit(cache=True, fastmath=True)
def metrics_jit(w, ret_mat):
    # 确保数组是连续的
    w = np.ascontiguousarray(w)
    ret_mat = np.ascontiguousarray(ret_mat)
    
    port = ret_mat @ w
    ar = port.mean() * TRADING_DAYS
    av = port.std() * np.sqrt(TRADING_DAYS)

    cum = np.cumprod(1.0 + port)

    peak = np.empty_like(cum)
    running = 0.0
    for i in range(cum.size):
        if cum[i] > running:
            running = cum[i]
        peak[i] = running

    mdd = ((cum - peak) / peak).min()
    shp = 0.0 if av == 0 else ar / av
    return ar, av, mdd, shp


@nb.njit(cache=True)
def enforce_cap_floor_jit(w, floors, caps):
    w = np.clip(w, floors, caps)
    
    # 设置最大迭代次数，避免无限循环
    max_iter = 50
    iter_count = 0
    
    while True:
        iter_count += 1
        if iter_count > max_iter:
            # 达到最大迭代次数，强制退出前再次应用上限约束
            w = w / np.sum(w)  # 先归一化
            w = np.clip(w, floors, caps)  # 再应用约束
            
            # 如果应用约束后总和不为1，进行一次最终调整
            if abs(w.sum() - 1.0) > 1e-8:
                # 找出可调整的权重
                if w.sum() > 1.0:
                    # 需要减少权重
                    room = w - floors
                    idx = room > 1e-8
                    if idx.any():
                        excess = w.sum() - 1.0
                        w[idx] -= excess * room[idx] / room[idx].sum()
                else:
                    # 需要增加权重
                    room = caps - w
                    idx = room > 1e-8
                    if idx.any():
                        deficit = 1.0 - w.sum()
                        w[idx] += deficit * room[idx] / room[idx].sum()
                
                # 最终再次应用约束并归一化
                w = np.clip(w, floors, caps)
            
            # 确保总和接近1
            w = w / np.sum(w)
            return w
        
        diff = w.sum() - 1.0
        if abs(diff) < 1e-8:
            break
        
        if diff > 0:
            room = w - floors
            idx = room > 1e-8
            if not idx.any():
                # 如果没有可用空间，尝试减小最大权重
                max_idx = np.argmax(w)
                w[max_idx] -= diff
            else:
                w[idx] -= diff * room[idx] / room[idx].sum()
        else:
            room = caps - w
            idx = room > 1e-8
            if not idx.any():
                # 如果没有可用空间，尝试增大最小权重
                min_idx = np.argmin(w)
                w[min_idx] += (-diff)
            else:
                w[idx] += (-diff) * room[idx] / room[idx].sum()
        w = np.clip(w, floors, caps)
    return w


# 约束包装
def enforce_cap_floor(w, tickers, floor):
    caps = np.array([ASSET_BOUNDS.get(t, (floor, GLOBAL_CAP))[1] for t in tickers])
    floors = np.array([ASSET_BOUNDS.get(t, (floor, GLOBAL_CAP))[0] for t in tickers])
    return enforce_cap_floor_jit(w.astype(np.float64), floors, caps)


def make_repair(floor, tickers):
    def repair(ind):
        w = np.asarray(ind, float)
        w = np.random.dirichlet(np.ones_like(w)) if w.sum() <= 0 else w / w.sum()
        ind[:] = enforce_cap_floor(w, tickers, floor).tolist()
        return ind

    return repair

#  GA 工具
def safe_dirichlet(n):
    w = np.random.dirichlet(np.ones(n))
    return w / w.sum()


def mutate(ind, indpb, repair):
    if np.random.random() < indpb:
        i, j = np.random.choice(len(ind), 2, False)
        d = np.random.uniform(-0.1, 0.1)
        ind[i] += d
        ind[j] -= d
    repair(ind)
    return ind,


def mate(ind1, ind2, alpha, repair):
    tools.cxBlend(ind1, ind2, alpha)
    repair(ind1)
    repair(ind2)
    return ind1, ind2


def evaluate_nsga(ind, ret_mat):
    ar, _, mdd, _ = metrics_jit(np.asarray(ind), ret_mat)
    return ar, abs(mdd)


#  优化
def optimize_weights(ret_train_df, tickers, pref):
    
    if WEIGHT_MODE == "equal":
        # 等权重模式
        n_assets = len(tickers)
        print(f"资产数量: {n_assets}, 使用等权重策略")
        return np.ones(n_assets) / n_assets    
    
    # GA优化模式
    ret_mat = ret_train_df.values
    # 检查数据 --- DEBUG
    if np.isnan(ret_mat).any() or np.isinf(ret_mat).any():
        print("警告: 收益率数据包含NaN或无穷大值!")
        # 替换NaN和无穷大值
        ret_mat = np.nan_to_num(ret_mat, nan=0.0, posinf=0.0, neginf=0.0)
    # 检查数据 --- DEBUG END
    best = None
    
    # 预先计算最大可能的floor值
    n_assets = len(tickers)
    max_possible_floor = 1.0 / n_assets
    print(f"资产数量: {n_assets}, 理论最大floor值: {max_possible_floor:.4f}")
    # 调整GRID_MIN_W，只测试有解的floor值
    valid_floors = [fl for fl in GRID_MIN_W if fl * n_assets <= 1.0]
    if not valid_floors:
        print("警告: 所有floor值都导致无解问题，使用最大可能的floor值")
        valid_floors = [max_possible_floor * 0.99]  # 稍微小于理论最大值
    
    print(f"将测试以下floor值: {[f'{fl:.2f}' for fl in valid_floors]}")
    
    # for fl in GRID_MIN_W:
    for fl in valid_floors:
        print(f"\n开始处理 floor={fl:.2f}")
        repair = make_repair(fl, tickers)
        n = len(tickers)
        tb = base.Toolbox()
        tb.register("attr_float", lambda: safe_dirichlet(n).tolist())
        tb.register("individual", tools.initIterate, creator.Individual, tb.attr_float)
        tb.register("population", tools.initRepeat, list, tb.individual)
        tb.register("mate", mate, alpha=0.5, repair=repair)
        tb.register("mutate", mutate, indpb=0.9, repair=repair)
        tb.register("select", tools.selNSGA2)
        tb.register("evaluate", partial(evaluate_nsga, ret_mat=ret_mat))

        with Pool(processes=os.cpu_count()-1) as pool:
            tb.register("map", pool.map)
            pop = tb.population(POP_SIZE)
            for ind in pop: # 关键修改: 让父代从开始就满足个体约束!
                repair(ind)
            pop, _ = algorithms.eaMuPlusLambda(pop, tb, POP_SIZE, POP_SIZE,
                                               cxpb=CX_PB, mutpb=MUT_PB,
                                               ngen=NGEN, verbose=False)
            
        front = tools.sortNondominated(pop, len(pop), True)[0]
        feasible = [ind for ind in front if metrics_jit(np.asarray(ind), ret_mat)[2] >= MAX_DD_LIMIT]
        if not feasible:
            feasible = [min(front, key=lambda ind: metrics_jit(np.asarray(ind), ret_mat)[2])]
        key = (lambda ind: metrics_jit(np.asarray(ind), ret_mat)[0]) if pref == 1 \
            else (lambda ind: metrics_jit(np.asarray(ind), ret_mat)[3])
        cand = max(feasible, key=key)
        if best is None or key(cand) > key(best):
            best = cand
        # # 添加调试信息
        # repaired_weights = repair(list(best))
        # if any(w > GLOBAL_CAP for w in repaired_weights):
        #     print(f"Warning: Weights exceed global cap after repair: {repaired_weights}")
        
        # 添加调试信息
        if any(w > GLOBAL_CAP for w in list(best)):
            print(f"Warning: Weights exceed global cap after repair: {list(best)}")

        print(f"   • floor={fl:.0%} … OK (Pareto {len(front)})")
    return np.asarray(best)

def main():
    for a_dir_str in ETF_SELECT_DIR:
        ''' ==================== WFA回测: GA求解每步最优weights ==================== '''
        # 构建完整路径
        etf_select_path = os.path.join(REPORTS_DIR, a_dir_str)
        # base_info = os.path.join(REPORTS_DIR, ETF_PIVOT_PATH, ETF_NAME_FILE) # etf名称码表
        
        if not os.path.exists(etf_select_path):
            print(f"错误: ETF选择目录 {etf_select_path} 不存在!")
            return

        # 获取所有日期分段
        date_segments = []
        for file in os.listdir(etf_select_path):
            if file.startswith("selected_etfs_") and file.endswith(".txt"):
                date_str = file.replace("selected_etfs_", "").replace(".txt", "")
                try:
                    date = datetime.strptime(date_str, "%Y%m%d")
                    date_segments.append((date, date_str))
                except ValueError:
                    print(f"警告: 无法解析日期 {date_str}")
        
        # 按日期排序
        date_segments.sort()
        
        if not date_segments:
            print("错误: 未找到有效的ETF选择分段!")
            return
        
        print(f"找到 {len(date_segments)} 个ETF选择分段:")
        for date, date_str in date_segments:
            print(f"  - {date.strftime('%Y-%m-%d')}")

        # 设置生成报告存放位置
        ts = datetime.now().strftime("%Y%m%d_%H%M")
        script_name = os.path.basename(__file__).split('.')[0]
        outdir = REPORTS_DIR.joinpath(f"{script_name}_{ts}")
        os.makedirs(outdir, exist_ok=True)

        # 保存配置信息
        with open(os.path.join(outdir, "config.txt"), "w", encoding="utf-8") as f:
            f.write(f"ETF_SELECT_DIR: {a_dir_str}\n")
            f.write(f"TRAIN_DAYS: {TRAIN_DAYS}\n")
            f.write(f"STEP_DAYS: {STEP_DAYS}\n")
            f.write(f"OPT_FREQ: {OPT_FREQ}\n")
            f.write(f"STOP_LOSS: {STOP_LOSS}\n")
            f.write(f"TC_RATE: {TC_RATE}\n")
            f.write(f"MIN_TRADE: {MIN_TRADE}\n")
            f.write(f"MAX_DD_LIMIT: {MAX_DD_LIMIT}\n")
            f.write(f"GLOBAL_CAP: {GLOBAL_CAP}\n")
        
        pref = PREF
        # if WEIGHT_MODE == "ga":   
        #     while True:
        #         sel = input("选择目标：1=收益优先  2=夏普优先 > ").strip()
        #         if sel in ("1", "2"):
        #             pref = int(sel)
        #             break
        # elif WEIGHT_MODE == "equal":
        #     print(f"使用等权重策略，无需选择优化目标")

        nav = [1.0]
        segment_data = []
        nav_dates = []  # 用于直接跟踪与nav对应的日期
        records = []
        steps_since_last_opt = 0  # 跟踪自上次优化以来的步数
        
        first_w = None
        first_port = None
        nav_in = None
        prev_w = None
        
        print("\n🔄 Walk‑forward 开始 ...")
        
        for i, (current_date, date_str) in enumerate(date_segments):
            print(f"\n[{current_date.date()}] 处理分段 {i+1}/{len(date_segments)}")
            
            # 读取当前分段的ETF列表
            etf_file = os.path.join(etf_select_path, f"selected_etfs_{date_str}.txt")
            tickers = []
            try:
                with open(etf_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            parts = line.split(',', 1)
                            ticker = parts[0].strip()
                            tickers.append(ticker)
                print(f"  读取了 {len(tickers)} 只ETF")
            except Exception as e:
                print(f"  读取ETF列表出错: {e}")
                continue
            
            if not tickers:
                print("  警告: 未找到有效的ETF!")
                continue
            
            # 读取当前分段的收益率数据
            returns_file = os.path.join(etf_select_path, f"etf_returns_{date_str}.csv")
            try:
                ret_all = pd.read_csv(returns_file, index_col=0, parse_dates=True)
                print(f"  读取了收益率数据，形状: {ret_all.shape}")
                print(f"  数据时间范围: {ret_all.index[0]} 至 {ret_all.index[-1]}")
            except Exception as e:
                print(f"  读取收益率数据出错: {e}")
                continue
            
            # 确保所有ETF都在收益率数据中
            missing_tickers = [t for t in tickers if t not in ret_all.columns]
            if missing_tickers:
                print(f"  警告: {len(missing_tickers)} 只ETF在收益率数据中缺失: {missing_tickers}")
                tickers = [t for t in tickers if t in ret_all.columns]
            
            # 找到当前日期在收益率数据中的位置
            date_idx = ret_all.index.get_indexer([current_date], method='nearest')[0]
            if date_idx < 0 or date_idx >= len(ret_all.index):
                print(f"  警告: 无法在收益率数据中找到当前日期 {current_date}")
                continue
            
            # 分割训练和测试数据
            train_start_idx = max(0, date_idx - TRAIN_DAYS)
            train = ret_all.iloc[train_start_idx:date_idx+1]
            
            # 如果有下一个分段，使用它作为测试数据
            if i < len(date_segments) - 1:
                next_date = date_segments[i+1][0]
                test_mask = (ret_all.index > current_date) & (ret_all.index <= next_date)
                test = ret_all[test_mask]
            else:
                # 最后一个分段，使用剩余数据作为测试
                test = ret_all[ret_all.index > current_date]
            
            if test.empty:
                print("  警告: 测试数据为空!")
                continue
            
            print(f"  训练数据: {train.shape}, 从 {train.index[0]} 到 {train.index[-1]}")
            print(f"  测试数据: {test.shape}, 从 {test.index[0]} 到 {test.index[-1]}")
            
            # 计算自上次优化以来的步数
            days_since_last_opt = steps_since_last_opt * STEP_DAYS
            
            # 只在指定频率进行优化
            if steps_since_last_opt == 0 or days_since_last_opt >= OPT_FREQ:
                print(f"  执行优化 (距上次优化: {days_since_last_opt}天) ...")
                w = optimize_weights(train[tickers], tickers, pref)
                
                if first_w is None:
                    first_w = w.copy()
                    first_train = train[tickers]
                    first_port = (first_train * first_w).sum(axis=1)
                    nav_in = (1 + first_port).cumprod()
                
                steps_since_last_opt = 0  # 重置计数器
                
                # 保存本次优化的权重
                opt_date = current_date.strftime('%Y%m%d')
                opt_weights_df = pd.DataFrame({"权重": w, "百分比": (w * 100).round(2)}, index=tickers)
                opt_weights_df.to_csv(os.path.join(outdir, f"weights_{opt_date}.csv"), encoding="utf-8-sig")
                
                # 读取ETF名称
                etf_names = {}
                try:
                    with open(etf_file, 'r', encoding='utf-8') as f:
                        for line in f:
                            line = line.strip()
                            if line and not line.startswith('#'):
                                if '#' in line:  # 处理格式: SH512010, # 医药ETF
                                    parts = line.split('#', 1)
                                    code = parts[0].strip().rstrip(',')
                                    name = parts[1].strip()
                                    etf_names[code] = name
                except Exception as e:
                    print(f"  读取ETF名称出错: {e}")
                
                # 绘制本次优化的权重图
                plt.figure(figsize=(12, 6))
                
                # 创建带名称的标签
                labels = []
                for tick in tickers:
                    if tick in etf_names:
                        labels.append(f"{tick}\n{etf_names[tick]}")
                    else:
                        labels.append(tick)
                
                # 绘制条形图
                plt.bar(range(len(tickers)), w)
                
                # 设置x轴标签
                plt.xticks(range(len(tickers)), labels, rotation=45, ha='right')
                
                # 在条形上方添加百分比标签
                for i, v in enumerate(w):
                    plt.text(i, v + 0.01, f"{v:.1%}", ha='center')
                
                plt.title(f"优化权重 ({opt_date})")
                plt.grid(axis='y', linestyle='--', alpha=.5)
                plt.tight_layout()
                plt.savefig(os.path.join(outdir, f"weights_bar_{opt_date}.png"), dpi=300)
                plt.close()
            else:
                print(f"  使用前一步权重 (距上次优化: {days_since_last_opt}天) ...")
                w = prev_w
            
            steps_since_last_opt += 1  # 增加步数计数
            
            # 如果是第一个分段，初始化prev_w
            if prev_w is None:
                prev_w = np.ones(len(tickers)) / len(tickers)
                prev_tickers = tickers.copy()  # 保存当前tickers作为prev_tickers

            # 计算调仓
            if len(tickers) != len(prev_tickers) or not all(t in prev_tickers for t in tickers):
                # ETF池发生变化，需要特殊处理
                print(f"  ETF池发生变化: 前一步 {len(prev_tickers)}只, 当前 {len(tickers)}只")
                
                # 创建当前ETF到前一步权重的映射
                prev_weights = {t: prev_w[i] for i, t in enumerate(prev_tickers)}
                
                # 计算新旧权重差异和换手率
                turnover = 0.0
                w_adj = w.copy()  # 使用新的目标权重
                
                # 计算需要卖出的ETF（在前一步存在但当前不存在）
                for t in prev_tickers:
                    if t not in tickers:
                        turnover += prev_weights[t]
                
                # 计算需要调整的ETF（在两步都存在）
                for i, t in enumerate(tickers):
                    if t in prev_tickers:
                        prev_idx = prev_tickers.index(t)
                        delta = abs(w[i] - prev_w[prev_idx])
                        if delta < MIN_TRADE:
                            # 变化小于阈值，保持原权重
                            w_adj[i] = prev_w[prev_idx]
                        else:
                            # 变化大于阈值，计入换手率
                            turnover += delta
                    else:
                        # 新增ETF，全部权重计入换手率
                        turnover += w[i]
            else:
                # ETF池没有变化，可以直接计算
                delta = np.abs(w - prev_w)
                mask = delta < MIN_TRADE
                delta[mask] = 0
                w_adj = prev_w.copy()
                w_adj[~mask] = w[~mask]
                turnover = delta.sum()  

            cost = turnover * TC_RATE

            # 更新前一步的权重和ETF列表，为下一次迭代做准备
            prev_w = w_adj.copy()
            prev_tickers = tickers.copy()
            
            test_dates = test.index.tolist()
            
            # 计算组合收益
            port = (test[tickers] * w_adj).sum(axis=1)
            port.iloc[0] -= cost
            nav_seg = (1 + port).cumprod() * nav[-1]
            if len(nav_seg) > 0:
                nav.extend(nav_seg.values[1:])
                nav_dates.extend(test_dates[1:])
                print(f"  分段{i+1}添加了{len(test_dates[1:])}个日期，对应{len(nav_seg.values[1:])}个净值点")
            # 重要：记录测试数据的实际日期
            test_dates = test.index.tolist()
            # 记录当前分段的日期和净值
            segment_data.append({
                'dates': test_dates,
                'nav_values': nav_seg.values,
                'start_date': current_date,
                'end_date': next_date if i < len(date_segments) - 1 else None
            })
            
            # 计算分段绩效
            ar_seg = port.mean() * TRADING_DAYS
            mdd_seg = ((nav_seg - nav_seg.cummax()) / nav_seg.cummax()).min()
            print(f"  → turnover {turnover:.1%} | OOS年化 {ar_seg:.2%} | MDD {mdd_seg:.2%}")
            records.append((current_date.date(), ar_seg, mdd_seg))
            
            # 检查止损条件
            current_nav = nav[-1]
            peak_nav = max(nav)
            drawdown = (current_nav - peak_nav) / peak_nav
            if drawdown <= STOP_LOSS:
                print(f"触发止损条件，当前净值 {current_nav:.4f}, 最大回撤 {drawdown:.2%}. 停止交易.")
                break
        
        # 在所有分段处理完成后，打印收集到的日期和净值数量
        print(f"  收集到{len(nav_dates)}个唯一日期，净值数组长度为{len(nav)-1}（不含初始值）")

        # 检查日期和净值长度是否匹配
        if len(nav_dates) != len(nav) - 1:
            print(f"警告：日期长度({len(nav_dates)})与净值长度({len(nav)-1})不匹配")
            # 如果不匹配，使用nav_dates作为基准
            print(f"  日期数量{'少于' if len(nav_dates) < len(nav) - 1 else '多于'}净值数量，使用直接记录的日期序列")
            
            # 调整日期和净值长度
            if len(nav_dates) < len(nav) - 1:
                # 日期少于净值，截断净值
                nav = nav[:len(nav_dates)+1]  # +1是因为包含初始值
            else:
                # 日期多于净值，截断日期
                nav_dates = nav_dates[:len(nav)-1]
            
            print(f"  调整后: 日期长度 = {len(nav_dates)}, 净值长度 = {len(nav)-1}")
            
            # 使用nav_dates和nav创建净值序列
            nav_s = pd.Series(nav[1:], index=nav_dates)  # 不包括初始值1.0
        else:
            # 如果匹配，直接使用nav_dates和nav创建净值序列
            nav_s = pd.Series(nav[1:], index=nav_dates) 

        # 创建净值序列 - 修复分段边界处理
        all_dates = []
        all_nav_values = [1.0]  # 初始值

        # 调试信息：打印每个分段的日期和净值数量
        for i, segment in enumerate(segment_data):
            print(f"分段{i+1}: {len(segment['dates'])}个日期, {len(segment['nav_values'])}个净值")
            
            # 打印每个分段的第一个和最后一个日期
            if len(segment['dates']) > 0:
                print(f"  第一个日期: {segment['dates'][0]}, 净值: {segment['nav_values'][0]:.4f}")
                print(f"  最后一个日期: {segment['dates'][-1]}, 净值: {segment['nav_values'][-1]:.4f}")
            
            # 检查分段内日期和净值数量是否匹配
            if len(segment['dates']) != len(segment['nav_values']):
                print(f"  警告：分段{i+1}内日期和净值数量不匹配！")

        # 构建完整序列，确保边界处理正确
        for i, segment in enumerate(segment_data):
            if i == 0:
                # 第一个分段：包括所有日期和净值
                all_dates.extend(segment['dates'])
                all_nav_values.extend(segment['nav_values'])
            else:
                # 后续分段：跳过第一个日期和净值（已包含在前一个分段）
                # 检查边界连接
                if len(segment['dates']) > 0 and len(all_dates) > 0:
                    # 检查当前分段的第一个日期是否与上一个分段的最后一个日期相同
                    if segment['dates'][0] == all_dates[-1]:
                        # 日期相同，跳过第一个日期和净值
                        all_dates.extend(segment['dates'][1:])
                        all_nav_values.extend(segment['nav_values'][1:])
                        print(f"  分段{i+1}：第一个日期与上一分段最后日期相同，已跳过")
                    else:
                        # 日期不同，添加所有日期
                        all_dates.extend(segment['dates'])
                        # 重要修改：如果添加所有日期，也要添加所有净值
                        # 之前的逻辑是添加所有日期但只添加从第二个开始的净值，这导致了不匹配
                        all_nav_values.extend(segment['nav_values'])
                        print(f"  分段{i+1}：第一个日期与上一分段最后日期不同，已全部添加")
        # 检查日期和净值长度是否匹配
        print(f"\n构建完成：日期长度 = {len(all_dates)}, 净值长度 = {len(all_nav_values)-1}")
        if len(all_dates) != len(all_nav_values) - 1:
            print(f"警告：日期长度({len(all_dates)})与净值长度({len(all_nav_values)-1})不匹配")
            
            # 查找可能的问题
            print("\n检查可能的问题...")
            
            # 检查日期是否唯一
            unique_dates = set(all_dates)
            if len(unique_dates) != len(all_dates):
                print(f"  发现重复日期！唯一日期数: {len(unique_dates)}, 总日期数: {len(all_dates)}")
                # 找出重复的日期
                date_counts = {}
                for date in all_dates:
                    date_counts[date] = date_counts.get(date, 0) + 1
                duplicates = [date for date, count in date_counts.items() if count > 1]
                print(f"  重复日期: {duplicates[:5]}... (共{len(duplicates)}个)")
            
            # 检查日期是否有序
            is_sorted = all(all_dates[i] <= all_dates[i+1] for i in range(len(all_dates)-1))
            if not is_sorted:
                print("  日期不是有序的！")
                # 找出不有序的位置
                for i in range(len(all_dates)-1):
                    if all_dates[i] > all_dates[i+1]:
                        print(f"  位置{i}: {all_dates[i]} > {all_dates[i+1]}")
            
            # 调整日期和净值长度
            print("\n调整日期和净值长度...")
            if len(all_dates) < len(all_nav_values) - 1:
                # 日期少于净值，截断净值
                print(f"  日期少于净值，截断净值数组")
                all_nav_values = all_nav_values[:len(all_dates)+1]  # +1是因为包含初始值
            else:
                # 日期多于净值，截断日期
                print(f"  日期多于净值，截断日期数组")
                all_dates = all_dates[:len(all_nav_values)-1]
            
            print(f"  调整后: 日期长度 = {len(all_dates)}, 净值长度 = {len(all_nav_values)-1}")

        # 确保日期唯一且有序
        all_dates_unique = []
        all_nav_values_unique = [all_nav_values[0]]  # 保留初始值

        seen_dates = set()
        for i, date in enumerate(all_dates):
            if date not in seen_dates:
                seen_dates.add(date)
                all_dates_unique.append(date)
                all_nav_values_unique.append(all_nav_values[i+1])  # +1是因为all_nav_values包含初始值

        print(f"去重后: 日期长度 = {len(all_dates_unique)}, 净值长度 = {len(all_nav_values_unique)-1}")

        # 比较两种方法构建的净值序列
        print("\n比较两种方法构建的净值序列:")
        print(f"方法1 (nav_dates): {len(nav_dates)}个日期, {len(nav)-1}个净值")
        print(f"方法2 (all_dates_unique): {len(all_dates_unique)}个日期, {len(all_nav_values_unique)-1}个净值")

        # 使用更可靠的方法创建最终的净值序列
        if len(nav_dates) == len(nav) - 1:
            print("使用方法1 (nav_dates)创建净值序列")
            nav_s = pd.Series(nav[1:], index=nav_dates)
        else:
            print("使用方法2 (all_dates_unique)创建净值序列")
            nav_s = pd.Series(all_nav_values_unique[1:], index=all_dates_unique)
        # 创建净值序列
        # nav_s = pd.Series(all_nav_values_unique[1:], index=all_dates_unique)  # 不包括初始值1.0
        ret_oos = nav_s.pct_change().dropna()
        ann_ret = ret_oos.mean() * TRADING_DAYS
        ann_vol = ret_oos.std() * np.sqrt(TRADING_DAYS)
        cum = (1 + ret_oos).cumprod()
        mdd = ((cum - cum.cummax()) / cum.cummax()).min()
        shp = ann_ret / ann_vol if ann_vol > 0 else 0
        
        print("\n============== 总结(样本外) ==============")
        print(f"年化 {ann_ret:.2%} | 波动 {ann_vol:.2%} | 最大回撤 {mdd:.2%} | Sharpe {shp:.2f}")
        
        # 保存完整净值数据
        nav_s.to_csv(os.path.join(outdir, "nav_series_out.csv"), encoding="utf-8-sig")
        nav_s.plot(figsize=(12, 6), title="净值曲线(OUT)")
        plt.grid()
        plt.tight_layout()
        plt.savefig(os.path.join(outdir, "net_curve_out.png"), dpi=300)
        plt.close()

        if nav_in is not None:
            # 保存样本内净值数据
            nav_in.to_csv(os.path.join(outdir, "nav_series_in.csv"), encoding="utf-8-sig")
            
            # 单独绘制样本内净值曲线
            plt.figure(figsize=(12, 6))
            nav_in.plot(title="净值曲线(IN)")
            plt.grid()
            plt.tight_layout()
            plt.savefig(os.path.join(outdir, "net_curve_in.png"), dpi=300)
            plt.close()
            
            # 绘制样本内和样本外的组合图
            plt.figure(figsize=(14, 7))
            # 调整样本外净值起点与样本内终点对齐
            scale_factor = nav_in.iloc[-1] / nav_s.iloc[0]
            nav_s_aligned = nav_s * scale_factor
            
            # 创建完整的净值序列
            full_dates = nav_in.index.tolist() + nav_s.index.tolist()
            full_values = nav_in.values.tolist() + (nav_s_aligned).values.tolist()
            nav_full = pd.Series(full_values, index=full_dates)
            
            # 绘制完整净值曲线
            nav_full.plot(label="完整净值曲线")
            plt.axvline(x=nav_in.index[-1], color='r', linestyle='--', label="样本内/样本外分界")
            plt.legend()
            plt.grid()
            plt.title("完整回测净值曲线")
            plt.tight_layout()
            plt.savefig(os.path.join(outdir, "net_curve_full.png"), dpi=300)
            plt.close()

        df_alloc = pd.DataFrame({"权重": prev_w, "百分比": (prev_w * 100).round(2)}, index=prev_tickers)
        df_alloc.to_csv(os.path.join(outdir, "weights_last.csv"), encoding="utf-8-sig")
        plt.figure(figsize=(10, 4))
        plt.bar(prev_tickers, prev_w)
        plt.xticks(rotation=45, ha='right')
        plt.title("最终训练窗口权重")
        plt.grid(axis='y', linestyle='--', alpha=.5)
        plt.tight_layout()
        plt.savefig(os.path.join(outdir, "weights_bar_last.png"), dpi=300)
        plt.close()

        with open(os.path.join(outdir, "walk_forward_metrics.txt"), "w", encoding="utf-8") as f:
            f.write(f"AnnualReturn {ann_ret:.2%}\nVol {ann_vol:.2%}\nMDD {mdd:.2%}\nSharpe {shp:.2f}\n")
            for d, a, m in records:
                f.write(f"{d}  AnnRet {a:.2%}  MDD {m:.2%}\n")

        print(f"\n📂 结果保存在 ./{outdir}/   DONE")
    
if __name__ == "__main__":
    main()