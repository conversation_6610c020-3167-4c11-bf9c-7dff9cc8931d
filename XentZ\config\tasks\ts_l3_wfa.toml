# L3阶段WFA动态稳健性检验任务配置
# 继承数据集配置，复用标的和时间范围设置
dynaconf_include = ["_datasets/ts_single_etf.toml"]

[task]
name = "L3_WFA_Dynamic_Validation"
desc = "L3阶段因子的Walk-Forward Analysis动态稳健性检验"

# ============================== WFA核心参数 ============================== #
[wfa]
# 滚动窗口参数 (经过优化的最佳配置)
training_window = 750                  # 训练窗口：750个交易日(约3年)
testing_window = 60                    # 测试窗口：60个交易日(约3个月)
step_size = 60                         # 步进大小：等于测试窗口，无重叠

# 算法参数
tanh_k = 5                             # S型曲线陡峭度参数 (平衡噪声过滤与信号响应)
hold_n = 1                             # 持有期：1个交易日
gap = 1                                # 时序验证间隔，避免信息泄露
min_periods = 50                       # 最小有效样本数
correlation_method = "spearman"        # 相关性计算方法：spearman更稳健

# ============================== 通过标准 ============================== #
[criteria]
# 主要标准 (必须全部满足)
min_sharpe = 0.5                       # 最小夏普比率
max_mdd = 0.30                         # 最大回撤上限
min_win_rate = 0.55                    # 最小胜率

# 补充标准 (用于筛选优质因子)
min_calmar = 0.8                       # 最小卡玛比率
max_volatility = 0.25                  # 最大年化波动率
min_skewness = -0.5                    # 最小偏度（避免极端负偏）
max_kurtosis = 5.0                     # 最大峰度
min_total_return = 0.0                 # 最小总收益率

# ============================== 因子查询配置 ============================== #
[factor_query]
source_pipeline_step = "L2"            # 从L2阶段筛选通过的因子
batch_limit = 10                       # 最多处理的批次数量
factor_limit_per_batch = 100           # 每批次最多处理的因子数量
status_filter = ["active"]             # 只处理活跃状态的因子
exclude_symbols = []                   # 排除的品种列表

# ============================== 输出配置 ============================== #
[output]
save_detailed_reports = true           # 是否保存详细报告
save_pnl_series = true                # 是否保存PnL序列
save_position_series = false          # 是否保存仓位序列（节省空间）
save_metrics_json = true              # 是否保存指标JSON
plot_format = "png"                   # 图表格式：png, svg, pdf
plot_dpi = 300                        # 图表分辨率
compress_results = false              # 是否压缩结果文件

# ============================== 性能优化配置 ============================== #
[performance]
parallel_processing = true            # 是否启用并行处理
max_workers = 4                       # 最大并行工作进程数
chunk_size = 50                       # 批处理块大小
memory_limit_gb = 8                   # 内存使用限制（GB）
cache_intermediate_results = true     # 是否缓存中间结果

# ============================== 日志配置 ============================== #
[logging]
level = "INFO"                        # 日志级别：DEBUG, INFO, WARNING, ERROR
save_logs = true                      # 是否保存日志文件
log_progress_interval = 10            # 进度日志间隔（处理多少个因子后输出一次）
detailed_timing = false               # 是否记录详细的计时信息

# ============================== 绩效分析配置 ============================== #
[analysis]
use_quantstats = true                 # 是否使用quantstats生成专业报告
use_empyrical = true                  # 是否使用empyrical计算精确指标
generate_custom_charts = true        # 是否生成自定义matplotlib图表
benchmark_symbol = ""                 # 基准标的代码，空表示不使用基准

[analysis.quantstats]
save_html_report = true              # 是否保存HTML完整报告
save_individual_charts = true       # 是否保存单独图表文件
chart_dpi = 300                     # 图表分辨率
include_benchmark = false           # 是否包含基准对比

[analysis.custom_charts]
include_risk_analysis = true        # 是否包含风险分析图表
include_rolling_metrics = true     # 是否包含滚动指标图表
include_distribution_analysis = true # 是否包含分布分析图表
