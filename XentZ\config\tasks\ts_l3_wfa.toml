# L3阶段WFA动态稳健性检验任务配置
# 继承数据集配置，复用标的和时间范围设置
dynaconf_include = ["_datasets/ts_single_etf.toml"]

[task]
name = "L3_WFA_Dynamic_Validation"
desc = "L3阶段因子的Walk-Forward Analysis动态稳健性检验"

# ============================== 工作流总控 ============================== #
[workflow]
mode = 'test'  # 'test' 或 'live'，控制下面所有参数集

# ============================== 按模式存放的参数仓库 ============================== #
[mode]
  [mode.test]
    [mode.test.wfa]
      training_window = 500      # 测试模式：较小窗口，快速验证
      testing_window = 60
      step_size = 30
    [mode.test.criteria]
      min_sharpe = 0.3          # 测试模式：较宽松标准
      max_mdd = 0.40
      
  [mode.live]
    [mode.live.wfa]
      training_window = 750      # 生产模式：标准窗口
      testing_window = 60
      step_size = 60
    [mode.live.criteria]
      min_sharpe = 0.5          # 生产模式：严格标准
      max_mdd = 0.30

# ============================== WFA核心参数 ============================== #
[wfa]
dynaconf_merge = true
# 共享的基础参数
tanh_k = 5                             # S型曲线陡峭度参数
hold_n = 1                             # 持有期：1个交易日
gap = 1                                # 时序验证间隔，避免信息泄露
min_periods = 50                       # 最小有效样本数
correlation_method = "spearman"        # 相关性计算方法：spearman更稳健
# 动态合并当前模式的专属参数
"@merge" = "@jinja {{ this.mode[this.workflow.mode].wfa }}"

# ============================== 通过标准 ============================== #
[criteria]
dynaconf_merge = true
# 共享的基础标准
min_win_rate = 0.55                    # 最小胜率
min_calmar = 0.8                       # 最小卡玛比率
max_volatility = 0.25                  # 最大年化波动率
min_skewness = -0.5                    # 最小偏度（避免极端负偏）
max_kurtosis = 5.0                     # 最大峰度
min_total_return = 0.0                 # 最小总收益率
# 动态合并当前模式的专属标准
"@merge" = "@jinja {{ this.mode[this.workflow.mode].criteria }}"

# ============================== 因子查询配置 ============================== #
[factor_query]
source_pipeline_step = "L2"            # 从L2阶段筛选通过的因子
batch_limit = 10                       # 最多处理的批次数量
factor_limit_per_batch = 100           # 每批次最多处理的因子数量
status_filter = ["active"]             # 只处理活跃状态的因子
exclude_symbols = []                   # 排除的品种列表

# ============================== 输出配置 ============================== #
[output]
save_detailed_reports = true           # 是否保存详细报告
save_pnl_series = true                # 是否保存PnL序列
save_position_series = false          # 是否保存仓位序列（节省空间）
save_metrics_json = true              # 是否保存指标JSON
plot_format = "png"                   # 图表格式：png, svg, pdf
plot_dpi = 300                        # 图表分辨率
compress_results = false              # 是否压缩结果文件

# ============================== 性能优化配置 ============================== #
[performance]
parallel_processing = true            # 是否启用并行处理
max_workers = 4                       # 最大并行工作进程数
chunk_size = 50                       # 批处理块大小
memory_limit_gb = 8                   # 内存使用限制（GB）
cache_intermediate_results = true     # 是否缓存中间结果

# ============================== 日志配置 ============================== #
[logging]
level = "INFO"                        # 日志级别：DEBUG, INFO, WARNING, ERROR
save_logs = true                      # 是否保存日志文件
log_progress_interval = 10            # 进度日志间隔（处理多少个因子后输出一次）
detailed_timing = false               # 是否记录详细的计时信息

# ============================== 开源库增强配置 ============================== #
[enhancement]
enable_tsfresh = false                # 是否启用tsfresh特征扩展
enable_tsfel = false                  # 是否启用TSFEL特征提取
enable_catch22 = false                # 是否启用catch22特征
enable_sktime = false                 # 是否启用sktime高级分析

[enhancement.tsfresh]
max_features = 100                    # tsfresh最大特征数量
significance_level = 0.05             # 统计显著性水平
n_jobs = -1                          # 并行处理进程数
chunk_size = 50                      # 批处理块大小

[enhancement.tsfel]
feature_domains = ["statistical", "temporal", "spectral"]  # 启用的特征域
sampling_frequency = 1                # 采样频率
window_size = 0                      # 窗口大小，0表示使用全序列

[enhancement.catch22]
catch24 = false                      # 是否使用catch24 (包含2个额外特征)
normalize = true                     # 是否标准化输入序列

[enhancement.sktime]
clustering_method = "kmeans"         # 聚类方法: kmeans, hierarchical
n_clusters = 5                       # 聚类数量
distance_metric = "dtw"              # 距离度量: dtw, euclidean
