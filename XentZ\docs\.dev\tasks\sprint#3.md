# 任务
借鉴./datafeed/dataloader.py的实现逻辑, 扩展HKUDataloader类
# 目标
1. 实现HKUDataloader类, 用于从hku数据文件中读取数据(读取工具沿用HKU的sm,kdata等已有的工具, 得到的数据的进一步的处理借鉴CSVDataloader的实现)
2. 实现HKUDataloader类的get_df方法, 用于从hku数据文件中读取数据, 并返回pandas.DataFrame对象
3. 实现HKUDataloader类的get_backtrader_df方法, 用于从hku数据文件中读取数据, 并返回backtrader.feeds.PandasData对象
4. 实现HKUDataloader类的get_col_df方法, 用于接收已有的df, 并返回pandas.DataFrame对象, 并支持透视图
5. 实现HKUDataloader类的get方法,用于从hku数据文件中读取数据, 并返回pandas df, 并支持透视图
# 要求
1. 代码风格符合 Python 规范
2. 提供完整的单元测试
3. 提供完整的文档 - 在本sprint下完善
4. 提供完整的示例代码 - 在dataloader.py main下
5. 提供完整的说明文档 - 常规代码说明
