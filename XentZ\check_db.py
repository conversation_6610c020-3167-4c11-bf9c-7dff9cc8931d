#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库状态
"""

import sqlite3
from pathlib import Path

def check_database():
    """检查数据库状态"""
    print("🔍 检查数据库状态...")
    
    db_path = Path("D:/myquant/FZoo/factorzoo.db")
    print(f"数据库路径: {db_path}")
    print(f"数据库文件存在: {db_path.exists()}")
    
    if not db_path.exists():
        print("❌ 数据库文件不存在")
        return False
    
    try:
        with sqlite3.connect(str(db_path)) as conn:
            cursor = conn.cursor()
            
            # 检查所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            table_names = [t[0] for t in tables]
            
            print(f"📊 数据库包含 {len(table_names)} 个表:")
            for table in sorted(table_names):
                print(f"   - {table}")
            
            # 特别检查factor_performance_logs表
            if 'factor_performance_logs' in table_names:
                print("✅ factor_performance_logs表存在")
                cursor.execute("PRAGMA table_info(factor_performance_logs)")
                columns = cursor.fetchall()
                print(f"   表包含 {len(columns)} 列")
                
                # 检查是否有数据
                cursor.execute("SELECT COUNT(*) FROM factor_performance_logs")
                count = cursor.fetchone()[0]
                print(f"   表包含 {count} 条记录")
            else:
                print("❌ factor_performance_logs表不存在")
                return False
            
            return True
                
    except Exception as e:
        print(f"❌ 数据库检查失败: {str(e)}")
        return False

if __name__ == "__main__":
    check_database() 