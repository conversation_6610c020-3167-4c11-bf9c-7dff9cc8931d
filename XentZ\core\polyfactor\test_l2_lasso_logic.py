''' 测试L2Lasso筛选逻辑的正确性 - 使用模拟数据 '''
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from core.polyfactor.l2_lasso_filter import apply_lasso_cv_filter_for_symbol

def create_test_data_with_features():
    """创建包含多个特征的测试数据"""
    print("📊 创建LassoCV测试数据...")
    
    # 生成时间序列
    dates = pd.date_range(start='2024-01-01', end='2024-06-01', freq='D')
    n_days = len(dates)
    
    # 生成基础价格数据
    np.random.seed(42)
    base_price = 100
    returns = np.random.normal(0, 0.02, n_days)
    prices = base_price * np.exp(np.cumsum(returns))
    
    base_data = pd.DataFrame({
        'open': prices * (1 + np.random.normal(0, 0.001, n_days)),
        'high': prices * (1 + np.abs(np.random.normal(0, 0.005, n_days))),
        'low': prices * (1 - np.abs(np.random.normal(0, 0.005, n_days))),
        'close': prices,
        'volume': np.random.lognormal(10, 0.5, n_days),
    }, index=dates)
    
    # 计算收益率
    base_data['ret'] = base_data['close'].pct_change().fillna(0)
    base_data['ret_open'] = base_data['open'].pct_change().fillna(0)
    
    # 创建多个不同的因子 - 确保有足够的特征进行LassoCV
    factor_data = pd.DataFrame(index=dates)
    
    # 1. 价格相关因子
    factor_data['factor_price_ma5'] = base_data['close'].rolling(5).mean()
    factor_data['factor_price_ma10'] = base_data['close'].rolling(10).mean()
    factor_data['factor_price_ma20'] = base_data['close'].rolling(20).mean()
    
    # 2. 成交量相关因子
    factor_data['factor_volume_ma5'] = base_data['volume'].rolling(5).mean()
    factor_data['factor_volume_ma10'] = base_data['volume'].rolling(10).mean()
    
    # 3. 价格波动因子
    factor_data['factor_price_std5'] = base_data['close'].rolling(5).std()
    factor_data['factor_price_std10'] = base_data['close'].rolling(10).std()
    
    # 4. 价格相对位置因子
    factor_data['factor_hl_ratio'] = (base_data['high'] - base_data['low']) / base_data['close']
    factor_data['factor_oc_ratio'] = (base_data['open'] - base_data['close']) / base_data['close']
    
    # 5. 与收益率相关的因子（应该被Lasso选中）
    factor_data['factor_good_predictor'] = base_data['ret'].shift(1) * 2 + np.random.normal(0, 0.1, n_days)
    
    # 6. 噪声因子（应该被Lasso过滤掉）
    factor_data['factor_noise_1'] = np.random.normal(0, 1, n_days)
    factor_data['factor_noise_2'] = np.random.normal(0, 1, n_days)
    factor_data['factor_noise_3'] = np.random.normal(0, 1, n_days)
    
    # 7. 高相关因子（部分应该被过滤）
    factor_data['factor_corr_1'] = factor_data['factor_price_ma5'] + np.random.normal(0, 0.01, n_days)
    factor_data['factor_corr_2'] = factor_data['factor_price_ma5'] * 1.01 + np.random.normal(0, 0.01, n_days)
    
    # 填充缺失值
    factor_data = factor_data.fillna(method='bfill').fillna(0)
    
    print(f"    基础数据: {base_data.shape}")
    print(f"    因子数据: {factor_data.shape}")
    print(f"    因子列表: {list(factor_data.columns)}")
    
    return base_data, factor_data

def test_lasso_cv_filtering_logic():
    """测试LassoCV筛选逻辑的正确性"""
    print("🧪 测试LassoCV筛选逻辑...")
    
    # 创建测试数据
    base_data, factor_data = create_test_data_with_features()
    
    # 准备数据字典
    data_dict = {
        'base_data': base_data,
        'factor_data': factor_data,
        'batch_ids': ['TEST_LASSO_BATCH_001']
    }
    
    # 测试不同参数的筛选效果
    test_configs = [
        {'split_perc': 0.7, 'cv_folds': 3, 'max_iter': 10000, 'tol': 1e-3},
        {'split_perc': 0.8, 'cv_folds': 5, 'max_iter': 50000, 'tol': 1e-4},
    ]
    
    print(f"\n🎯 测试不同LassoCV参数的筛选效果:")
    print(f"{'配置':<8} {'输入因子':<8} {'输出因子':<8} {'筛选率':<8} {'保留因子'}")
    print("-" * 80)
    
    for i, config in enumerate(test_configs, 1):
        selected_factors = apply_lasso_cv_filter_for_symbol(
            symbol='TEST_SYMBOL',
            data_dict=data_dict,
            **config
        )
        
        input_count = factor_data.shape[1]
        output_count = len(selected_factors)
        filter_rate = output_count / input_count * 100
        
        print(f"配置{i:<7} {input_count:<8} {output_count:<8} {filter_rate:<7.1f}% {selected_factors[:3]}...")
    
    # 详细分析第一个配置的结果
    print(f"\n🔍 详细分析配置1的筛选结果:")
    selected_factors_detail = apply_lasso_cv_filter_for_symbol(
        symbol='TEST_SYMBOL',
        data_dict=data_dict,
        **test_configs[0]
    )
    
    print(f"保留的因子:")
    for i, factor in enumerate(selected_factors_detail, 1):
        print(f"  {i}. {factor}")
    
    print(f"\n被筛选掉的因子:")
    all_factors = set(factor_data.columns)
    removed_factors = all_factors - set(selected_factors_detail)
    for i, factor in enumerate(removed_factors, 1):
        print(f"  {i}. {factor}")
    
    # 验证筛选逻辑的正确性
    print(f"\n✅ 筛选逻辑验证:")
    
    # 检查好的预测因子是否被保留
    good_predictors = [f for f in selected_factors_detail if 'good_predictor' in f]
    print(f"  好的预测因子: 保留{len(good_predictors)}个")
    if len(good_predictors) > 0:
        print(f"    ✅ 正确: 好的预测因子被保留")
    else:
        print(f"    ⚠️  注意: 好的预测因子未被保留")
    
    # 检查噪声因子是否被过滤
    noise_factors = [f for f in selected_factors_detail if 'noise' in f]
    print(f"  噪声因子: 保留{len(noise_factors)}个")
    if len(noise_factors) == 0:
        print(f"    ✅ 正确: 噪声因子被过滤")
    else:
        print(f"    ⚠️  注意: 部分噪声因子被保留")
    
    # 检查高相关因子是否被适当处理
    corr_factors = [f for f in selected_factors_detail if 'corr' in f]
    print(f"  高相关因子: 保留{len(corr_factors)}个")
    
    return len(selected_factors_detail) > 0 and len(removed_factors) > 0

def test_edge_cases():
    """测试边界情况"""
    print(f"\n🧪 测试边界情况...")
    
    # 测试1: 空数据
    empty_data_dict = {
        'base_data': pd.DataFrame(),
        'factor_data': pd.DataFrame(),
        'batch_ids': []
    }
    
    result_empty = apply_lasso_cv_filter_for_symbol('TEST', empty_data_dict)
    print(f"  空数据测试: {len(result_empty)} 个因子 {'✅' if len(result_empty) == 0 else '❌'}")
    
    # 测试2: 单个因子
    dates = pd.date_range('2024-01-01', periods=100, freq='D')
    single_base = pd.DataFrame({
        'close': np.random.randn(100).cumsum() + 100,
        'ret': np.random.randn(100) * 0.02
    }, index=dates)
    
    single_factor = pd.DataFrame({
        'single_factor': np.random.randn(100)
    }, index=dates)
    
    single_data_dict = {
        'base_data': single_base,
        'factor_data': single_factor,
        'batch_ids': ['TEST']
    }
    
    result_single = apply_lasso_cv_filter_for_symbol('TEST', single_data_dict)
    print(f"  单因子测试: {len(result_single)} 个因子 {'✅' if len(result_single) == 0 else '❌'}")
    
    # 测试3: 少量样本
    short_dates = pd.date_range('2024-01-01', periods=20, freq='D')
    short_base = pd.DataFrame({
        'close': np.random.randn(20).cumsum() + 100,
        'ret': np.random.randn(20) * 0.02
    }, index=short_dates)
    
    short_factors = pd.DataFrame({
        'factor_1': np.random.randn(20),
        'factor_2': np.random.randn(20),
        'factor_3': np.random.randn(20)
    }, index=short_dates)
    
    short_data_dict = {
        'base_data': short_base,
        'factor_data': short_factors,
        'batch_ids': ['TEST']
    }
    
    result_short = apply_lasso_cv_filter_for_symbol('TEST', short_data_dict, split_perc=0.7, cv_folds=3)
    print(f"  少量样本测试: {len(result_short)} 个因子 {'✅' if len(result_short) >= 0 else '❌'}")
    
    return True

def run_lasso_logic_tests():
    """运行所有LassoCV逻辑测试"""
    print("🚀 开始L2Lasso筛选逻辑测试...")
    print("=" * 80)
    
    try:
        # 测试1: LassoCV筛选逻辑
        result1 = test_lasso_cv_filtering_logic()
        print(f"\n✅ LassoCV筛选逻辑测试: {'通过' if result1 else '失败'}")
        
        # 测试2: 边界情况
        result2 = test_edge_cases()
        print(f"✅ 边界情况测试: {'通过' if result2 else '失败'}")
        
        # 总结
        all_passed = result1 and result2
        print(f"\n{'='*80}")
        print(f"📊 测试总结: {'所有测试通过' if all_passed else '部分测试失败'}")
        
        if all_passed:
            print("🎉 L2Lasso筛选逻辑工作正常!")
            print("   - LassoCV能够正确识别和保留有效因子")
            print("   - 噪声因子能够被正确过滤")
            print("   - 边界情况处理正确")
        else:
            print("⚠️  发现问题，需要进一步检查")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_lasso_logic_tests()
    exit(0 if success else 1)
