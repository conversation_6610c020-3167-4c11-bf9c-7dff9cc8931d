import pandas as pd
import numpy as np
from numpy.lib.stride_tricks import sliding_window_view
import scipy.stats as stats
import talib
from sklearn.preprocessing import PowerTransformer
from sklearn.feature_selection import mutual_info_regression
from typing import List, Optional, Union
from tqdm import tqdm
from common.cls_base import BaseObj
from config.settings import get_missing_mode, get_norm_cols, get_outliers_params,get_norm_params
from datafeed.expr_funcs.expr_utils import calc_by_symbol, calc_df_by_symbol
from datafeed.expr_funcs.expr import calc_expr

class FeatPreprocessing(BaseObj):
    """特征预处理相关工具"""
    
    @staticmethod
    def calc_expr_df_all(df_all: pd.DataFrame, fields, names):
        ''' df_all: [datetime] OHLCV, symbol 
            fields: 特征表达式列表
            names:  特征名称列表
            return: [datetime] OHLCV, symbol
        '''
        if len(fields) == 0:
            return df_all
        
        # 确保DataFrame格式正确 - 保留原有验证逻辑
        if df_all.index.name != 'datetime':
            BaseObj.log('df的index必须为datetime', level='error')
            return None
        if 'symbol' not in df_all.columns:
            BaseObj.log('df必须包含symbol列', level='error')
            return None
        
        BaseObj.log(f"开始计算特征，总数: {len(fields)}")
        
        df = df_all.copy()
        count = 0
        df = df.set_index('symbol', append=True)

        for field, name in tqdm(zip(fields, names), desc="计算特征"):
            try:
                if len(field) <= 0:
                    continue
                se = calc_expr(df, field)
                count += 1
                df[name] = se
                if count >= 50:
                    df = df.copy()
            except Exception:
                import traceback
                BaseObj.log(f'{field}错误: {traceback.format_exc()}', level='error')
                continue
            
        # 还原索引和列名        
        df.reset_index(level='symbol', inplace=True)
            
        return df
    
    @staticmethod
    def normalize_df_all(df_all: pd.DataFrame, model_type: str = 'robust', **kwargs):
        """
        对load_df_all输出的DataFrame进行归一化处理
        
        Args:
            df_all: load_df_all的输出，单索引含symbol列
            base2keep: 不需要归一化的基础列，默认['open','close','high','low']
            base2norm: 需要归一化的基础列，默认['volume']
            feat2keep: 不需要归一化的特征前缀，默认'_ts_cons_'
            featlabel: 标签列前缀，默认'label_'
            model_type: 模型类型，如果为None则使用配置文件中的默认值
            **kwargs: 归一化参数，可覆盖配置文件中的设置
        
        Returns:
            pd.DataFrame: 归一化后的DataFrame
        """
        ''' ============================ 1. 集中处理参数配置 ============================= '''
        missing_mode = get_missing_mode()
        outliers_params = get_outliers_params()
        norm_cols = get_norm_cols()
        X_params = get_norm_params(model_type, 'X')
        label_params = get_norm_params(model_type, 'label')
        
        # 处理skip逻辑
        skip_X_norm = X_params == 'skip'
        skip_label_norm = label_params == 'skip'
        
        # 只有非skip的参数才与kwargs合并
        if not skip_X_norm:
            X_params = {**X_params, **kwargs}
        if not skip_label_norm:
            label_params = {**label_params, **kwargs}
 
        # 设置默认参数
        base2keep = norm_cols['base2keep'] or ['open', 'close', 'high', 'low'] 
        base2norm = norm_cols['base2norm'] or ['volume']
        feat2keep = norm_cols['feat2keep'] or '_ts_cons_'
        featlabel = norm_cols['featlabel'] or 'label_'
        
        BaseObj.log(f"norm_cols: {norm_cols}", level="DEBUG")
        BaseObj.log(f"X: {X_params}", level="DEBUG")
        BaseObj.log(f"label: {label_params}", level="DEBUG")
        
        # 深拷贝避免修改原始数据
        df_result = df_all.copy()
        
        # 区分不同类型的列
        all_columns = df_result.columns.tolist()
        symbol_col = 'symbol'  # 假设symbol列名为'symbol'
        
        # 分类所有列 - 修复分类逻辑，确保无重叠无遗漏
        cols_base2keep = [col for col in all_columns if col in base2keep or col == symbol_col]
        cols_base2norm = [col for col in all_columns if col in base2norm]
        
        # 剩余所有列中区分特征类型
        remaining_cols = [col for col in all_columns if col not in cols_base2keep + cols_base2norm]
        cols_feat2keep = [col for col in remaining_cols if col.startswith(feat2keep)]
        cols_feat2label = [col for col in remaining_cols if col.startswith(featlabel)]
        cols_feat_other = [col for col in remaining_cols if col not in cols_feat2keep + cols_feat2label]
        
        # Debug: 打印列分类结果
        BaseObj.log(f"列分类结果:", level="DEBUG")
        BaseObj.log(f"  基础保留列({len(cols_base2keep)}): {cols_base2keep}", level="DEBUG")
        BaseObj.log(f"  基础归一化列({len(cols_base2norm)}): {cols_base2norm}", level="DEBUG") 
        BaseObj.log(f"  保持不变特征列({len(cols_feat2keep)}): {cols_feat2keep[:5]}{'...' if len(cols_feat2keep)>5 else ''}", level="DEBUG")
        BaseObj.log(f"  标签列({len(cols_feat2label)}): {cols_feat2label[:5]}{'...' if len(cols_feat2label)>5 else ''}", level="DEBUG")
        BaseObj.log(f"  待筛选特征列({len(cols_feat_other)}): {len(cols_feat_other)}个", level="DEBUG")
        
        ''' ============================ 2. 特征预处理: 缺失/异常/归一化 ============================= '''
        # 1. 填充数值型列的缺失值
        numeric_cols = [col for col in all_columns if col != symbol_col and pd.api.types.is_numeric_dtype(df_result[col])]
        df_result = FeatPreprocessing.fill_missing(df_result, method=missing_mode, columns=numeric_cols)
       
        df_multi = df_result.set_index(symbol_col, append=True)
        
        # 2. 对非标签的数值列执行MAD裁剪（异常值处理）
        # 注意：只对需要归一化的基础列和待筛选特征列进行异常值处理
        mad_clip_cols = cols_base2norm + cols_feat_other
        if mad_clip_cols:
            # 先获取所有非标签特征列
            non_label_df = df_multi[mad_clip_cols] if len(mad_clip_cols) > 1 else df_multi[[mad_clip_cols[0]]]
            # 执行MAD裁剪
            clipped_df = FeatPreprocessing.mad_clip_df(non_label_df, **outliers_params)
            # 更新到原始数据
            df_multi[mad_clip_cols] = clipped_df
        
        # 2.5 执行方差筛选 - 仅对特征列进行筛选，保留基础列、标签列和保持不变的特征列
        removed_features = set()  # 初始化变量，确保作用域正确
        if cols_feat_other:
            # 提取特征列并执行方差筛选
            feat_df = df_multi[cols_feat_other]
            
            # 执行方差筛选
            filtered_df = FeatPreprocessing.variance_filter(feat_df, threshold=0.0005, axis=0)
            
            # 记录被筛选的特征数量
            removed_features = set(feat_df.columns) - set(filtered_df.columns)
            BaseObj.log(f"方差筛选: 移除了{len(removed_features)}个低方差特征", level="INFO")
            if removed_features:
                BaseObj.log(f"移除的特征: {list(removed_features)[:10]}{'...' if len(removed_features)>10 else ''}", level="DEBUG")
            
            # 更新特征列列表，仅保留筛选后的列
            cols_feat_other = filtered_df.columns.tolist()
            
            # 关键修复：从df_multi和df_result中都移除被筛选掉的特征列
            df_multi = df_multi.drop(columns=removed_features)
            df_result = df_result.drop(columns=removed_features)
        
        # 3. 归一化基础列 - 保留原列，新增_norm列
        for col in cols_base2norm:
            norm_col = f"{col}_norm"
            if skip_X_norm:
                # 跳过归一化，直接复制原值
                df_result[norm_col] = df_result[col]
            else:
                # 正常归一化处理
                norm_result = FeatPreprocessing.norm_df(
                    df_multi[[col]], 
                    **X_params
                ).droplevel(1).values
                df_result[norm_col] = norm_result
            
        # 4. 归一化标签列 - 直接覆盖原列值
        if cols_feat2label:
            if skip_label_norm:
                # 跳过归一化，保持原值
                pass
            else:
                # 正常归一化处理
                norm_result = FeatPreprocessing.norm_df(
                    df_multi[cols_feat2label], **label_params
                ).droplevel(1).values
                df_result[cols_feat2label] = norm_result
        
        # 5. 归一化其他特征列 - 直接覆盖原列值
        if cols_feat_other:
            if skip_X_norm:
                # 跳过归一化，保持原值
                pass
            else:
                # 正常归一化处理
                norm_result = FeatPreprocessing.norm_df(
                    df_multi[cols_feat_other], **X_params
                ).droplevel(1).values
                df_result[cols_feat_other] = norm_result
        
        # 注意：cols_feat2keep 不进行任何处理，保持原始值
        
        # 替换无穷大和NaN为0.0
        df_result = df_result.replace([np.inf, np.nan, -np.inf], 0.0)
        
        # 最终验证：确保没有误删应该保留的列
        expected_cols = (cols_base2keep + cols_base2norm + 
                        [f"{col}_norm" for col in cols_base2norm] + 
                        cols_feat2keep + cols_feat2label + cols_feat_other)
        final_cols = df_result.columns.tolist()
        
        BaseObj.log(f"最终输出验证:", level="DEBUG")
        BaseObj.log(f"  期望总列数: {len(expected_cols)} (基础{len(cols_base2keep + cols_base2norm)}+归一化{len(cols_base2norm)}+保持{len(cols_feat2keep)}+标签{len(cols_feat2label)}+筛选后特征{len(cols_feat_other)})", level="DEBUG")
        BaseObj.log(f"  实际总列数: {len(final_cols)}", level="DEBUG")
        BaseObj.log(f"归一化策略: X_norm={'跳过' if skip_X_norm else '执行'}, label_norm={'跳过' if skip_label_norm else '执行'}", level="DEBUG")

        return df_result
    
    @staticmethod
    @calc_df_by_symbol
    def fill_missing(df_all: pd.DataFrame, method: str = 'ffill', limit: Optional[int] = None,
                    value: Optional[float] = None, axis: int = 0,
                    columns: Optional[List[str]] = None, **kwargs) -> pd.DataFrame:
        """缺失值填充
        
        Args:
            df_all: 待处理的数据框, loader返回的df格式(index为datetime, columns为symbol)
            method: 填充方法，可选项:
                - 'ffill': 前向填充 (用前一个非缺失值填充)
                - 'bfill': 后向填充 (用后一个非缺失值填充)
                - 'mean': 均值填充 (用列均值填充)
                - 'median': 中位数填充 (用列中位数填充)
                - 'mode': 众数填充 (用列众数填充)
                - 'value': 固定值填充 (用指定的value填充)
                - 'interpolate': 插值填充 (线性插值)
            limit: 限制填充连续缺失值的数量，None表示无限制
            value: 当method='value'时使用的填充值
            axis: 填充方向，0表示按列填充，1表示按行填充
            columns: 需要填充的列名列表，默认为None表示处理所有列
            **kwargs: 其他参数，包含current_symbol（由装饰器传入）
            
        Returns:
            填充后的数据框,同df_all样式
        """
        if df_all is None or df_all.empty:
            return df_all
            
        result = df_all.copy()
        
        # 如果指定了列，则只处理这些列
        if columns is not None:
            cols_to_fill = [col for col in columns if col in result.columns]
            if not cols_to_fill:
                return result  # 如果没有匹配的列，直接返回原始数据框
        else:
            cols_to_fill = result.columns
        
        # 检查缺失值情况
        missing_counts = result[cols_to_fill].isna().sum()
        if missing_counts.sum() == 0:
            return result  # 如果没有缺失值，直接返回原始数据框
            
        # 根据不同方法填充缺失值
        if method in ['ffill', 'pad']:
            result[cols_to_fill] = result[cols_to_fill].fillna(method='ffill', axis=axis, limit=limit)
        
        elif method in ['bfill', 'backfill']:
            result[cols_to_fill] = result[cols_to_fill].fillna(method='bfill', axis=axis, limit=limit)
        
        elif method == 'mean':
            # 对每列分别计算均值并填充
            for col in cols_to_fill:
                if pd.api.types.is_numeric_dtype(result[col]):
                    mean_val = result[col].mean()
                    result[col] = result[col].fillna(mean_val)
        
        elif method == 'median':
            # 对每列分别计算中位数并填充
            for col in cols_to_fill:
                if pd.api.types.is_numeric_dtype(result[col]):
                    median_val = result[col].median()
                    result[col] = result[col].fillna(median_val)
        
        elif method == 'mode':
            # 对每列分别计算众数并填充
            for col in cols_to_fill:
                mode_vals = result[col].mode()
                if not mode_vals.empty:
                    result[col] = result[col].fillna(mode_vals[0])
        
        elif method == 'value' and value is not None:
            result[cols_to_fill] = result[cols_to_fill].fillna(value)
        
        elif method == 'interpolate':
            # 使用线性插值填充
            for col in cols_to_fill:
                if pd.api.types.is_numeric_dtype(result[col]):
                    result[col] = result[col].interpolate(method='linear', limit=limit, axis=axis)
        
        else:
            valid_methods = ['ffill', 'pad', 'bfill', 'backfill', 'mean', 'median', 'mode', 'value', 'interpolate']
            error_msg = f"不支持的填充方法: {method}，有效的方法包括: {', '.join(valid_methods)}"
            BaseObj.log(error_msg, level="ERROR")
            raise ValueError(error_msg)
        
        # 检查填充后是否还有缺失值
        remaining_missing = result[cols_to_fill].isna().sum().sum()
        if remaining_missing > 0:
            BaseObj.log(f"警告: 填充后仍有 {remaining_missing} 个缺失值", level="WARNING")
            
        return result

    @staticmethod
    @calc_by_symbol
    def mad_clip_col(x: pd.Series, *, k: int = 3, axis=0, **kwargs):
        import warnings
        if isinstance(x, pd.Series):
            x = x.values
        x = np.nan_to_num(x, nan=0.0, posinf=0.0, neginf=0.0)
        if axis not in [0, 1]:
            raise ValueError("轴方向(axis)必须是0或1")
        with warnings.catch_warnings():
            warnings.filterwarnings('error', category=RuntimeWarning)
            try:
                med = np.median(x, axis=axis)
                mad = np.median(np.abs(x - med), axis=axis)
            except RuntimeWarning:
                return x  # 返回原始数据，避免进一步计算
        mad = np.where(mad == 0, np.nan, mad)
        # 计算上下界
        with warnings.catch_warnings():
            warnings.filterwarnings('error', category=RuntimeWarning)
            try:
                if axis == 0:
                    lower_bound = med - k * 1.4826 * mad
                    upper_bound = med + k * mad
                elif axis == 1:
                    lower_bound = med[:, np.newaxis] - k * 1.4826 * mad[:, np.newaxis]
                    upper_bound = med[:, np.newaxis] + k * mad[:, np.newaxis]
            except RuntimeWarning:
                return x  # 返回原始数据，避免进一步计算

        lower_bound = np.clip(lower_bound, -np.inf, np.inf)
        upper_bound = np.clip(upper_bound, -np.inf, np.inf)

        return np.clip(x, lower_bound, upper_bound)
        
    @staticmethod
    @calc_df_by_symbol
    def mad_clip_df(df_all: pd.DataFrame, *, k: int = 3, axis=0, **kwargs):
        '''3倍mad截断法
        
        Args:
            df: 输入数据，可以是numpy数组或DataFrame
            k: MAD的倍数，默认为3
            axis: 计算方向，0表示按列计算，1表示按行计算
            
        Returns:
            处理后的数据，与输入类型相同
        '''
        import warnings
        
        # 保存原始输入类型
        is_dataframe = isinstance(df_all, pd.DataFrame)
        if is_dataframe:
            original_index = df_all.index
            original_columns = df_all.columns
            np_all = df_all.values
        
        # 处理缺失值和无穷值
        np_all = np.nan_to_num(np_all, nan=0.0, posinf=0.0, neginf=0.0)
        
        # 检查轴方向
        if axis not in [0, 1]:
            raise ValueError("轴方向(axis)必须是0或1")
        
        with warnings.catch_warnings():
            warnings.filterwarnings('error', category=RuntimeWarning)
            try:
                # 计算中位数
                med = np.median(np_all, axis=axis)
                
                # 计算绝对偏差
                if axis == 0:
                    # 按列计算，med形状为(n_cols,)
                    abs_dev = np.abs(np_all - med.reshape(1, -1))
                else:
                    # 按行计算，med形状为(n_rows,)
                    abs_dev = np.abs(np_all - med.reshape(-1, 1))
                
                # 计算MAD
                mad = np.median(abs_dev, axis=axis)
            except RuntimeWarning:
                # 出现警告时返回原始数据
                if is_dataframe:
                    return pd.DataFrame(np_all, index=original_index, columns=original_columns)
                return np_all
        
        # 处理MAD为0的情况
        mad = np.where(mad == 0, np.nan, mad)
        
        # 计算上下界
        with warnings.catch_warnings():
            warnings.filterwarnings('error', category=RuntimeWarning)
            try:
                if axis == 0:
                    # 按列计算，调整形状以便广播
                    lower_bound = med - k * 1.4826 * mad
                    upper_bound = med + k * mad
                    
                    # 调整形状以便广播到原始数组
                    lower_bound = lower_bound.reshape(1, -1)
                    upper_bound = upper_bound.reshape(1, -1)
                else:
                    # 按行计算，调整形状以便广播
                    lower_bound = med - k * 1.4826 * mad
                    upper_bound = med + k * mad
                    
                    # 调整形状以便广播到原始数组
                    lower_bound = lower_bound.reshape(-1, 1)
                    upper_bound = upper_bound.reshape(-1, 1)
            except RuntimeWarning:
                # 出现警告时返回原始数据
                if is_dataframe:
                    return pd.DataFrame(np_all, index=original_index, columns=original_columns)
                return np_all
        
        # 确保边界是有限的
        lower_bound = np.nan_to_num(lower_bound, nan=-np.inf)
        upper_bound = np.nan_to_num(upper_bound, nan=np.inf)
        
        # 应用截断
        clipped = np.clip(np_all, lower_bound, upper_bound)
        
        # 如果输入是DataFrame，则返回DataFrame
        if is_dataframe:
            return pd.DataFrame(clipped, index=original_index, columns=original_columns)
        
        return clipped

    @staticmethod
    @calc_df_by_symbol
    def transform_features(df: pd.DataFrame, *, 
                          skew_threshold: float = 2.0,
                          kurt_threshold: float = 7.0,
                          method: str = 'auto',
                          suffix: str = '_tfm',
                          **kwargs) -> pd.DataFrame:
        """
        对 DataFrame 中的每个特征列进行分布变换，改善数据的正态性。
        
        Args:
            df: 输入DataFrame
            skew_threshold: 偏度阈值，超过此值的特征将被变换，默认2.0
            kurt_threshold: 峰度阈值，超过此值的特征将被变换，默认7.0
            method: 变换方法，可选：
                - 'auto': 自动选择（正值用log1p，其他用yeo-johnson）
                - 'log1p': 对数变换（仅适用于正值）
                - 'yeo-johnson': Yeo-Johnson变换（适用于所有值）
                - 'box-cox': Box-Cox变换（仅适用于正值）
            suffix: 变换后特征的后缀，默认'_tfm'
            **kwargs: 其他参数，包含current_symbol（由装饰器传入）
            
        Returns:
            变换后的DataFrame，包含原始特征和变换后的特征
        """
        if df.empty:
            return df.copy()
            
        # 从kwargs中获取当前symbol信息
        current_symbol = kwargs.get('current_symbol', None)
        
        # 参数验证
        if skew_threshold <= 0 or kurt_threshold <= 0:
            raise ValueError("阈值必须为正数")
            
        valid_methods = ['auto', 'log1p', 'yeo-johnson', 'box-cox']
        if method not in valid_methods:
            raise ValueError(f"不支持的变换方法: {method}，有效方法: {valid_methods}")
        
        # 只处理数值列
        numeric_cols = [col for col in df.columns if pd.api.types.is_numeric_dtype(df[col])]
        if not numeric_cols:
            BaseObj.log(f"警告: 品种{current_symbol} 未找到数值列", level="WARNING")
            return df.copy()
        
        new_df = df.copy()
        transform_count = 0
        
        # 初始化变换器
        pt_yj = PowerTransformer(method='yeo-johnson', standardize=False)
        pt_bc = PowerTransformer(method='box-cox', standardize=False)
        
        for col in numeric_cols:
            try:
                # 获取数据并处理缺失值
                data = df[col].dropna()
                
                if len(data) < 3:
                    BaseObj.log(f"跳过列 {col}: 有效数据点不足3个", level="DEBUG")
                    continue
                
                # 计算偏度和峰度
                skew_val = data.skew()
                kurt_val = data.kurtosis()
                
                # 检查是否需要变换
                needs_transform = abs(skew_val) > skew_threshold or abs(kurt_val) > kurt_threshold
                
                if not needs_transform:
                    continue
                
                # 检查数据特征
                min_val = data.min()
                has_negatives = (data < 0).any()
                
                # 选择变换方法
                if method == 'auto':
                    if min_val > 0:  # 严格正值
                        chosen_method = 'log1p'
                    else:
                        chosen_method = 'yeo-johnson'
                else:
                    chosen_method = method
                
                # 验证方法与数据的兼容性
                if chosen_method in ['log1p', 'box-cox'] and has_negatives:
                    BaseObj.log(f"警告: 列 {col} 包含负值，无法使用 {chosen_method}，改用 yeo-johnson", 
                              level="WARNING")
                    chosen_method = 'yeo-johnson'
                
                # 执行变换
                if chosen_method == 'log1p':
                    transformed_data = np.log1p(df[col])
                        
                elif chosen_method == 'yeo-johnson':
                    transformed = pt_yj.fit_transform(df[[col]])
                    transformed_data = pd.Series(transformed.flatten(), index=df.index)
                    
                elif chosen_method == 'box-cox':
                    if min_val <= 0:
                        # Box-Cox需要正值，添加常数
                        shift = abs(min_val) + 1
                        shifted_data = df[col] + shift
                        transformed = pt_bc.fit_transform(shifted_data.values.reshape(-1, 1))
                        transformed_data = pd.Series(transformed.flatten(), index=df.index)
                        BaseObj.log(f"Box-Cox变换: 列 {col} 添加常数 {shift}", level="DEBUG")
                    else:
                        transformed = pt_bc.fit_transform(df[[col]])
                        transformed_data = pd.Series(transformed.flatten(), index=df.index)
                
                # 验证变换结果
                if transformed_data.isna().all():
                    BaseObj.log(f"警告: 列 {col} 变换后全为NaN，跳过", level="WARNING")
                    continue
                
                # 添加变换后的列
                new_col_name = f"{col}{suffix}"
                new_df[new_col_name] = transformed_data
                transform_count += 1
                
                # 记录变换信息
                new_skew = transformed_data.dropna().skew()
                new_kurt = transformed_data.dropna().kurtosis()
                BaseObj.log(f"变换 {col}: {chosen_method}, "
                          f"skew {skew_val:.2f}->{new_skew:.2f}, "
                          f"kurt {kurt_val:.2f}->{new_kurt:.2f}", level="DEBUG")
                
            except Exception as e:
                BaseObj.log(f"变换列 {col} 时出错: {e}", level="WARNING")
                continue
        
        BaseObj.log(f"品种{current_symbol}: 成功变换 {transform_count} 个特征", level="INFO")
        return new_df

    @staticmethod
    @calc_df_by_symbol
    def variance_filter(df_all: pd.DataFrame, *, threshold: float = 0.01, axis: int = 0, **kwargs):
        """按方差阈值筛选特征
        
        Args:
            df_all: 输入特征DataFrame
            threshold: 方差阈值，低于此值的特征将被移除
            axis: 计算方向，0表示沿列方向计算（移除低方差列），1表示沿行方向计算（移除低方差行）
        
        Returns:
            筛选后的DataFrame
        """
        # 直接使用原生pandas方差计算，不再反转axis
        variances = df_all.var(axis=axis, skipna=True)
        # 找出方差大于阈值的索引
        valid_indices = variances[variances > threshold].index
        
        # 根据axis决定过滤的维度
        if axis == 0:  # 列方向计算，过滤列
            BaseObj.log(f"方差筛选实际输出: {len(df_all.loc[:, valid_indices].columns)} 列", level="INFO")
            return df_all.loc[:, valid_indices]
        else:  # 行方向计算，过滤行
            BaseObj.log(f"方差筛选实际输出: {len(df_all.loc[valid_indices, :].columns)} 列", level="INFO")
            return df_all.loc[valid_indices, :]

    @staticmethod
    @calc_by_symbol
    def norm_se(
        x: pd.Series,
        *,
        window: int = 2000,
        n_clip: int = 6,
        logmode: int = 0,
        smooth: int = 0,
        demean: bool = True,
        algomode: int = 0,
        **kwargs
    ) -> np.ndarray:
        """
        滚动归一化函数, 支持NumPy数组和Pandas Series作为输入。
        参数: 
        - x: 输入数据,可以是NumPy数组或Pandas Series。
        - window: 滚动窗口大小,默认2000。
        - n_clip: 归一化后数据裁剪的最大绝对值,默认2。
        - logmode: 对数处理方法,0为不对数处理, 1为正数对数化,2为保留正负数的对数化。
        - smooth: 是否使用移动平均,1为使用,其他为不使用。
        - demean: 是否减去均值,默认False。
        - algomode: 归一化算法,0为z-score, 1为L2-norm, 2为MinMax, 3为鲁棒, 4为累计概率, 5为分位数norm
        返回: 归一化后的数组。
        """
        # FeaturePreprocessing.log(f"{window=}, {logmode=}, {algomode=}, {n_clip=}, {smooth=}, {demean=}", level="DEBUG")
        try:
            # 统一处理NaN和inf值 + 确保输入是一维数组
            if isinstance(x, np.ndarray):
                x = np.nan_to_num(x, nan=0.0, posinf=0.0, neginf=0.0)
                if x.ndim > 1 and x.shape[1] == 1:
                    x = x.ravel()  # 展平为一维数组
                elif x.ndim > 1:
                    raise ValueError("norm 函数只支持一维数组作为输入")
            elif isinstance(x, pd.Series):
                x = x.replace([np.inf, np.nan, -np.inf], 0.0).astype(np.float64)
                if x.shape != (len(x),):  # 检查形状是否为(n,)
                    x = x.values.reshape(-1)  # 如果不是，展平它
                else:
                    x = x.to_numpy()
            ''' ==== 直接处理非线性模型的 分位数norm 并直接返回结果 ==== '''
            if algomode == 5:
                n = len(x)
                result = np.zeros(n)
                if n <= window:
                    return result

                np.random.seed(42)
                random_seq = np.random.random(n)

                # 构造滑动窗口，shape 为 (n-window+1, window)
                total_windows = sliding_window_view(x, window_shape=window)
                # 对应 x[window:] 的窗口为 total_windows[0:n-window]
                windows = total_windows[: n - window]

                # 当前值对应 x[window:]
                current_vals = x[window:]

                # 统计窗口中比当前值小的个数和等于当前值的个数
                ranks = np.sum(windows < current_vals[:, None], axis=1)
                ties = np.sum(windows == current_vals[:, None], axis=1)

                rank_adj = ranks + np.where(ties > 0, random_seq[window:] * (ties + 1), random_seq[window:])
                quantiles = rank_adj / (window + 1)
                q = np.where(quantiles < 0.5, quantiles, 1.0 - quantiles)
                sign = np.where(quantiles < 0.5, -1.0, 1.0)

                r = np.sqrt(-2.0 * np.log(q))
                r2 = r * r
                r3 = r2 * r
                numerator = 2.515517 + 0.802853 * r + 0.010328 * r2
                denominator = 1.0 + 1.432788 * r + 0.189269 * r2 + 0.001308 * r3
                transformed = sign * (r - numerator / denominator)
                result[window:] = transformed
                
                return np.clip(result, -n_clip, n_clip)
            
            ''' ==== 处理线性模型的正常norm逻辑 ==== '''
            # 对数处理
            if logmode == 1:
                if np.any(x < 0):
                    raise ValueError("Log method 1 requires all values to be non-negative")
                x = np.log1p(x)
            elif logmode == 2:
                mean_abs_x = np.nanmean(np.abs(x))
                x = np.sign(x) * np.log1p(np.abs(x)) / np.log1p(mean_abs_x)
                x = np.nan_to_num(x)
                
            # 计算滚动统计量
            factor_mean = pd.Series(x).rolling(window=window, min_periods=1).mean().to_numpy()
            factor_std = pd.Series(x).rolling(window=window, min_periods=1).std().to_numpy()

            # 去均值操作
            if demean:
                x = x - factor_mean

            # 归一化处理
            with np.errstate(divide='ignore', invalid='ignore'):
                if algomode == 0:  # zscore -- 性能提升2倍
                    factor_value = x / factor_std
                    factor_value[factor_std == 0] = 0.0
                elif algomode == 1:  # divisor: L2norm --- 性能提升100倍, [-1,1] 可保号
                    # l2norm = pd.Series(x).rolling(window=window, min_periods=1).apply(
                    #     lambda x: np.sqrt(np.sum(x**2)), raw=True).to_numpy()
                    # factor_value = x / l2norm
                    # factor_value[l2norm == 0] = 0.0
                    squared_sum = np.lib.stride_tricks.sliding_window_view(x**2, window_shape=window).sum(axis=-1)
                    l2norm = np.sqrt(squared_sum)
                    prefix = np.sqrt(np.cumsum(x[:window]**2))
                    l2norm = np.concatenate((prefix, l2norm))[:len(x)]
                    factor_value = np.divide(x, l2norm, out=np.zeros_like(x), where=l2norm != 0)
                elif algomode == 2:  # MinMax -- 性能提升2倍 [-0.5,0.5]
                    min_val = pd.Series(x).rolling(window=window, min_periods=1).min().to_numpy()
                    max_val = pd.Series(x).rolling(window=window, min_periods=1).max().to_numpy()
                    factor_value = ((x - min_val) / (max_val - min_val)) - 0.5
                    factor_value[(max_val - min_val) == 0] = 0.0
                elif algomode == 3: # robust [-0.5,0.5]
                    # 🔧 修正：使用滚动统计量而非全局统计量，与norm_df保持一致
                    # 计算滚动中位数和分位数
                    median = pd.Series(x).rolling(window=window, min_periods=1).median().to_numpy()
                    q75 = pd.Series(x).rolling(window=window, min_periods=1).quantile(0.75).to_numpy()
                    q25 = pd.Series(x).rolling(window=window, min_periods=1).quantile(0.25).to_numpy()
                    iqr = q75 - q25
                    
                    # 第一步标准化：(x - median) / iqr
                    norm_x = np.divide(x - median, iqr, out=np.zeros_like(x), where=iqr != 0)
                    
                    # 计算标准化后数据的滚动分位数
                    low = pd.Series(norm_x).rolling(window=window, min_periods=1).quantile(0.01).to_numpy()
                    high = pd.Series(norm_x).rolling(window=window, min_periods=1).quantile(0.99).to_numpy()
                    denom = high - low
                    
                    # 第二步标准化并缩放到[-n_clip, n_clip]范围
                    factor_value = np.divide(norm_x - low, denom, out=np.zeros_like(norm_x), where=denom != 0) * 2 * n_clip - n_clip
                    factor_value[denom == 0] = 0.0
                elif algomode == 4: # 累计概率 [-0.5,0.5]
                    factor_value = x / factor_std
                    factor_value = stats.norm.cdf(factor_value)
                    factor_value = factor_value - 0.5
                else:
                    raise ValueError("Invalid value for algomode")

            # 剔除异常值
            factor_value[np.isnan(factor_value) | np.isinf(factor_value)] = 0.0
            factor_value = np.clip(factor_value, -n_clip, n_clip)
            
            # 移动平均处理
            if smooth == 1:
                # 确保数据类型为float64，因为TAlib要求double类型
                factor_value = talib.MA(factor_value.astype(np.float64), timeperiod=5, matype=1)
            
            factor_value = np.nan_to_num(factor_value)
                
            return factor_value.flatten()

        except Exception as e:
            print(f'排查: norm()计算异常: {e}')
            return x if isinstance(x, np.ndarray) else x.values

    @staticmethod
    @calc_df_by_symbol
    def norm_df( # 多列时性能比norm_se提升2.77倍!
        df: Union[pd.DataFrame, pd.Series],
        *,
        window: int = 2000,
        n_clip: int = 6,
        logmode: int = 0,
        smooth: int = 0,
        demean: bool = True,
        algomode: int = 0,
        **kwargs
    ) -> pd.DataFrame:
        
        # FeaturePreprocessing.log(f"{window=}, {logmode=}, {algomode=}, {n_clip=}, {smooth=}, {demean=}", level="DEBUG")
        arr = df.astype(np.float64).replace([np.inf, -np.inf], np.nan).fillna(0.0).values
        n_rows, n_cols = arr.shape
        result = np.zeros_like(arr)

        if algomode == 5:
            # 分位数归一化，每列单独处理（不可向量化）
            np.random.seed(42)
            random_seq = np.random.rand(n_rows)
            for i in range(n_cols):
                x = arr[:, i]
                r = np.zeros(n_rows)
                if n_rows <= window:
                    result[:, i] = r
                    continue
                total_windows = sliding_window_view(x, window_shape=window)
                windows = total_windows[: n_rows - window]
                current_vals = x[window:]
                ranks = np.sum(windows < current_vals[:, None], axis=1)
                ties = np.sum(windows == current_vals[:, None], axis=1)
                rank_adj = ranks + np.where(ties > 0, random_seq[window:] * (ties + 1), random_seq[window:])
                quantiles = rank_adj / (window + 1)
                q = np.where(quantiles < 0.5, quantiles, 1.0 - quantiles)
                sign = np.where(quantiles < 0.5, -1.0, 1.0)
                r = np.sqrt(-2.0 * np.log(q))
                r2 = r * r
                r3 = r2 * r
                numerator = 2.515517 + 0.802853 * r + 0.010328 * r2
                denominator = 1.0 + 1.432788 * r + 0.189269 * r2 + 0.001308 * r3
                transformed = sign * (r - numerator / denominator)
                out = np.zeros(n_rows)
                out[window:] = transformed
                result[:, i] = np.clip(out, -n_clip, n_clip)
                return pd.DataFrame(result, index=df.index, columns=df.columns)

        # log 处理
        if logmode == 1:
            if (arr < 0).any():
                raise ValueError("Logmode 1 requires non-negative values.")
            arr = np.log1p(arr)
        elif logmode == 2:
            mean_abs = np.nanmean(np.abs(arr), axis=0)
            arr = np.sign(arr) * np.log1p(np.abs(arr)) / np.log1p(mean_abs)
            arr = np.nan_to_num(arr)

        # rolling mean/std using pandas（逐列）
        roll_mean = pd.DataFrame(arr, index=df.index).rolling(window, min_periods=1).mean().values
        roll_std = pd.DataFrame(arr, index=df.index).rolling(window, min_periods=1).std().values

        if demean:
            arr = arr - roll_mean

        with np.errstate(divide='ignore', invalid='ignore'):
            if algomode == 0:  # z-score
                result = np.divide(arr, roll_std, out=np.zeros_like(arr), where=roll_std != 0)
            elif algomode == 1:  # L2 norm
                result = np.zeros_like(arr)
                for i in range(n_cols):
                    x = arr[:, i]
                    x2 = x**2
                    prefix = np.sqrt(np.cumsum(x2[:window]))
                    l2 = np.sqrt(sliding_window_view(x2, window).sum(axis=-1))
                    l2 = np.concatenate((prefix, l2))[:n_rows]
                    result[:, i] = np.divide(x, l2, out=np.zeros_like(x), where=l2 != 0)
            elif algomode == 2:  # MinMax
                roll_min = pd.DataFrame(arr, index=df.index).rolling(window, min_periods=1).min().values
                roll_max = pd.DataFrame(arr, index=df.index).rolling(window, min_periods=1).max().values
                denom = roll_max - roll_min
                result = np.divide(arr - roll_min, denom, out=np.zeros_like(arr), where=denom != 0) - 0.5
            elif algomode == 3:  # robust
                result = np.zeros_like(arr)
                for i in range(n_cols):
                    x = arr[:, i]
                    median = pd.Series(x).rolling(window, min_periods=1).median().values
                    q75 = pd.Series(x).rolling(window, min_periods=1).quantile(0.75).values
                    q25 = pd.Series(x).rolling(window, min_periods=1).quantile(0.25).values
                    iqr = q75 - q25
                    norm = np.divide(x - median, iqr, out=np.zeros_like(x), where=iqr != 0)
                    low = pd.Series(norm).rolling(window, min_periods=1).quantile(0.01).values
                    high = pd.Series(norm).rolling(window, min_periods=1).quantile(0.99).values
                    denom = high - low
                    res = np.divide(norm - low, denom, out=np.zeros_like(norm), where=denom != 0) * 2 * n_clip - n_clip
                    result[:, i] = res
            elif algomode == 4:  # 累计概率
                z = np.divide(arr, roll_std, out=np.zeros_like(arr), where=roll_std != 0)
                result = stats.norm.cdf(z) - 0.5
            else:
                raise ValueError(f"Unsupported algomode={algomode}")

        # 剔除异常值
        result = np.clip(np.nan_to_num(result), -n_clip, n_clip)

        # 平滑处理（逐列 MA）
        if smooth == 1:
            for i in range(n_cols):
                # 确保数据类型为float64，因为TAlib要求double类型
                result[:, i] = talib.MA(result[:, i].astype(np.float64), timeperiod=5, matype=1)

        return pd.DataFrame(result, index=df.index, columns=df.columns)
    
    @staticmethod
    def norm(
        x: np.ndarray, # 一维数组
        *,
        window: int = 2000,
        n_clip: int = 6,
        logmode: int = 0,
        smooth: int = 0,
        demean: bool = True,
        algomode: int = 0,
        **kwargs
    ) -> np.ndarray:
        """
        滚动归一化函数, 支持NumPy数组和Pandas Series作为输入。
        参数: 
        - x: 输入数据,可以是NumPy数组或Pandas Series。
        - window: 滚动窗口大小,默认2000。
        - n_clip: 归一化后数据裁剪的最大绝对值,默认2。
        - logmode: 对数处理方法,0为不对数处理, 1为正数对数化,2为保留正负数的对数化。
        - smooth: 是否使用移动平均,1为使用,其他为不使用。
        - demean: 是否减去均值,默认False。
        - algomode: 归一化算法,0为z-score, 1为L2-norm, 2为MinMax, 3为鲁棒, 4为累计概率, 5为分位数norm
        返回: 归一化后的数组。
        """
        # FeatPreprocessing.log(f"{window=}, {logmode=}, {algomode=}, {n_clip=}, {smooth=}, {demean=}", level="DEBUG")
        try:
            # 统一处理NaN和inf值 + 确保输入是一维数组
            if isinstance(x, np.ndarray):
                x = np.nan_to_num(x, nan=0.0, posinf=0.0, neginf=0.0)
                if x.ndim > 1 and x.shape[1] == 1:
                    x = x.ravel()  # 展平为一维数组
                elif x.ndim > 1:
                    raise ValueError("norm 函数只支持一维数组作为输入")

            ''' ==== 直接处理非线性模型的 分位数norm 并直接返回结果 ==== '''
            if algomode == 5:
                n = len(x)
                result = np.zeros(n)
                if n <= window:
                    return result

                np.random.seed(42)
                random_seq = np.random.random(n)

                # 构造滑动窗口，shape 为 (n-window+1, window)
                total_windows = sliding_window_view(x, window_shape=window)
                # 对应 x[window:] 的窗口为 total_windows[0:n-window]
                windows = total_windows[: n - window]

                # 当前值对应 x[window:]
                current_vals = x[window:]

                # 统计窗口中比当前值小的个数和等于当前值的个数
                ranks = np.sum(windows < current_vals[:, None], axis=1)
                ties = np.sum(windows == current_vals[:, None], axis=1)

                rank_adj = ranks + np.where(ties > 0, random_seq[window:] * (ties + 1), random_seq[window:])
                quantiles = rank_adj / (window + 1)
                q = np.where(quantiles < 0.5, quantiles, 1.0 - quantiles)
                sign = np.where(quantiles < 0.5, -1.0, 1.0)

                r = np.sqrt(-2.0 * np.log(q))
                r2 = r * r
                r3 = r2 * r
                numerator = 2.515517 + 0.802853 * r + 0.010328 * r2
                denominator = 1.0 + 1.432788 * r + 0.189269 * r2 + 0.001308 * r3
                transformed = sign * (r - numerator / denominator)
                result[window:] = transformed
                
                return np.clip(result, -n_clip, n_clip)
            
            ''' ==== 处理线性模型的正常norm逻辑 ==== '''
            # 对数处理
            if logmode == 1:
                if np.any(x < 0):
                    raise ValueError("Log method 1 requires all values to be non-negative")
                x = np.log1p(x)
            elif logmode == 2:
                mean_abs_x = np.nanmean(np.abs(x))
                x = np.sign(x) * np.log1p(np.abs(x)) / np.log1p(mean_abs_x)
                x = np.nan_to_num(x)
                
            # 计算滚动统计量
            factor_mean = pd.Series(x).rolling(window=window, min_periods=1).mean().to_numpy()
            factor_std = pd.Series(x).rolling(window=window, min_periods=1).std().to_numpy()

            # 去均值操作
            if demean:
                x = x - factor_mean

            # 归一化处理
            with np.errstate(divide='ignore', invalid='ignore'):
                if algomode == 0:  # zscore -- 性能提升2倍
                    factor_value = x / factor_std
                    factor_value[factor_std == 0] = 0.0
                elif algomode == 1:  # divisor: L2norm --- 性能提升100倍, [-1,1] 可保号
                    # l2norm = pd.Series(x).rolling(window=window, min_periods=1).apply(
                    #     lambda x: np.sqrt(np.sum(x**2)), raw=True).to_numpy()
                    # factor_value = x / l2norm
                    # factor_value[l2norm == 0] = 0.0
                    squared_sum = np.lib.stride_tricks.sliding_window_view(x**2, window_shape=window).sum(axis=-1)
                    l2norm = np.sqrt(squared_sum)
                    prefix = np.sqrt(np.cumsum(x[:window]**2))
                    l2norm = np.concatenate((prefix, l2norm))[:len(x)]
                    factor_value = np.divide(x, l2norm, out=np.zeros_like(x), where=l2norm != 0)
                elif algomode == 2:  # MinMax -- 性能提升2倍 [-0.5,0.5]
                    min_val = pd.Series(x).rolling(window=window, min_periods=1).min().to_numpy()
                    max_val = pd.Series(x).rolling(window=window, min_periods=1).max().to_numpy()
                    factor_value = ((x - min_val) / (max_val - min_val)) - 0.5
                    factor_value[(max_val - min_val) == 0] = 0.0
                elif algomode == 3: # robust [-0.5,0.5]
                    # 🔧 修正：使用滚动统计量而非全局统计量，与norm_df保持一致
                    # 计算滚动中位数和分位数
                    median = pd.Series(x).rolling(window=window, min_periods=1).median().to_numpy()
                    q75 = pd.Series(x).rolling(window=window, min_periods=1).quantile(0.75).to_numpy()
                    q25 = pd.Series(x).rolling(window=window, min_periods=1).quantile(0.25).to_numpy()
                    iqr = q75 - q25
                    
                    # 第一步标准化：(x - median) / iqr
                    norm_x = np.divide(x - median, iqr, out=np.zeros_like(x), where=iqr != 0)
                    
                    # 计算标准化后数据的滚动分位数
                    low = pd.Series(norm_x).rolling(window=window, min_periods=1).quantile(0.01).to_numpy()
                    high = pd.Series(norm_x).rolling(window=window, min_periods=1).quantile(0.99).to_numpy()
                    denom = high - low
                    
                    # 第二步标准化并缩放到[-n_clip, n_clip]范围
                    factor_value = np.divide(norm_x - low, denom, out=np.zeros_like(norm_x), where=denom != 0) * 2 * n_clip - n_clip
                    factor_value[denom == 0] = 0.0
                elif algomode == 4: # 累计概率 [-0.5,0.5]
                    factor_value = x / factor_std
                    factor_value = stats.norm.cdf(factor_value)
                    factor_value = factor_value - 0.5
                else:
                    raise ValueError("Invalid value for algomode")

            # 剔除异常值
            factor_value[np.isnan(factor_value) | np.isinf(factor_value)] = 0.0
            factor_value = np.clip(factor_value, -n_clip, n_clip)
            
            # 移动平均处理
            if smooth == 1:
                # 确保数据类型为float64，因为TAlib要求double类型
                factor_value = talib.MA(factor_value.astype(np.float64), timeperiod=5, matype=1)
            
            factor_value = np.nan_to_num(factor_value)
                
            return factor_value.flatten()

        except Exception as e:
            print(f'排查: norm()计算异常: {e}')
            return x if isinstance(x, np.ndarray) else x.values   
        
class FeatDistribution(BaseObj):
    """特征分布检验相关工具"""
    
    @staticmethod
    @calc_df_by_symbol
    def plot_curve(df: pd.DataFrame, *, 
                             benchmark_cols: List[str] = None,
                             show_cumulative: bool = False,
                             save_path: str,
                             grid_size: tuple = (6, 4),
                             figsize_per_subplot: tuple = (2.5, 2.0),
                             title_max_length: int = 50,
                             n_samples: int = 300,
                             **kwargs) -> None:
        """绘制多因子对比图表
        
        Args:
            df: 包含因子数据的DataFrame（单个symbol的数据）
            benchmark_cols: 基准对比列（如'ret', 'close'等），默认为['ret']
            show_cumulative: 是否显示基准列的累积值，默认False
            save_path: 图片保存根目录路径
            grid_size: 子图网格大小(行数, 列数)，默认(6, 4)
            figsize_per_subplot: 每个子图的尺寸，默认(2.5, 2.0)
            title_max_length: 标题最大长度，超过则截断，默认50
            n_samples: 绘图使用的样本数量，默认500（使用最后500个数据点）
            **kwargs: 其他绘图参数，包含current_symbol（由装饰器传入）
            
        Returns:
            None，直接保存图片到指定路径
            
        Raises:
            ValueError: 当参数不合法时
            FileNotFoundError: 当保存路径无效时
        """
        if df.empty:
            raise ValueError("输入DataFrame不能为空")
            
        if not save_path:
            raise ValueError("保存路径不能为空")
            
        # 从kwargs中获取当前symbol信息
        current_symbol = kwargs.get('current_symbol', None)
        
        # 数据采样：取最后n_samples个数据点
        if len(df) > n_samples and n_samples > 0:
            df_plot = df.tail(n_samples).copy()
        else:
            df_plot = df.copy()
        
        # 创建日期时间子目录
        import os
        from datetime import datetime
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M")
        sub_dir = os.path.join(save_path, f"features/{timestamp}_CURVES")
        os.makedirs(sub_dir, exist_ok=True)
        
        # 参数处理
        benchmark_cols = benchmark_cols or ['ret']
        rows, cols = grid_size
        plots_per_page = rows * cols
        symbol_suffix = f"_{current_symbol}" if current_symbol else ""
        
        # 数据预处理
        df_work = df_plot.copy()
        available_benchmark_cols = [col for col in benchmark_cols if col in df_work.columns]
        if not available_benchmark_cols:
            BaseObj.log(f"警告: 品种{current_symbol} 基准列 {benchmark_cols} 在数据中未找到", level="WARNING")
            return
            
        # 识别因子列（排除基准列）
        factor_cols = [col for col in df_work.columns if col not in benchmark_cols]
        
        # 检查特殊列是否存在
        special_cols = ['symbol', 'datetime', 'date', 'timestamp']
        existing_special = [col for col in special_cols if col in df_work.columns]
        if existing_special:
            BaseObj.log(f"  发现特殊列: {existing_special} (可能影响计数)", level="DEBUG")
        
        if not factor_cols:
            BaseObj.log(f"警告: 品种{current_symbol} 未找到因子列", level="WARNING")
            return
            
        # 构建累积数据
        cumulative_data = {}
        
        # 因子列累积（始终计算）
        for col in factor_cols:
            if pd.api.types.is_numeric_dtype(df_work[col]):
                cumulative_data[f'cum_{col}'] = df_work[col].cumsum()
                
        # 基准列累积（按需计算）
        benchmark_display_cols = available_benchmark_cols.copy()
        if show_cumulative:
            for col in available_benchmark_cols:
                if pd.api.types.is_numeric_dtype(df_work[col]):
                    cumulative_data[f'cum_{col}'] = df_work[col].cumsum()
                    benchmark_display_cols = [f'cum_{col}' for col in available_benchmark_cols]
        
        # 合并累积数据
        if cumulative_data:
            df_work = pd.concat([df_work, pd.DataFrame(cumulative_data, index=df_work.index)], axis=1)
        
        # 计算分页
        num_factors = len(factor_cols)
        num_pages = (num_factors + plots_per_page - 1) // plots_per_page
        
        # 绘图配置
        import matplotlib.pyplot as plt
        plt.style.use('default')  # 使用默认样式
        
        # 生成每页图表
        for page_idx in range(num_pages):
            start_idx = page_idx * plots_per_page
            end_idx = min(start_idx + plots_per_page, num_factors)
            page_factors = factor_cols[start_idx:end_idx]
            
            # 创建画布
            fig_width = cols * figsize_per_subplot[0]
            fig_height = rows * figsize_per_subplot[1]
            fig, axes = plt.subplots(rows, cols, figsize=(fig_width, fig_height), 
                                   sharex=True, squeeze=False)
            
            sample_indices = range(len(df_work))
            
            # 绘制每个因子
            for i, factor_col in enumerate(page_factors):
                row_idx = i // cols
                col_idx = i % cols
                ax_left = axes[row_idx, col_idx]
                ax_right = ax_left.twinx()
                
                # 左轴：因子值（不累积）
                factor_data = df_work[factor_col]
                ax_left.plot(sample_indices, factor_data, 'b-', linewidth=1, 
                           label=f'Feat', alpha=0.8)
                ax_left.set_ylabel('Feat Value', color='blue', fontsize=8, fontweight='bold')
                ax_left.tick_params(axis='y', colors='blue', labelsize=7)
                ax_left.grid(True, alpha=0.3)
                
                # 右轴：基准数据
                for j, bench_col in enumerate(benchmark_display_cols):
                    if bench_col in df_work.columns:
                        color = ['red', 'green', 'orange', 'purple'][j % 4]
                        ax_right.plot(sample_indices, df_work[bench_col], 
                                    color=color, linewidth=1, alpha=0.7,
                                    label=bench_col.replace('cum_', ''))
                
                ax_right.set_ylabel('Benchmark', color='red', fontsize=8, fontweight='bold')
                ax_right.tick_params(axis='y', colors='red', labelsize=7)
                
                # 图例
                ax_left.legend(loc='upper left', fontsize=6)
                ax_right.legend(loc='upper right', fontsize=6)
                
                # 标题处理
                display_title = FeatDistribution._truncate_title(factor_col, title_max_length)
                title = f'{start_idx + i + 1}: {display_title}'
                ax_left.set_title(title, fontsize=9, fontweight='bold', pad=10)
            
            # 隐藏多余的子图
            for i in range(len(page_factors), plots_per_page):
                row_idx = i // cols
                col_idx = i % cols
                if row_idx < rows:
                    axes[row_idx, col_idx].axis('off')
            
            # 设置公共标签和布局
            fig.text(0.5, 0.02, 'Sample Index', ha='center', fontsize=10, fontweight='bold')
            plt.tight_layout()
            plt.subplots_adjust(bottom=0.06)
            
            # 保存图片
            try:
                if num_pages > 1:
                    save_file = os.path.join(sub_dir, f"CURVE{symbol_suffix}_page{page_idx + 1}.png")
                else:
                    save_file = os.path.join(sub_dir, f"CURVE{symbol_suffix}.png")
                    
                plt.savefig(save_file, dpi=300, bbox_inches='tight', 
                          facecolor='white', edgecolor='none')
                BaseObj.log(f"图表已保存: {save_file}", level="INFO")
            except Exception as e:
                BaseObj.log(f"保存图表失败: {e}", level="ERROR")
                raise
            finally:
                plt.close(fig)
    
    @staticmethod
    @calc_df_by_symbol
    def plot_hist(df: pd.DataFrame, *, 
                  save_path: str,
                  grid_size: tuple = (6, 4),
                  figsize_per_subplot: tuple = (2.5, 3.0),
                  title_max_length: int = 50,
                  bins: int = 50,
                  show_density: bool = True,
                  show_boxplot: bool = True,
                  **kwargs) -> None:
        """绘制多因子分布直方图
        
        Args:
            df: 包含因子数据的DataFrame（单个symbol的数据）
            save_path: 图片保存根目录路径
            grid_size: 子图网格大小(行数, 列数)，默认(6, 4)
            figsize_per_subplot: 每个子图的尺寸，默认(2.5, 3.0)
            title_max_length: 标题最大长度，超过则截断，默认50
            bins: 直方图分箱数量，默认50
            show_density: 是否显示密度曲线，默认True
            show_boxplot: 是否显示箱型图，默认True
            **kwargs: 其他参数，包含current_symbol（由装饰器传入）
            
        Returns:
            None，直接保存图片到指定路径
            
        Raises:
            ValueError: 当参数不合法时
            FileNotFoundError: 当保存路径无效时
        """
        if df.empty:
            raise ValueError("输入DataFrame不能为空")
            
        if not save_path:
            raise ValueError("保存路径不能为空")
            
        # 从kwargs中获取当前symbol信息
        current_symbol = kwargs.get('current_symbol', None)
        
        # 创建日期时间子目录
        import os
        from datetime import datetime
        from scipy.stats import gaussian_kde, norm
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M")
        sub_dir = os.path.join(save_path, f"features/{timestamp}_HISTS")
        os.makedirs(sub_dir, exist_ok=True)
        
        # 参数处理
        rows, cols = grid_size
        plots_per_page = rows * cols
        symbol_suffix = f"_{current_symbol}" if current_symbol else ""
        
        # 数据预处理 - 只保留数值列
        numeric_cols = [col for col in df.columns if pd.api.types.is_numeric_dtype(df[col])]
        if not numeric_cols:
            BaseObj.log(f"警告: 品种{current_symbol} 未找到数值列", level="WARNING")
            return
            
        df_work = df[numeric_cols].copy()
        
        # 计算分页
        num_factors = len(numeric_cols)
        num_pages = (num_factors + plots_per_page - 1) // plots_per_page
        
        # 绘图配置
        import matplotlib.pyplot as plt
        plt.style.use('default')
        
        # 生成每页图表
        for page_idx in range(num_pages):
            start_idx = page_idx * plots_per_page
            end_idx = min(start_idx + plots_per_page, num_factors)
            page_factors = numeric_cols[start_idx:end_idx]
            
            # 创建画布
            fig_width = cols * figsize_per_subplot[0]
            fig_height = rows * figsize_per_subplot[1]
            fig, axes = plt.subplots(rows, cols, figsize=(fig_width, fig_height), 
                                   squeeze=False)
            
            # 绘制每个因子的分布
            for i, factor_col in enumerate(page_factors):
                row_idx = i // cols
                col_idx = i % cols
                ax_hist = axes[row_idx, col_idx]
                
                # 获取数据并处理缺失值
                data = df_work[factor_col].dropna()
                if len(data) == 0:
                    ax_hist.text(0.5, 0.5, 'No Data', ha='center', va='center', 
                               transform=ax_hist.transAxes, fontsize=12)
                    ax_hist.set_title(f'{start_idx + i + 1}: {factor_col}', fontsize=9)
                    continue
                
                # 计算统计量
                mean_val = data.mean()
                std_val = data.std()
                std_err = std_val / np.sqrt(len(data))
                skew_val = data.skew()
                kurt_val = data.kurtosis()
                
                # 计算95%置信区间
                conf_interval = norm.interval(0.95, loc=mean_val, scale=std_err) if std_err > 0 else (mean_val, mean_val)
                
                # 智能处理数据范围和bins
                data_range = data.max() - data.min()
                unique_values = len(data.unique())
                
                # 检查特殊情况
                if data_range == 0:
                    # 常数数据
                    ax_hist.text(0.5, 0.5, f'Constant Value: {mean_val:.6f}', 
                               ha='center', va='center', transform=ax_hist.transAxes, 
                               fontsize=10, fontweight='bold')
                    ax_hist.set_title(f'{start_idx + i + 1}: {factor_col}', fontsize=9)
                    continue
                
                # 智能计算bins数量
                if unique_values <= 10:
                    # 离散数据或值很少的数据
                    bins_to_use = unique_values
                    bin_method = 'auto'
                elif data_range < 1e-6:
                    # 数据范围极小
                    bins_to_use = min(20, unique_values)
                    bin_method = 'auto'
                else:
                    # 使用Freedman-Diaconis规则或固定值
                    q75, q25 = np.percentile(data, [75, 25])
                    iqr = q75 - q25
                    if iqr > 0:
                        bin_width = 2 * iqr / (len(data) ** (1/3))
                        bins_to_use = max(10, min(50, int(data_range / bin_width)))
                    else:
                        bins_to_use = min(20, unique_values)
                    bin_method = bins_to_use
                
                # 异常值检测和处理（用于显示范围）
                q1, q99 = np.percentile(data, [1, 99])
                data_for_range = data[(data >= q1) & (data <= q99)]
                
                if len(data_for_range) < len(data) * 0.5:
                    # 如果异常值太多，使用全部数据
                    data_display = data
                    outlier_info = ""
                else:
                    # 使用去除极端异常值后的数据来设置显示范围
                    data_display = data
                    outliers_count = len(data) - len(data_for_range)
                    outlier_info = f" ({outliers_count} outliers)" if outliers_count > 0 else ""
                
                # 绘制直方图
                try:
                    counts, bins, patches = ax_hist.hist(data_display, bins=bin_method, 
                                                       alpha=0.7, density=True, 
                                                       color='skyblue', 
                                                       edgecolor='black', linewidth=0.5)
                    
                    # 如果有去除的异常值，设置合理的x轴范围
                    if len(data_for_range) < len(data):
                        ax_hist.set_xlim(q1 - (q99-q1)*0.1, q99 + (q99-q1)*0.1)
                        
                except Exception as e:
                    # 如果直方图绘制失败，显示错误信息
                    ax_hist.text(0.5, 0.5, f'Hist Error: {str(e)[:30]}...', 
                               ha='center', va='center', transform=ax_hist.transAxes, 
                               fontsize=8)
                    ax_hist.set_title(f'{start_idx + i + 1}: {factor_col}', fontsize=9)
                    continue
                
                # 绘制密度曲线
                density_plotted = False
                if show_density and len(data) > 1 and data_range > 1e-10:
                    try:
                        density = gaussian_kde(data)
                        # 使用显示范围来绘制密度曲线
                        x_min, x_max = ax_hist.get_xlim()
                        x_range = np.linspace(x_min, x_max, 200)
                        density_values = density(x_range)
                        ax_hist.plot(x_range, density_values, 'r-', linewidth=2)
                        density_plotted = True
                    except Exception as e:
                        BaseObj.log(f"密度曲线计算失败 {factor_col}: {e}", level="DEBUG")
                
                # 添加均值线
                ax_hist.axvline(mean_val, color='red', linestyle='--', linewidth=2)
                
                # 绘制箱型图（改进版本 - 放在底部，不使用twinx）
                if show_boxplot:
                    # 获取当前y轴范围
                    y_min, y_max = ax_hist.get_ylim()
                    box_y_position = y_min + (y_max - y_min) * 0.05  # 放在底部5%的位置
                    box_height = (y_max - y_min) * 0.08  # 箱型图高度为总高度的8%
                    
                    # 在主轴上绘制箱型图
                    bp = ax_hist.boxplot(data, vert=False, widths=box_height,
                                       positions=[box_y_position],
                                       patch_artist=True, 
                                       boxprops=dict(facecolor='lightgreen', alpha=0.7, linewidth=1),
                                       medianprops=dict(color='darkgreen', linewidth=2),
                                       whiskerprops=dict(linewidth=1),
                                       capprops=dict(linewidth=1),
                                       flierprops=dict(marker='o', markersize=2, alpha=0.5))
                    
                    # 重新设置y轴范围以确保箱型图可见
                    ax_hist.set_ylim(y_min, y_max)
                
                # 添加统计信息文本（优化格式和位置）
                def format_number(val):
                    """智能格式化数字显示"""
                    if abs(val) >= 1000:
                        return f'{val:.0f}'
                    elif abs(val) >= 100:
                        return f'{val:.1f}'
                    elif abs(val) >= 10:
                        return f'{val:.2f}'
                    elif abs(val) >= 1:
                        return f'{val:.3f}'
                    elif abs(val) >= 0.01:
                        return f'{val:.4f}'
                    elif abs(val) == 0:
                        return '0'
                    else:
                        return f'{val:.2e}'
                
                # 左上角统计信息（紧凑格式）
                density_label = "Dens." if density_plotted else ""
                stats_text = f'N: {len(data):,}{outlier_info}\nμ: {format_number(mean_val)}\nσ: {format_number(std_val)}\nS: {skew_val:.2f} K: {kurt_val:.2f}'
                if isinstance(bin_method, int):
                    stats_text += f'\nBins: {bin_method}'
                if density_label:
                    stats_text += f'\n{density_label}'
                
                ax_hist.text(0.02, 0.95, stats_text, transform=ax_hist.transAxes, 
                           fontsize=7, verticalalignment='top', fontweight='bold',
                           bbox=dict(boxstyle="round,pad=0.2", facecolor="white", alpha=0.9))
                
                # 右上角置信区间（更紧凑）
                ci_text = f'95%CI:\n[{format_number(conf_interval[0])},\n {format_number(conf_interval[1])}]'
                ax_hist.text(0.98, 0.95, ci_text, transform=ax_hist.transAxes, 
                           fontsize=7, verticalalignment='top', horizontalalignment='right',
                           fontweight='bold', color='red',
                           bbox=dict(boxstyle="round,pad=0.2", facecolor="white", alpha=0.9))
                
                # 设置标题
                display_title = FeatDistribution._truncate_title(factor_col, title_max_length)
                title = f'{start_idx + i + 1}: {display_title}'
                ax_hist.set_title(title, fontsize=9, fontweight='bold', pad=10)
                
                # 设置坐标轴
                ax_hist.set_xlabel('Value', fontsize=8)
                ax_hist.set_ylabel('', fontsize=8)  # 去掉Y轴标签，节省空间
                ax_hist.tick_params(axis='both', labelsize=7)
                ax_hist.grid(True, alpha=0.3)
                
                # 格式化坐标轴数字显示，控制小数位数
                from matplotlib.ticker import FuncFormatter, MaxNLocator
                
                def format_axis(x, pos):
                    """格式化坐标轴数字显示"""
                    if abs(x) >= 1000:
                        return f'{x:.0f}'
                    elif abs(x) >= 100:
                        return f'{x:.1f}'
                    elif abs(x) >= 10:
                        return f'{x:.1f}'
                    elif abs(x) >= 1:
                        return f'{x:.2f}'
                    elif abs(x) >= 0.01:
                        return f'{x:.2f}'
                    elif abs(x) >= 0.0001:
                        return f'{x:.2f}'
                    elif abs(x) == 0:
                        return '0'
                    else:
                        return f'{x:.1e}'
                
                # 限制坐标轴刻度数量，避免过于拥挤
                ax_hist.xaxis.set_major_locator(MaxNLocator(nbins=5, prune='both'))
                ax_hist.yaxis.set_major_locator(MaxNLocator(nbins=4, prune='both'))
                ax_hist.xaxis.set_major_formatter(FuncFormatter(format_axis))
                ax_hist.yaxis.set_major_formatter(FuncFormatter(format_axis))
                
                # 图例已删除 - 信息已在统计框中体现
            
            # 隐藏多余的子图
            for i in range(len(page_factors), plots_per_page):
                row_idx = i // cols
                col_idx = i % cols
                if row_idx < rows:
                    axes[row_idx, col_idx].axis('off')
            
            # 调整布局
            plt.tight_layout()
            
            # 保存图片
            try:
                if num_pages > 1:
                    save_file = os.path.join(sub_dir, f"HIST{symbol_suffix}_page{page_idx + 1}.png")
                else:
                    save_file = os.path.join(sub_dir, f"HIST{symbol_suffix}.png")
                    
                plt.savefig(save_file, dpi=300, bbox_inches='tight', 
                          facecolor='white', edgecolor='none')
                BaseObj.log(f"直方图已保存: {save_file}", level="INFO")
            except Exception as e:
                BaseObj.log(f"保存直方图失败: {e}", level="ERROR")
                raise
            finally:
                plt.close(fig)
    
    @staticmethod
    def _truncate_title(title: str, max_length: int) -> str:
        """截断过长的标题
        
        Args:
            title: 原标题
            max_length: 最大长度
            
        Returns:
            截断后的标题
        """
        if len(title) <= max_length:
            return title
        
        # 计算前后保留的字符数
        prefix_len = (max_length - 3) // 2
        suffix_len = max_length - 3 - prefix_len
        
        return f"{title[:prefix_len]}...{title[-suffix_len:]}"

    @staticmethod
    @calc_df_by_symbol
    def plot_qq(df: pd.DataFrame, *, 
                save_path: str,
                grid_size: tuple = (6, 4),
                figsize_per_subplot: tuple = (2.5, 2.5),
                title_max_length: int = 50,
                distribution: str = 'norm',
                show_stats: bool = True,
                **kwargs) -> None:
        """绘制多因子QQ图（分位数-分位数图）
        
        用于检验数据是否符合理论分布，识别异常值，评估分布特征
        
        Args:
            df: 包含因子数据的DataFrame（单个symbol的数据）
            save_path: 图片保存根目录路径
            grid_size: 子图网格大小(行数, 列数)，默认(6, 4)
            figsize_per_subplot: 每个子图的尺寸，默认(2.5, 2.5)
            title_max_length: 标题最大长度，超过则截断，默认50
            distribution: 理论分布类型，默认'norm'（正态分布）
            show_stats: 是否显示统计信息，默认True
            **kwargs: 其他参数，包含current_symbol（由装饰器传入）
            
        Returns:
            None，直接保存图片到指定路径
            
        Raises:
            ValueError: 当参数不合法时
            FileNotFoundError: 当保存路径无效时
        """
        if df.empty:
            raise ValueError("输入DataFrame不能为空")
            
        if not save_path:
            raise ValueError("保存路径不能为空")
            
        # 从kwargs中获取当前symbol信息
        current_symbol = kwargs.get('current_symbol', None)
        
        # 创建日期时间子目录
        import os
        from datetime import datetime
        from scipy import stats
        import scipy.stats as scipy_stats
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M")
        sub_dir = os.path.join(save_path, f"features/{timestamp}_QQ")
        os.makedirs(sub_dir, exist_ok=True)
        
        # 参数处理
        rows, cols = grid_size
        plots_per_page = rows * cols
        symbol_suffix = f"_{current_symbol}" if current_symbol else ""
        
        # 数据预处理 - 只保留数值列
        numeric_cols = [col for col in df.columns if pd.api.types.is_numeric_dtype(df[col])]
        if not numeric_cols:
            BaseObj.log(f"警告: 品种{current_symbol} 未找到数值列", level="WARNING")
            return
            
        df_work = df[numeric_cols].copy()
        
        # 计算分页
        num_factors = len(numeric_cols)
        num_pages = (num_factors + plots_per_page - 1) // plots_per_page
        
        # 绘图配置
        import matplotlib.pyplot as plt
        plt.style.use('default')
        
        # 生成每页图表
        for page_idx in range(num_pages):
            start_idx = page_idx * plots_per_page
            end_idx = min(start_idx + plots_per_page, num_factors)
            page_factors = numeric_cols[start_idx:end_idx]
            
            # 创建画布
            fig_width = cols * figsize_per_subplot[0]
            fig_height = rows * figsize_per_subplot[1]
            fig, axes = plt.subplots(rows, cols, figsize=(fig_width, fig_height), 
                                   squeeze=False)
            
            # 绘制每个因子的QQ图
            for i, factor_col in enumerate(page_factors):
                row_idx = i // cols
                col_idx = i % cols
                ax = axes[row_idx, col_idx]
                
                # 获取数据并处理缺失值
                data = df_work[factor_col].dropna()
                if len(data) < 3:
                    ax.text(0.5, 0.5, 'Insufficient Data\n(need ≥3 points)', 
                           ha='center', va='center', transform=ax.transAxes, 
                           fontsize=10, fontweight='bold')
                    ax.set_title(f'{start_idx + i + 1}: {factor_col}', fontsize=9)
                    continue
                
                # 计算基本统计量
                mean_val = data.mean()
                std_val = data.std()
                skew_val = data.skew()
                kurt_val = data.kurtosis()
                
                try:
                    # 生成QQ图数据
                    if distribution == 'norm':
                        # 更严格的数据清理
                        clean_data = data.replace([np.inf, -np.inf], np.nan).dropna()
                        
                        if len(clean_data) < 3:
                            ax.text(0.5, 0.5, 'Insufficient Clean Data\n(need ≥3 valid points)', 
                                   ha='center', va='center', transform=ax.transAxes, 
                                   fontsize=10, fontweight='bold')
                            ax.set_title(f'{start_idx + i + 1}: {factor_col}', fontsize=9)
                            continue
                        
                        # 重新计算统计量（基于清理后的数据）
                        mean_val = clean_data.mean()
                        std_val = clean_data.std()
                        skew_val = clean_data.skew()
                        kurt_val = clean_data.kurtosis()
                        
                        # 数据采样：如果数据量太大，进行采样以提高显示效果
                        if len(clean_data) > 2000:
                            sample_size = 2000
                            # 对于极端偏态数据，使用更智能的采样策略
                            if abs(skew_val) > 3:  # 极端偏态
                                # 保留更多尾部数据点
                                sorted_data = np.sort(clean_data)
                                n_tail = int(sample_size * 0.2)  # 20%用于尾部
                                n_middle = sample_size - 2 * n_tail
                                
                                # 头部、中部、尾部分别采样
                                head_indices = np.linspace(0, len(sorted_data)//4, n_tail, dtype=int)
                                middle_indices = np.linspace(len(sorted_data)//4, 3*len(sorted_data)//4, n_middle, dtype=int)
                                tail_indices = np.linspace(3*len(sorted_data)//4, len(sorted_data)-1, n_tail, dtype=int)
                                
                                all_indices = np.concatenate([head_indices, middle_indices, tail_indices])
                                sampled_data = sorted_data[all_indices]
                            else:
                                # 常规分层采样
                                sorted_data = np.sort(clean_data)
                                indices = np.linspace(0, len(sorted_data)-1, sample_size, dtype=int)
                                sampled_data = sorted_data[indices]
                        else:
                            sampled_data = clean_data
                        
                        # 标准差检查
                        if std_val <= 1e-10:
                            ax.text(0.5, 0.5, f'Constant Value: {mean_val:.6f}', 
                                   ha='center', va='center', transform=ax.transAxes, 
                                   fontsize=10, fontweight='bold')
                            ax.set_title(f'{start_idx + i + 1}: {factor_col}', fontsize=9)
                            continue
                        
                        # 生成QQ图数据，使用采样后的数据
                        qq_result = stats.probplot(sampled_data, dist='norm', plot=None)
                        theoretical_quantiles = qq_result[0][0]
                        sample_quantiles = qq_result[0][1]
                        
                        # 确保数组长度一致
                        min_length = min(len(theoretical_quantiles), len(sample_quantiles))
                        theoretical_quantiles = theoretical_quantiles[:min_length]
                        sample_quantiles = sample_quantiles[:min_length]
                        
                        if len(theoretical_quantiles) == 0 or len(sample_quantiles) == 0:
                            ax.text(0.5, 0.5, 'QQ Plot Error:\nNo valid quantiles', 
                                   ha='center', va='center', transform=ax.transAxes, fontsize=10)
                            continue
                        
                        # 绘制QQ图 - 优化显示效果
                        ax.scatter(theoretical_quantiles, sample_quantiles, 
                                 alpha=0.7, s=12, color='steelblue', edgecolors='navy', linewidth=0.3)
                        
                        # 添加理论线（45度线）
                        line_min = min(theoretical_quantiles.min(), sample_quantiles.min())
                        line_max = max(theoretical_quantiles.max(), sample_quantiles.max())
                        ax.plot([line_min, line_max], [line_min, line_max], 'r-', linewidth=2.5, alpha=0.8)
                        
                        # 计算R²相关系数 - 修复计算问题
                        if len(theoretical_quantiles) > 1:
                            correlation = scipy_stats.pearsonr(theoretical_quantiles, sample_quantiles)[0]
                            r_squared = correlation ** 2 if not np.isnan(correlation) else 0.0
                            
                            # 添加拟合度评估
                            if r_squared >= 0.95:
                                fit_quality = "Excellent"
                            elif r_squared >= 0.90:
                                fit_quality = "Good"
                            elif r_squared >= 0.80:
                                fit_quality = "Fair"
                            else:
                                fit_quality = "Poor"
                        else:
                            r_squared = 0.0
                            fit_quality = "N/A"
                        
                        # 正态性检验
                        try:
                            if std_val <= 1e-10:
                                # 常数数据或方差极小，无法进行正态性检验
                                normality_p = np.nan
                                normality_test = 'Constant Data'
                            elif len(clean_data) >= 3 and len(clean_data) <= 5000:
                                # 使用Shapiro-Wilk检验
                                shapiro_stat, normality_p = stats.shapiro(clean_data)
                                normality_test = f'SW: {shapiro_stat:.3f}\np: {normality_p:.2e}'
                            elif len(clean_data) > 5000:
                                # 大样本使用Kolmogorov-Smirnov检验
                                standardized = (clean_data - mean_val) / std_val
                                ks_stat, normality_p = stats.kstest(standardized, 'norm')
                                normality_test = f'KS: {ks_stat:.3f}\np: {normality_p:.2e}'
                            else:
                                normality_p = np.nan  # 数据太少，无法检验
                                normality_test = 'Insufficient Data'
                        except Exception as e:
                            normality_p = np.nan
                            normality_test = 'Test Failed'
                        
                        # 更新数据为清理后的数据
                        data = clean_data
                        
                    else:
                        # 支持其他分布（暂时只实现正态分布）
                        ax.text(0.5, 0.5, f'Distribution "{distribution}"\nnot implemented', 
                               ha='center', va='center', transform=ax.transAxes, fontsize=10)
                        continue
                        
                except Exception as e:
                    ax.text(0.5, 0.5, f'QQ Error:\n{str(e)[:30]}...', 
                           ha='center', va='center', transform=ax.transAxes, fontsize=8)
                    ax.set_title(f'{start_idx + i + 1}: {factor_col}', fontsize=9)
                    continue
                
                # 添加统计信息
                if show_stats:
                    def format_number(val):
                        """智能格式化数字显示"""
                        if abs(val) >= 1000:
                            return f'{val:.0f}'
                        elif abs(val) >= 100:
                            return f'{val:.1f}'
                        elif abs(val) >= 10:
                            return f'{val:.2f}'
                        elif abs(val) >= 1:
                            return f'{val:.3f}'
                        elif abs(val) >= 0.01:
                            return f'{val:.3f}'
                        elif abs(val) == 0:
                            return '0'
                        else:
                            return f'{val:.2e}'
                    
                    # 左上角统计信息
                    stats_text = f'N: {len(data):,}\nμ: {format_number(mean_val)}\nσ: {format_number(std_val)}\nS: {skew_val:.2f} K: {kurt_val:.2f}'
                    
                    # 基于统计指标的变换建议（不再基于列名）
                    transform_suggestion = ""
                    if abs(skew_val) > 2 or kurt_val > 7:
                        if abs(skew_val) > 2:
                            transform_suggestion = "💡 consider Box-Cox/Yeo-Johnson"
                        elif kurt_val > 7:
                            transform_suggestion = "💡 consider outlier treatment"
                    
                    if transform_suggestion:
                        stats_text += f'\n{transform_suggestion}'
                    
                    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
                           fontsize=7, verticalalignment='top', fontweight='bold',
                           bbox=dict(boxstyle="round,pad=0.2", facecolor="white", alpha=0.9))
                    
                    # 右上角正态性检验结果 - 改善显示格式
                    if r_squared >= 0:
                        r2_text = f'R²: {r_squared:.3f} ({fit_quality})'
                    else:
                        r2_text = 'R²: N/A'
                    
                    # 根据拟合质量选择颜色
                    if fit_quality == "Excellent":
                        box_color = "lightgreen"
                        text_color = "darkgreen"
                    elif fit_quality == "Good":
                        box_color = "lightyellow"
                        text_color = "darkorange"
                    elif fit_quality == "Fair":
                        box_color = "lightcoral"
                        text_color = "darkred"
                    else:
                        box_color = "lightgray"
                        text_color = "black"
                    
                    test_text = f'{r2_text}\n{normality_test}'
                    ax.text(0.98, 0.98, test_text, transform=ax.transAxes, 
                           fontsize=7, verticalalignment='top', horizontalalignment='right',
                           fontweight='bold', color=text_color,
                           bbox=dict(boxstyle="round,pad=0.2", facecolor=box_color, alpha=0.9))
                
                # 设置坐标轴标签和标题
                ax.set_xlabel('Theoretical Quantiles', fontsize=8)
                ax.set_ylabel('Sample Quantiles', fontsize=8)
                
                # 设置标题（不再包含变换建议）
                display_title = FeatDistribution._truncate_title(factor_col, title_max_length)
                title = f'{start_idx + i + 1}: {display_title}'
                ax.set_title(title, fontsize=9, fontweight='bold', pad=10)
                
                # 网格和刻度
                ax.grid(True, alpha=0.3)
                ax.tick_params(axis='both', labelsize=7)
                
                # 格式化坐标轴数字
                from matplotlib.ticker import FuncFormatter, MaxNLocator
                
                def format_axis(x, pos):
                    """格式化坐标轴数字显示"""
                    if abs(x) >= 1000:
                        return f'{x:.0f}'
                    elif abs(x) >= 100:
                        return f'{x:.1f}'
                    elif abs(x) >= 10:
                        return f'{x:.1f}'
                    elif abs(x) >= 1:
                        return f'{x:.2f}'
                    elif abs(x) >= 0.01:
                        return f'{x:.2f}'
                    elif abs(x) >= 0.0001:
                        return f'{x:.2f}'
                    elif abs(x) == 0:
                        return '0'
                    else:
                        return f'{x:.1e}'
                
                # 限制坐标轴刻度数量，避免过于拥挤
                ax.xaxis.set_major_locator(MaxNLocator(nbins=5, prune='both'))
                ax.yaxis.set_major_locator(MaxNLocator(nbins=5, prune='both'))
                ax.xaxis.set_major_formatter(FuncFormatter(format_axis))
                ax.yaxis.set_major_formatter(FuncFormatter(format_axis))
            
            # 隐藏多余的子图
            for i in range(len(page_factors), plots_per_page):
                row_idx = i // cols
                col_idx = i % cols
                if row_idx < rows:
                    axes[row_idx, col_idx].axis('off')
            
            # 调整布局
            plt.tight_layout()
            
            # 保存图片
            try:
                if num_pages > 1:
                    save_file = os.path.join(sub_dir, f"QQ{symbol_suffix}_page{page_idx + 1}.png")
                else:
                    save_file = os.path.join(sub_dir, f"QQ{symbol_suffix}.png")
                    
                plt.savefig(save_file, dpi=300, bbox_inches='tight', 
                          facecolor='white', edgecolor='none')
                BaseObj.log(f"QQ图已保存: {save_file}", level="INFO")
            except Exception as e:
                BaseObj.log(f"保存QQ图失败: {e}", level="ERROR")
                raise
            finally:
                plt.close(fig)

    @staticmethod
    @calc_df_by_symbol
    def analyze_stats(df: pd.DataFrame, *, save_path: str = None, **kwargs) -> pd.DataFrame:
        """分析分布统计指标
        
        Args:
            df: 包含因子数据的DataFrame（可能是多级索引）
            save_path: 可选，统计结果保存路径
            **kwargs: 其他参数
            
        Returns:
            DataFrame: 包含各特征统计指标的汇总表
            columns: ['symbol', 'feature', 'count', 'mean', 'std', 'skew', 'kurt', 
                     'q1', 'q5', 'q25', 'q50', 'q75', 'q95', 'q99', 
                     'outlier_ratio', 'normality_p']
        """
        from scipy import stats
        
        # 装饰器已经处理了分组，这里直接处理单个symbol的数据
        from scipy import stats
        
        # 从kwargs中获取当前symbol信息
        current_symbol = kwargs.get('current_symbol', None)
        
        if df.empty:
            return pd.DataFrame()
        
        # 数据预处理 - 只保留数值列
        numeric_cols = [col for col in df.columns if pd.api.types.is_numeric_dtype(df[col])]
        if not numeric_cols:
            BaseObj.log(f"警告: 品种{current_symbol} 未找到数值列", level="WARNING")
            return pd.DataFrame()
            
        df_work = df[numeric_cols].copy()
        
        # 初始化结果列表
        results = []
        
        for col in numeric_cols:
            # 获取数据并处理缺失值
            data = df_work[col].dropna()
            
            if len(data) == 0:
                # 无有效数据的情况
                result = {
                    'feature': col,
                    'count': 0,
                    'mean': np.nan, 'std': np.nan, 'skew': np.nan, 'kurt': np.nan,
                    'q1': np.nan, 'q5': np.nan, 'q25': np.nan, 'q50': np.nan, 
                    'q75': np.nan, 'q95': np.nan, 'q99': np.nan,
                    'outlier_ratio': np.nan, 'normality_p': np.nan
                }
            else:
                # 基础统计量
                mean_val = data.mean()
                std_val = data.std()
                skew_val = data.skew()
                kurt_val = data.kurtosis()
                
                # 分位数
                quantiles = data.quantile([0.01, 0.05, 0.25, 0.5, 0.75, 0.95, 0.99])
                
                # 异常值比例（使用IQR方法）
                q1, q3 = quantiles[0.25], quantiles[0.75]
                iqr = q3 - q1
                if iqr > 0:
                    lower_bound = q1 - 1.5 * iqr
                    upper_bound = q3 + 1.5 * iqr
                    outliers = data[(data < lower_bound) | (data > upper_bound)]
                    outlier_ratio = len(outliers) / len(data)
                else:
                    outlier_ratio = 0.0
                
                # 正态性检验
                try:
                    if std_val <= 1e-10:
                        # 常数数据或方差极小，无法进行正态性检验
                        normality_p = np.nan
                    elif len(data) >= 3 and len(data) <= 5000:
                        # 使用Shapiro-Wilk检验
                        _, normality_p = stats.shapiro(data)
                    elif len(data) > 5000:
                        # 大样本使用Kolmogorov-Smirnov检验
                        standardized = (data - mean_val) / std_val
                        _, normality_p = stats.kstest(standardized, 'norm')
                    else:
                        normality_p = np.nan  # 数据太少，无法检验
                except Exception:
                    normality_p = np.nan
                
                # 组装结果
                result = {
                    'feature': col,
                    'count': len(data),
                    'mean': mean_val,
                    'std': std_val,
                    'skew': skew_val,
                    'kurt': kurt_val,
                    'q1': quantiles[0.01],
                    'q5': quantiles[0.05],
                    'q25': quantiles[0.25],
                    'q50': quantiles[0.5],
                    'q75': quantiles[0.75],
                    'q95': quantiles[0.95],
                    'q99': quantiles[0.99],
                    'outlier_ratio': outlier_ratio,
                    'normality_p': normality_p
                }
            
            results.append(result)
        
        # 转换为DataFrame
        stats_df = pd.DataFrame(results)
        
        # 可选：保存结果
        if save_path:
            import os
            from datetime import datetime
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M")
            sub_dir = os.path.join(save_path, f"features/{timestamp}_STATS")
            os.makedirs(sub_dir, exist_ok=True)
            
            symbol_suffix = f"_{current_symbol}" if current_symbol else ""
            save_file = os.path.join(sub_dir, f"distribution_stats{symbol_suffix}.csv")
            
            try:
                stats_df.to_csv(save_file, index=False, float_format='%.6f')
                BaseObj.log(f"分布统计已保存: {save_file}", level="INFO")
            except Exception as e:
                BaseObj.log(f"保存分布统计失败: {e}", level="ERROR")
        
        return stats_df
    
class FeatEngineering(BaseObj):
    """特征工程相关工具"""
    
    @staticmethod
    @calc_df_by_symbol
    def extract_tsfresh_features(df: pd.DataFrame, *,
                               feature_set: str = 'efficient',
                               column_sort: str = 'datetime',
                               column_value: str = None,
                               n_jobs: int = 1,
                               custom_fc_parameters: dict = None,
                               **kwargs) -> pd.DataFrame:
        """
        使用 tsfresh 提取时间序列特征
        
        Args:
            df: 时间序列数据（单个symbol的数据，由装饰器处理分组）
            feature_set: 特征集合类型，'efficient'(高效) 或 'comprehensive'(全面) 或 'custom'(自定义)
            column_sort: 时间排序列名
            column_value: 值列名，如果为None则处理所有数值列
            n_jobs: 并行进程数
            custom_fc_parameters: 自定义特征参数（当feature_set='custom'时使用）
            **kwargs: 其他参数，包含current_symbol（由装饰器传入）
            
        Returns:
            提取的特征DataFrame
        """
        if df.empty:
            return pd.DataFrame()
            
        # 从kwargs中获取当前symbol信息
        current_symbol = kwargs.get('current_symbol', None) 
        
        try:
            # 处理数值列
            if column_value is None:
                numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
                if column_sort in numeric_cols:
                    numeric_cols.remove(column_sort)
            else:
                numeric_cols = [column_value] if column_value in df.columns else []
            
            if not numeric_cols:
                FeatEngineering.log(f"警告: 品种{current_symbol} 未找到数值列", level="WARNING")
                return pd.DataFrame()
            
            all_features = []
            
            for col in numeric_cols:
                # 为tsfresh准备数据格式，需要添加id列
                temp_df = df[[column_sort, col]].copy()
                temp_df['id'] = current_symbol or 0  # 添加id列
                temp_df = temp_df.rename(columns={col: 'value'})
                temp_df = temp_df[['id', column_sort, 'value']]  # 重新排列列顺序
                
                # 选择特征参数
                if feature_set == 'efficient':
                    from tsfresh.feature_extraction import EfficientFCParameters
                    fc_parameters = EfficientFCParameters()
                elif feature_set == 'comprehensive':
                    from tsfresh.feature_extraction import ComprehensiveFCParameters
                    fc_parameters = ComprehensiveFCParameters()
                elif feature_set == 'custom' and custom_fc_parameters:
                    fc_parameters = custom_fc_parameters
                else:
                    FeatEngineering.log(f"无效的特征集合类型: {feature_set}", level="ERROR")
                    continue
                
                # 提取特征
                from tsfresh import extract_features
                features = extract_features(
                    temp_df,
                    column_id='id',
                    column_sort=column_sort,
                    default_fc_parameters=fc_parameters,
                    n_jobs=n_jobs
                )
                
                # 重命名特征列
                features.columns = [f"{col}__{feat}" for feat in features.columns]
                all_features.append(features)
            
            # 合并所有特征
            if all_features:
                result = pd.concat(all_features, axis=1)
                # 处理缺失值
                from tsfresh.utilities.dataframe_functions import impute
                impute(result)
                
                # 确保索引与原始数据一致
                if len(result) == 1:  # tsfresh返回的是聚合特征，只有一行
                    # 将单行特征扩展到与原始数据相同的行数
                    result_expanded = pd.DataFrame(
                        np.tile(result.values, (len(df), 1)),
                        columns=result.columns,
                        index=df.index
                    )
                    FeatEngineering.log(f"品种{current_symbol}: 成功提取 {result_expanded.shape[1]} 个 tsfresh 特征", level="INFO")
                    return result_expanded
                else:
                    FeatEngineering.log(f"品种{current_symbol}: 成功提取 {result.shape[1]} 个 tsfresh 特征", level="INFO")
                    return result
            else:
                return pd.DataFrame()
                
        except Exception as e:
            FeatEngineering.log(f"品种{current_symbol}: tsfresh 特征提取失败: {e}", level="ERROR")
            return pd.DataFrame()

    @staticmethod
    @calc_df_by_symbol
    def featuretools_extract(df: pd.DataFrame, *,
                       window_size: int = 20,
                       max_depth: int = 1,
                       agg_primitives: List[str] = ['mean', 'std', 'min', 'max'],
                       trans_primitives: List[str] = ['diff', 'lag'],
                       groupby_trans_primitives: List[str] = ['cum_sum', 'cum_mean'],
                       n_jobs: int = 1,
                       var_threshold: float = 0.0005,
                       verbose: bool = False,
                       **kwargs) -> pd.DataFrame:
        """
        使用 featuretools 对筛选后的特征进行时序衍生
        
        基于滑动窗口对现有特征进行聚合和变换，生成更多样性的时序特征。
        专门针对已经过筛选的特征集合进行深度特征工程。
        
        Args:
            df: 已筛选的特征数据（包含base2keep、label等）
            window_size: 滑动窗口大小，默认20个时间点
            max_depth: 特征合成最大深度，默认1（避免过度复杂）
            agg_primitives: 聚合函数列表
            trans_primitives: 变换函数列表  
            n_jobs: 并行数，默认1
            var_threshold: 方差阈值，默认0.0005
            **kwargs: 其他参数，包含current_symbol
            
        Returns:
            包含原始特征和衍生特征的DataFrame
        """
        # 记录输入特征数
        current_symbol = kwargs.get('current_symbol', 'Unknown')
        FeatEngineering.log(f"[{current_symbol}] featuretools输入: {df.shape[1]} 列, {df.shape[0]} 行", level="DEBUG")
        
        if df.empty or len(df) < window_size:
            FeatEngineering.log(f"[{current_symbol}] 数据不足，跳过特征衍生", level="WARNING")
            return df
        
        try:
            import featuretools as ft
        except ImportError:
            FeatEngineering.log("featuretools 未安装，跳过特征衍生", level="WARNING")
            return df
        
        current_symbol = kwargs.get('current_symbol', 'Unknown')
        
        # 1. 准备数据
        df_work = df.copy().reset_index()
        time_col = df_work.columns[0]  # 第一列是时间索引  
        df_work['entity_id'] = 0  # 单一实体
        df_work['observation_id'] = range(len(df_work))
        
        # 2. 识别不同类型的列
        label_cols = [col for col in df.columns if col.startswith('label_')]
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        feature_cols = [col for col in numeric_cols if col not in label_cols]
        # 最小化修改：确保 time_col 被包含用于时间原语
        if pd.api.types.is_datetime64_any_dtype(df_work[time_col]):
            feature_cols.append(time_col)
        
        if len(feature_cols) < 2:
            FeatEngineering.log(f"[{current_symbol}] 特征列不足，跳过衍生", level="DEBUG")
            return df
        
        try:
            # 3. 创建实体集 - 正确处理数据类型
            es = ft.EntitySet(id=f'ts_{current_symbol}')
            
            # 构建正确的逻辑类型映射
            logical_types = {}
            for col in df_work.columns:
                if col == 'observation_id':
                    logical_types[col] = 'Integer'  # 索引列
                elif col == time_col:
                    logical_types[col] = 'Datetime'  # 时间索引列
                elif col == 'entity_id':
                    logical_types[col] = 'Integer'  # 实体ID
                elif pd.api.types.is_datetime64_any_dtype(df_work[col]):
                    logical_types[col] = 'Datetime'
                elif pd.api.types.is_numeric_dtype(df_work[col]):
                    logical_types[col] = 'Double'
                elif pd.api.types.is_categorical_dtype(df_work[col]) or df_work[col].dtype == 'object':
                    logical_types[col] = 'Categorical'
                else:
                    logical_types[col] = 'Unknown'
            
            es = es.add_dataframe(
                dataframe=df_work,
                dataframe_name='observations',
                index='observation_id',
                time_index=time_col,
                logical_types=logical_types  # 使用完整的类型映射
            )
            
            # 4. 生成截止时间（滑动窗口）
            cutoff_times = []
            for i in range(window_size, len(df_work)):
                cutoff_times.append({
                    'observation_id': i,
                    time_col: df_work.iloc[i][time_col]
                })
            
            if not cutoff_times:
                FeatEngineering.log(f"[{current_symbol}] 无法生成截止时间", level="WARNING")
                return df
            
            cutoff_times_df = pd.DataFrame(cutoff_times)
            
            # 5. 执行特征合成
            feature_matrix, _ = ft.dfs(
                entityset=es,
                target_dataframe_name='observations',
                cutoff_time=cutoff_times_df,
                agg_primitives=agg_primitives,
                trans_primitives=trans_primitives,
                groupby_trans_primitives=groupby_trans_primitives,
                max_depth=max_depth,
                verbose=verbose,
                n_jobs=n_jobs if n_jobs > 0 else 1
            )
            
            if feature_matrix.empty:
                FeatEngineering.log(f"[{current_symbol}] 特征合成结果为空", level="WARNING")
                return df
            
            # 6. 清理和筛选特征
            # 移除包含原始特征名的衍生特征（避免重复）
            original_feature_names = set(feature_cols)
            derived_cols = []
            
            for col in feature_matrix.columns:
                col_str = str(col)
                # 只保留真正的衍生特征
                if not any(orig_name == col_str for orig_name in original_feature_names):
                    derived_cols.append(col)
            
            if not derived_cols:
                FeatEngineering.log(f"[{current_symbol}] 无有效衍生特征", level="DEBUG")
                return df
            
            derived_features = feature_matrix[derived_cols]
            
            # 强制转换所有衍生特征为数值类型，避免Categorical问题
            for col in derived_features.columns:
                if pd.api.types.is_categorical_dtype(derived_features[col]):
                    derived_features[col] = pd.to_numeric(derived_features[col], errors='coerce')
                elif not pd.api.types.is_numeric_dtype(derived_features[col]):
                    derived_features[col] = pd.to_numeric(derived_features[col], errors='coerce')
            
            # 处理缺失值和异常值
            derived_features = derived_features.replace([np.inf, -np.inf], np.nan)
            derived_features = derived_features.ffill().fillna(0)
            
            # 7. 特征筛选（可选）
            if var_threshold and len(derived_cols) > 10:
                from sklearn.feature_selection import VarianceThreshold
                selector = VarianceThreshold(threshold=var_threshold)
                
                try:
                    selected_features = selector.fit_transform(derived_features)
                    selected_cols = derived_features.columns[selector.get_support()]
                    derived_features = pd.DataFrame(
                        selected_features, 
                        index=derived_features.index, 
                        columns=selected_cols
                    )
                    FeatEngineering.log(f"[{current_symbol}] 特征筛选: {len(derived_cols)} -> {len(selected_cols)}", level="DEBUG")
                except Exception as e:
                    pass  # 筛选失败时保留所有特征
            
            # 8. 合并到原始数据
            result = df.copy()
            
            # 优化：一次性创建所有衍生特征列，避免碎片化
            if not derived_features.empty:
                # 创建衍生特征DataFrame
                ftool_data = pd.DataFrame(
                    index=result.index,
                    columns=[f'ftool_{col}' for col in derived_features.columns],
                    dtype='float64'
                )
                
                # 填充数据
                start_idx = window_size
                if start_idx + len(derived_features) <= len(result):
                    for i, col in enumerate(derived_features.columns):
                        ftool_col = f'ftool_{col}'
                        ftool_data.loc[result.index[start_idx:start_idx+len(derived_features)], ftool_col] = derived_features[col].values
                
                # 一次性合并，避免逐列添加
                result = pd.concat([result, ftool_data], axis=1)
                
                # 前向填充窗口期的缺失值
                derived_feature_cols = [col for col in result.columns if col.startswith('ftool_')]
                if derived_feature_cols:
                    result[derived_feature_cols] = result[derived_feature_cols].ffill().fillna(0)
            
                FeatEngineering.log(
                    f"[{current_symbol}] 特征衍生完成: 新增 {len(derived_feature_cols)} 个特征 "
                    f"(窗口={window_size})"
                )
            
            # 角度归一化时间特征
            time_features = {}
            original_time_cols = []  # 记录需要删除的原始时间列
            
            for col in derived_features.columns:
                col_lower = str(col).lower()
                if any(time_word in col_lower for time_word in ['hour', 'day', 'month']):
                    if pd.api.types.is_numeric_dtype(derived_features[col]):
                        original_time_cols.append(col)  # 标记为需要删除
                        
                        if 'hour' in col_lower:
                            # hour: [0, 23] → [0, 1)
                            time_features[f'{col}_norm'] = (derived_features[col] % 24) / 24
                        elif 'month' in col_lower:
                            # month: [1, 12] → [0, 1)
                            time_features[f'{col}_norm'] = ((derived_features[col] - 1) % 12) / 12
                        elif 'day' in col_lower:
                            # day: [1, 31] → [0, 1)
                            time_features[f'{col}_norm'] = ((derived_features[col] - 1) % 31) / 31
            
            # 添加归一化时间特征并删除原始时间列
            if time_features:
                # 添加新的归一化特征
                derived_features = pd.concat([derived_features, pd.DataFrame(time_features, index=derived_features.index)], axis=1)
                
                # 删除原始时间列
                derived_features = derived_features.drop(columns=original_time_cols)
                
                FeatEngineering.log(
                f"[{current_symbol}] 替换了 {len(original_time_cols)} 个时间特征为归一化版本: "
                f"{original_time_cols} → {list(time_features.keys())}", 
                    level="DEBUG"
                )
                
            return result
            
        except Exception as e:
            FeatEngineering.log(f"[{current_symbol}] 特征衍生失败: {str(e)}", level="ERROR")
            return df

class FeatSelection(BaseObj):
    """特征选择相关工具"""
    
    @staticmethod
    @calc_df_by_symbol
    def select_features(df: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """根据toml配置选择特征"""
        from config.settings import get_feat_selected_names, get_norm_cols
        
        selected_names = get_feat_selected_names()
        if not selected_names:
            FeatSelection.log("feat.selected.names为空，返回所有特征", level="WARNING")
            return df
            
        # 获取base2keep配置
        norm_cols_config = get_norm_cols()
        base2keep = norm_cols_config.get('base2keep', [])
        
        # 获取实际存在的特征
        existing_features = [name for name in selected_names if name in df.columns]
        existing_base2keep = [name for name in base2keep if name in df.columns]
        
        missing_features = set(selected_names) - set(existing_features)
        missing_base2keep = set(base2keep) - set(existing_base2keep)
        
        if missing_features:
            FeatSelection.log(f"缺失特征: {missing_features}", level="WARNING")
        if missing_base2keep:
            FeatSelection.log(f"缺失base2keep: {missing_base2keep}", level="WARNING")
        
        if not existing_features:
            FeatSelection.log("没有找到任何配置的特征，返回所有特征", level="WARNING")
            return df
            
        # 保留目标列、base2keep和配置的特征
        target_cols = [col for col in df.columns if col.startswith('label_')]
        keep_cols = target_cols + existing_base2keep + existing_features
        
        # 去重但保持顺序
        keep_cols = list(dict.fromkeys(keep_cols))
        
        FeatSelection.log(f"选择特征: {len(existing_features)}个 + base2keep: {len(existing_base2keep)}个 + 标签: {len(target_cols)}个")
        return df[keep_cols]

    @staticmethod
    @calc_df_by_symbol
    def drop_features(df: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        基于TOML配置删除指定的特征列
        
        从配置文件中读取需要删除的特征列名，并从数据框中移除这些列。
        会保留所有label_开头的目标列，只删除特征列。
        
        Args:
            df: 输入数据框
            **kwargs: 其他参数，包含current_symbol（由装饰器传入）
            
        Returns:
            删除指定特征后的数据框
        """
        if df.empty:
            return df
        
        # 获取配置中需要删除的列名
        from config.settings import get_feat_drop_names
        drop_names = get_feat_drop_names()

        if not drop_names:
            current_symbol = kwargs.get('current_symbol', 'Unknown')
            FeatSelection.log(f"[{current_symbol}] 配置中未指定需要删除的特征，保留所有列", level="DEBUG")
            return df
        
        # 识别目标列（label_开头的列）
        target_cols = [col for col in df.columns if col.startswith('label_')]
        
        # 找出实际存在且需要删除的列（排除目标列）
        existing_drop_cols = []
        for col_name in drop_names:
            if col_name in df.columns and col_name not in target_cols:
                existing_drop_cols.append(col_name)
        
        # 如果没有需要删除的列
        if not existing_drop_cols:
            current_symbol = kwargs.get('current_symbol', 'Unknown')
            FeatSelection.log(
                f"[{current_symbol}] 配置的删除列 {drop_names} 在数据中不存在或为目标列，无需删除", 
                level="DEBUG"
            )
            return df
        
        # 删除指定列
        result_df = df.drop(columns=existing_drop_cols)
        
        # 记录删除信息
        current_symbol = kwargs.get('current_symbol', 'Unknown')
        FeatSelection.log(
            f"[{current_symbol}] 特征删除完成: 删除了 {len(existing_drop_cols)} 个特征列 {existing_drop_cols}, "
            f"剩余 {len(result_df.columns)} 列(不含品种列)"
        )
        
        return result_df

    @staticmethod
    @calc_df_by_symbol
    def corr_filter(df: pd.DataFrame, *,
                    ic_threshold: float = 0.015,
                    rank_ic_threshold: float = 0.02,
                    mutual_info_threshold: float = 0.03,
                    **kwargs) -> pd.DataFrame:
        """
        基于多种相关性度量的特征筛选，自动识别label_开头的目标列
        
        使用IC(Pearson)、RankIC(Spearman)和互信息三种方法筛选特征，
        满足任一条件的特征将被保留（OR逻辑）。
        同时确保TOML配置中的base2keep列不会被过滤掉。
        
        Args:
            df: 输入数据框
            ic_threshold: IC(Pearson相关系数)绝对值阈值，默认0.015
            rank_ic_threshold: RankIC(Spearman相关系数)绝对值阈值，默认0.02
            mutual_info_threshold: 互信息阈值，默认0.01
            **kwargs: 其他参数，包含current_symbol（由装饰器传入）
            
        Returns:
            筛选后的数据框，包含目标列、base2keep列和选中的特征列
        """
        if df.empty:
            return df
        
        # 获取配置中的base2keep列
        from config.settings import get_norm_cols
        norm_cols_config = get_norm_cols()
        base2keep = norm_cols_config.get('base2keep', [])
        
        # 自动识别label_开头的目标列
        target_cols = [col for col in df.columns if col.startswith('label_')]
        
        if not target_cols:
            FeatSelection.log("未找到label_开头的目标列", level="WARNING")
            return df
        
        # 获取特征列
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        feature_cols = [col for col in numeric_cols if col not in target_cols]
        
        if not feature_cols:
            FeatSelection.log("未找到可用的数值特征列", level="WARNING")
            return df[target_cols]
        
        # 识别存在于数据中的base2keep列
        existing_base2keep = [col for col in base2keep if col in df.columns]
        
        # 筛选特征
        selected_features = []
        
        for feature in feature_cols:
            feature_data = df[feature]
            
            # 如果是base2keep列，直接保留
            if feature in existing_base2keep:
                selected_features.append(feature)
                continue
            
            # 检查是否与任一目标满足相关性条件
            should_select = False
            
            for target_col in target_cols:
                target_data = df[target_col]
                
                # 跳过与目标变量完全相同的特征
                if feature_data.equals(target_data):
                    continue
                    
                try:
                    # 计算三种相关性
                    ic = abs(feature_data.corr(target_data, method='pearson'))
                    rank_ic = abs(feature_data.corr(target_data, method='spearman'))
                    mi = mutual_info_regression(feature_data.values.reshape(-1, 1), 
                                            target_data.values, random_state=42)[0]
                    
                    # 任一条件满足即标记为选中
                    if (ic > ic_threshold or 
                        rank_ic > rank_ic_threshold or 
                        mi > mutual_info_threshold):
                        should_select = True
                        break  # 已满足条件，无需继续检查其他目标
                        
                except Exception as e:
                    FeatSelection.log(f"特征 {feature} 与目标 {target_col} 计算相关性失败: {str(e)}", level="WARNING")
                    continue
            
            if should_select:
                selected_features.append(feature)
        
        # 构建结果DataFrame
        result_cols = selected_features + target_cols
        result_df = df[result_cols].copy()
        
        current_symbol = kwargs.get('current_symbol', 'Unknown')
        
        # 统计信息
        base2keep_count = len(existing_base2keep)
        corr_selected_count = len(selected_features) - base2keep_count
        
        FeatSelection.log(
            f"[{current_symbol}] 相关性筛选完成: "
            f"从 {len(feature_cols)} 个特征中选择了 {len(selected_features)} 个特征 "
            f"(base2keep: {base2keep_count}, 相关性选择: {corr_selected_count}, 目标列: {len(target_cols)})"
        )
        
        if existing_base2keep:
            FeatSelection.log(f"[{current_symbol}] 保留的base2keep列: {existing_base2keep}", level="DEBUG")
        
        return result_df

    @staticmethod
    @calc_df_by_symbol
    def redundancy_filter(df: pd.DataFrame, *,
                        corr_threshold: float = 0.85,
                        cluster_method: str = 'average',
                        features_per_cluster: int = 1,
                        **kwargs) -> pd.DataFrame:
        """
        基于相关性和聚类的冗余特征去除
        
        通过计算特征间相关性矩阵，使用层次聚类对高相关特征分组，
        每个簇保留最具代表性的特征。
        同时确保TOML配置中的base2keep列不会被去除。
        
        Args:
            df: 输入数据框（假设常数特征已在外部预处理）
            corr_threshold: 特征间相关性阈值，默认0.85
            cluster_method: 聚类方法，'single'/'complete'/'average'，默认'average'
            features_per_cluster: 每个簇保留的特征数量，默认1
            **kwargs: 其他参数，包含current_symbol（由装饰器传入）
            
        Returns:
            去除冗余后的数据框，包含目标列、base2keep列和选中的特征列
        """
        if df.empty:
            return df
        
        # 获取配置中的base2keep列
        from config.settings import get_norm_cols
        norm_cols_config = get_norm_cols()
        base2keep = norm_cols_config.get('base2keep', [])
        
        # 分离目标列和特征列
        target_cols = [col for col in df.columns if col.startswith('label_')]
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        feature_cols = [col for col in numeric_cols if col not in target_cols]
        
        if len(feature_cols) <= 1:
            FeatSelection.log("特征数量<=1，无需去冗余", level="INFO")
            return df
        
        # 识别存在于数据中的base2keep列
        existing_base2keep = [col for col in base2keep if col in feature_cols]
        
        # 计算特征间相关性矩阵
        feature_data = df[feature_cols]
        corr_matrix = feature_data.corr().abs()
        
        # 处理NaN值：用0填充（表示无相关性）
        corr_matrix = corr_matrix.fillna(0)
        
        # 检查是否存在高相关特征对
        high_corr_pairs = []
        for i in range(len(feature_cols)):
            for j in range(i+1, len(feature_cols)):
                corr_val = corr_matrix.iloc[i, j]
                if corr_val > corr_threshold:
                    high_corr_pairs.append((feature_cols[i], feature_cols[j], corr_val))
        
        if not high_corr_pairs:
            current_symbol = kwargs.get('current_symbol', 'Unknown')
            FeatSelection.log(f"[{current_symbol}] 无高相关特征对，保留所有 {len(feature_cols)} 个特征")
            return df
        
        # 使用层次聚类对特征分组
        from sklearn.cluster import AgglomerativeClustering
        
        # 将相关性矩阵转换为距离矩阵
        distance_matrix = 1 - corr_matrix
        
        # 确保距离矩阵对角线为0，处理数值精度问题
        np.fill_diagonal(distance_matrix.values, 0)
        
        # 使用简化的聚类策略：基于距离阈值自动确定聚类数
        # 距离阈值 = 1 - 相关性阈值
        distance_threshold = 1 - corr_threshold
        
        clustering = AgglomerativeClustering(
            n_clusters=None,  # 不指定聚类数
            distance_threshold=distance_threshold,  # 基于距离阈值
            metric='precomputed',
            linkage=cluster_method
        )
        
        cluster_labels = clustering.fit_predict(distance_matrix)
        n_clusters = len(np.unique(cluster_labels))
        
        # 为每个簇选择代表性特征
        selected_features = []
        
        for cluster_id in range(n_clusters):
            # 获取当前簇的特征
            cluster_features = [feature_cols[i] for i in range(len(feature_cols)) 
                            if cluster_labels[i] == cluster_id]
            
            # 检查簇中是否有base2keep列
            cluster_base2keep = [f for f in cluster_features if f in existing_base2keep]
            
            if cluster_base2keep:
                # 如果簇中有base2keep列，优先保留所有base2keep列
                selected_features.extend(cluster_base2keep)
                
                # 如果还需要保留更多特征，从非base2keep列中选择
                remaining_features = [f for f in cluster_features if f not in existing_base2keep]
                remaining_slots = features_per_cluster - len(cluster_base2keep)
                
                if remaining_slots > 0 and remaining_features:
                    # 对剩余特征进行评分选择
                    cluster_scores = []
                    
                    for feature in remaining_features:
                        # 计算特征的代表性得分：与目标列的最大相关性
                        max_target_corr = 0
                        if target_cols:
                            for target_col in target_cols:
                                try:
                                    corr_val = abs(df[feature].corr(df[target_col], method='spearman'))
                                    if not np.isnan(corr_val):
                                        max_target_corr = max(max_target_corr, corr_val)
                                except:
                                    continue
                        
                        # 计算与其他特征的平均相关性（越低越好，表示独特性）
                        other_features = [f for f in feature_cols if f != feature]
                        if other_features:
                            avg_feature_corr = corr_matrix.loc[feature, other_features].mean()
                            if np.isnan(avg_feature_corr):
                                avg_feature_corr = 0
                        else:
                            avg_feature_corr = 0
                        
                        # 综合得分：目标相关性高 + 特征独特性高
                        score = max_target_corr - 0.3 * avg_feature_corr
                        cluster_scores.append((feature, score))
                    
                    # 按得分排序，选择top特征
                    cluster_scores.sort(key=lambda x: x[1], reverse=True)
                    selected_count = min(remaining_slots, len(cluster_scores))
                    selected_features.extend([item[0] for item in cluster_scores[:selected_count]])
            
            elif len(cluster_features) == 1:
                # 单个特征直接保留
                selected_features.extend(cluster_features)
            else:
                # 簇中没有base2keep列，按原逻辑选择代表性特征
                cluster_scores = []
                
                for feature in cluster_features:
                    # 计算特征的代表性得分：与目标列的最大相关性
                    max_target_corr = 0
                    if target_cols:
                        for target_col in target_cols:
                            try:
                                corr_val = abs(df[feature].corr(df[target_col], method='spearman'))
                                if not np.isnan(corr_val):
                                    max_target_corr = max(max_target_corr, corr_val)
                            except:
                                continue
                    
                    # 计算与其他特征的平均相关性（越低越好，表示独特性）
                    other_features = [f for f in feature_cols if f != feature]
                    if other_features:
                        avg_feature_corr = corr_matrix.loc[feature, other_features].mean()
                        if np.isnan(avg_feature_corr):
                            avg_feature_corr = 0
                    else:
                        avg_feature_corr = 0
                    
                    # 综合得分：目标相关性高 + 特征独特性高
                    score = max_target_corr - 0.3 * avg_feature_corr
                    cluster_scores.append((feature, score))
                
                # 按得分排序，选择top特征
                cluster_scores.sort(key=lambda x: x[1], reverse=True)
                selected_count = min(features_per_cluster, len(cluster_scores))
                selected_features.extend([item[0] for item in cluster_scores[:selected_count]])
        
        # 构建结果DataFrame
        result_cols = selected_features + target_cols
        result_df = df[result_cols].copy()
        
        current_symbol = kwargs.get('current_symbol', 'Unknown')
        
        # 统计信息
        base2keep_count = len([f for f in selected_features if f in existing_base2keep])
        redundancy_selected_count = len(selected_features) - base2keep_count
        
        FeatSelection.log(
            f"[{current_symbol}] 冗余去除完成: "
            f"从 {len(feature_cols)} 个特征中保留了 {len(selected_features)} 个特征 "
            f"(base2keep: {base2keep_count}, 冗余筛选: {redundancy_selected_count}, "
            f"相关性阈值={corr_threshold}, 聚类数={n_clusters})"
        )
        
        if existing_base2keep:
            preserved_base2keep = [f for f in selected_features if f in existing_base2keep]
            FeatSelection.log(f"[{current_symbol}] 保留的base2keep列: {preserved_base2keep}", level="DEBUG")
        
        return result_df

    @staticmethod
    @calc_df_by_symbol
    def xgb_importance_analysis(df: pd.DataFrame, *,
                               importance_types: List[str] = None,
                               max_features: int = 50,
                               model_params: dict = None,
                               enable_diagnostics: bool = True,
                               remove_correlated: bool = True,
                               corr_threshold: float = 0.95,
                               adjust_negative_corr: bool = True,
                               negative_corr_threshold: float = 0.3,
                               adjustment_factor: float = 0.5,
                               **kwargs) -> dict:
        """
        XGBoost重要性分析: https://mp.weixin.qq.com/s/uySG1mER1vHitV-k7mSsSg
        
        基于XGBoost的多种重要性指标进行特征选择分析，计算累积R²贡献，
        找到边际递减的拐点，完全复现文章的分析流程。
        
        🎯 新增：负相关特征修正机制，确保与目标变量强负相关的重要特征不被低估。
        
        Args:
            df: 输入数据框，必须包含 'ret_1' 作为目标变量
            importance_types: 重要性类型列表，默认使用推荐的3种：['gain', 'weight', 'cover']
                            支持7种类型：
                            - XGBoost原生(5种): 'weight', 'gain', 'cover', 'total_gain', 'total_cover'
                            - 高级方法(2种): 'shap'(SHAP值), 'permutation'(置换重要性)
            max_features: 最大分析特征数，默认50
            model_params: XGBoost模型参数字典
            enable_diagnostics: 是否启用数据诊断
            remove_correlated: 是否移除高相关特征  
            corr_threshold: 相关性阈值，默认0.95
            adjust_negative_corr: 是否启用负相关特征修正，默认True
            negative_corr_threshold: 负相关修正的相关性阈值，默认0.3
            adjustment_factor: 负相关特征的重要性加权因子，默认0.5（最多增加50%）
            **kwargs: 其他参数
            
        Returns:
            dict: {
                'weight': {...}, 'gain': {...}, 'cover': {...},
                'comparison': {...}  # 特征对比结果
            }
        """
        
        try:
            import xgboost as xgb
            from sklearn.metrics import r2_score
            import numpy as np
        except ImportError as e:
            FeatSelection.log(f"导入错误: {e}", level="ERROR")
            return {}
        
        current_symbol = kwargs.get('current_symbol', 'Unknown')
        
        # 🎯 配置默认重要性类型：推荐的3种最重要算法
        if importance_types is None:
            importance_types = ['gain', 'weight', 'cover']  # 默认推荐算法
        
        # 验证重要性类型 - 扩展支持SHAP值和置换重要性
        valid_types = ['weight', 'gain', 'cover', 'total_gain', 'total_cover', 'shap', 'permutation']
        importance_types = [t for t in importance_types if t in valid_types]
        
        if not importance_types:
            FeatSelection.log(f"[{current_symbol}] 无效的重要性类型，使用默认配置", level="WARNING")
            importance_types = ['gain', 'weight', 'cover']
        
        # 🚀 新增：时序数据稳定性增强配置
        stability_runs = kwargs.get('stability_runs', 3)  # 多次运行求平均
        feature_shuffle = kwargs.get('feature_shuffle', True)  # 特征列随机打乱
        
        # 🔧 修正1：统一目标变量处理逻辑
        target_cols = [col for col in df.columns if col.startswith('label_')]
        if not target_cols:
            # 如果没有label_列，尝试使用ret_1
            if 'ret_1' in df.columns:
                target_cols = ['ret_1']
            else:
                FeatSelection.log(f"[{current_symbol}] 未找到目标列", level="WARNING")
                return {}
        
        # 使用第一个目标列（XGBoost是单目标的）
        target_col = target_cols[0]
        
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        feature_cols = [col for col in numeric_cols if col not in target_cols]
        
        if len(feature_cols) < 3:
            FeatSelection.log(f"[{current_symbol}] 特征数量过少({len(feature_cols)})，无法分析", level="WARNING")
            return {}
        
        # 🔧 修正2：正确的数据准备
        X = df[feature_cols].fillna(0)
        y = df[target_col].fillna(0)  # 确保y是Series，不是DataFrame
        
        # 数据诊断
        if enable_diagnostics:
            FeatSelection.log(f"[{current_symbol}] 数据诊断: 样本{len(df)}, 原始特征{len(feature_cols)}")
        
        # 移除高相关特征
        if remove_correlated and len(feature_cols) > 1:
            # 🎯 优化：如果已经做过redundancy_filter，可以跳过这一步
            # 通过检查特征数量判断是否已经过滤过
            if len(feature_cols) > 100:  # 大量特征时才进行过滤
                corr_matrix = X.corr().abs()
                removed_features = set()
                
                # 🔧 修正3：正确的相关性计算
                for i in range(len(feature_cols)):
                    for j in range(i + 1, len(feature_cols)):
                        if corr_matrix.iloc[i, j] > corr_threshold:
                            feat1, feat2 = feature_cols[i], feature_cols[j]
                            if feat1 not in removed_features and feat2 not in removed_features:
                                # 保留与目标相关性更高的特征
                                corr1 = abs(X[feat1].corr(y))  # 现在y是Series，这里正确
                                corr2 = abs(X[feat2].corr(y))
                                removed_features.add(feat2 if corr1 >= corr2 else feat1)
                
                feature_cols = [col for col in feature_cols if col not in removed_features]
                X = X[feature_cols]
                
                if enable_diagnostics:
                    FeatSelection.log(f"  移除{len(removed_features)}个高相关特征, 剩余{len(feature_cols)}个")
            else:
                if enable_diagnostics:
                    FeatSelection.log(f"  特征数量较少({len(feature_cols)}个)，跳过相关性过滤")
        
        # 调整模型参数避免过拟合
        default_params = {
            'objective': 'reg:squarederror',
            'n_estimators': 20,
            'max_depth': 3,
            'learning_rate': 0.1,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'random_state': 42,
            'verbosity': 0
        }
        
        if model_params:
            default_params.update(model_params)
        
        results = {}
        
        # 🔍 添加更多调试信息
        FeatSelection.log(f"[{current_symbol}] 开始分析{len(importance_types)}种重要性类型: {importance_types}")
        FeatSelection.log(f"[{current_symbol}] X形状: {X.shape}, y形状: {y.shape}")
        FeatSelection.log(f"[{current_symbol}] 模型参数: {default_params}")
        
        # 分析每种重要性类型
        for imp_type in importance_types:
            try:
                FeatSelection.log(f"[{current_symbol}] 开始分析重要性类型: {imp_type}")
                
                # 🌟 时序数据稳定性增强：多次运行ensemble
                all_feature_importance = []
                
                for run_idx in range(stability_runs):
                    FeatSelection.log(f"[{current_symbol}] {imp_type} 第{run_idx+1}/{stability_runs}次运行...")
                    
                    # 准备当前运行的数据
                    current_X = X.copy()
                    current_y = y.copy()
                    
                    # 🎲 特征列随机打乱（保持时序数据顺序不变）
                    if feature_shuffle and run_idx > 0:
                        import numpy as np
                        np.random.seed(42 + run_idx)  # 确保可重现
                        shuffled_cols = np.random.permutation(current_X.columns).tolist()
                        current_X = current_X[shuffled_cols]
                    
                    # 训练模型
                    model_params_run = default_params.copy()
                    model_params_run['random_state'] = 42 + run_idx  # 不同随机种子
                    
                    model = xgb.XGBRegressor(**model_params_run)
                    model.fit(current_X, current_y)
                    
                    # 🌟 获取重要性 - 支持多种算法
                    run_feature_importance = {}
                    
                    if imp_type in ['weight', 'gain', 'cover', 'total_gain', 'total_cover']:
                        # XGBoost原生重要性
                        importance_dict = model.get_booster().get_score(importance_type=imp_type)
                        
                        if importance_dict:
                            # 映射特征编号到特征名
                            for feature_name in current_X.columns:
                                if feature_name in importance_dict:
                                    run_feature_importance[feature_name] = importance_dict[feature_name]
                    
                    elif imp_type == 'shap':
                        # SHAP值重要性
                        try:
                            import shap
                            explainer = shap.TreeExplainer(model)
                            
                            # 🚨 时序数据修正：使用最新的N个样本，严格保持时序顺序
                            sample_size = min(100, len(current_X))
                            X_sample = current_X.iloc[-sample_size:]
                            
                            # 计算SHAP值
                            shap_values = explainer.shap_values(X_sample)
                            
                            # 计算每个特征的平均绝对SHAP值作为重要性
                            if isinstance(shap_values, list):
                                shap_values = shap_values[0]  # 回归问题取第一个
                            
                            run_feature_importance = {
                                current_X.columns[i]: abs(shap_values[:, i]).mean()
                                for i in range(len(current_X.columns))
                            }
                            
                        except ImportError:
                            FeatSelection.log(f"[{current_symbol}] SHAP库未安装，跳过SHAP分析", level="WARNING")
                            continue
                        except Exception as e:
                            FeatSelection.log(f"[{current_symbol}] SHAP计算出错: {e}", level="ERROR")
                            continue
                    
                    elif imp_type == 'permutation':
                        # 置换重要性
                        try:
                            from sklearn.inspection import permutation_importance
                            
                            # 计算置换重要性
                            perm_importance = permutation_importance(
                                model, current_X, current_y, 
                                n_repeats=5,
                                random_state=42 + run_idx,
                                scoring='r2'
                            )
                            
                            # 提取重要性值
                            run_feature_importance = {
                                current_X.columns[i]: perm_importance.importances_mean[i]
                                for i in range(len(current_X.columns))
                            }
                            
                        except Exception as e:
                            FeatSelection.log(f"[{current_symbol}] 置换重要性计算出错: {e}", level="ERROR")
                            continue
                    
                    # 保存当前运行的结果
                    if run_feature_importance:
                        all_feature_importance.append(run_feature_importance)
                
                # 🎯 Ensemble：平均多次运行的重要性结果
                if not all_feature_importance:
                    FeatSelection.log(f"[{current_symbol}] {imp_type} 所有运行都失败", level="WARNING")
                    continue
                
                # 计算平均重要性
                all_features = set()
                for run_result in all_feature_importance:
                    all_features.update(run_result.keys())
                
                feature_importance = {}
                for feature in all_features:
                    importance_values = []
                    for run_result in all_feature_importance:
                        if feature in run_result:
                            importance_values.append(run_result[feature])
                    
                    if importance_values:
                        # 使用平均值作为最终重要性，提高稳定性
                        feature_importance[feature] = np.mean(importance_values)
                
                FeatSelection.log(f"[{current_symbol}] {imp_type} Ensemble完成，平均{len(all_feature_importance)}次运行，有效特征数: {len(feature_importance)}")
                
                # 验证结果
                if not feature_importance:
                    FeatSelection.log(f"[{current_symbol}] {imp_type} 重要性为空", level="WARNING")
                    continue
                
                # 后续处理...
                FeatSelection.log(f"[{current_symbol}] {imp_type} 开始后续处理...")
                
                # 🎯 负相关特征修正：考虑与目标变量的相关性方向
                if adjust_negative_corr:
                    adjusted_importance = {}
                    negative_corr_count = 0
                    
                    for feature_name, importance_value in feature_importance.items():
                        # 计算特征与目标变量的相关性
                        try:
                            correlation = X[feature_name].corr(y)
                            if pd.isna(correlation):
                                correlation = 0
                        except:
                            correlation = 0
                        
                        # 重要性修正策略：
                        # 1. 保持XGBoost重要性的绝对大小（反映预测能力）
                        # 2. 对于强负相关特征（|corr|>threshold且corr<0），给予相关性加权
                        if abs(correlation) > negative_corr_threshold and correlation < 0:
                            # 负相关但重要的特征，重要性加权
                            boost_factor = 1 + abs(correlation) * adjustment_factor
                            adjusted_importance[feature_name] = importance_value * boost_factor
                            negative_corr_count += 1
                        else:
                            # 正相关或弱相关特征保持原重要性
                            adjusted_importance[feature_name] = importance_value
                    
                    # 按修正后的重要性排序
                    sorted_features = sorted(adjusted_importance.items(), key=lambda x: x[1], reverse=True)
                    
                    if negative_corr_count > 0:
                        FeatSelection.log(f"[{current_symbol}] {imp_type} 负相关特征修正: {negative_corr_count}个特征获得重要性提升")
                else:
                    # 不进行修正，直接排序
                    sorted_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)
                
                top_features = sorted_features[:max_features]
                
                # 计算累积R²
                cumulative_r2 = []
                marginal_gains = []
                prev_r2 = 0
                
                for i, (feature_name, _) in enumerate(top_features):
                    # 使用前i+1个特征
                    selected_features = [item[0] for item in top_features[:i+1]]
                    X_subset = X[selected_features]
                    
                    temp_model = xgb.XGBRegressor(**default_params)
                    temp_model.fit(X_subset, y)
                    y_pred = temp_model.predict(X_subset)
                    
                    r2 = r2_score(y, y_pred)
                    cumulative_r2.append(r2)
                    marginal_gains.append(r2 - prev_r2)
                    prev_r2 = r2
                
                # 🔧 优化：更符合人眼判断的最优特征数算法
                optimal_n_features = FeatSelection._find_optimal_features_intelligent(
                    cumulative_r2, marginal_gains, current_symbol, imp_type
                )
                
                # 🔧 修正：统一使用最优特征数位置的R²，而不是全局最大R²
                optimal_r2 = cumulative_r2[optimal_n_features-1] if optimal_n_features > 0 and optimal_n_features <= len(cumulative_r2) else 0
                
                # 保存结果
                results[imp_type] = {
                    'feature_importance': dict(top_features),
                    'cumulative_r2': cumulative_r2,
                    'marginal_gains': marginal_gains,
                    'optimal_n_features': optimal_n_features,
                    'max_r2': max(cumulative_r2) if cumulative_r2 else 0,  # 保留全局最大R²用于其他用途
                    'optimal_r2': optimal_r2  # 新增：最优特征数位置的R²
                }
                
                FeatSelection.log(f"  {imp_type}: 最优特征数={optimal_n_features}, 最大R²={max(cumulative_r2):.4f}")
                
            except Exception as e:
                FeatSelection.log(f"[{current_symbol}] 分析 {imp_type} 时出错: {e}", level="ERROR")
                continue
        
        FeatSelection.log(f"[{current_symbol}] XGBoost重要性分析完成，共分析{len(results)}种类型")
        
        # 特征对比分析
        if len(results) > 1:
            comparison_results = FeatSelection._compare_importance_types(results, current_symbol)
            results['comparison'] = comparison_results
        
        return results

    @staticmethod
    def _compare_importance_types(analysis_results: dict, current_symbol: str) -> dict:
        """比较不同重要性类型的特征选择结果"""
        
        comparison = {
            'top_features_by_type': {},
            'common_features': set(),
            'unique_features': {},
            'r2_comparison': {},
            'ranking_correlation': {}
        }
        
        # 收集每种类型的top特征
        all_top_features = []
        for imp_type, result in analysis_results.items():
            if isinstance(result, dict) and 'top_features' in result:
                top_features = result['top_features']
                comparison['top_features_by_type'][imp_type] = top_features
                comparison['r2_comparison'][imp_type] = result['max_r2']
                all_top_features.extend(top_features)
        
        # 找到共同特征
        if len(comparison['top_features_by_type']) > 1:
            feature_sets = [set(features) for features in comparison['top_features_by_type'].values()]
            comparison['common_features'] = set.intersection(*feature_sets)
            
            # 找到每种类型独有的特征
            for imp_type, features in comparison['top_features_by_type'].items():
                other_features = set()
                for other_type, other_feat in comparison['top_features_by_type'].items():
                    if other_type != imp_type:
                        other_features.update(other_feat)
                comparison['unique_features'][imp_type] = set(features) - other_features
        
        FeatSelection.log(
            f"[{current_symbol}] 特征对比: 共同特征{len(comparison['common_features'])}个"
        )
        
        return comparison

    @staticmethod
    def _find_optimal_features_intelligent(cumulative_r2, marginal_gains, current_symbol, imp_type):
        """
        智能寻找最优特征数，更符合人眼判断的算法
        
        策略：
        1. 多种方法综合判断，而不是单一阈值
        2. 考虑整体趋势，不仅仅是局部波动
        3. 适应不同的R²水平和边际增益模式
        """
        
        if len(marginal_gains) < 3:
            return 1
            
        # 🎯 方法1：边际增益相对衰减法（主要方法）
        optimal_decay = FeatSelection._find_by_relative_decay(marginal_gains)
        
        # 🎯 方法2：累积R²饱和法（辅助验证）
        optimal_saturation = FeatSelection._find_by_saturation(cumulative_r2)
        
        # 🎯 方法3：滑动窗口平坦检测（最终验证）
        optimal_flat = FeatSelection._find_by_flat_window(marginal_gains)
        
        # 🎯 智能融合：折衷策略，取中位数偏上
        candidates = [optimal_decay, optimal_saturation, optimal_flat]
        candidates = [c for c in candidates if c > 0]  # 过滤无效值
        
        if not candidates:
            optimal_n_features = min(len(marginal_gains), 8)  # 默认值
        else:
            # 🔧 折衷调整：取中位数（50%）而不是75%，更平衡
            import numpy as np
            optimal_n_features = int(np.percentile(candidates, 50))  # 中位数
            
            # 🎯 最小约束：至少要有几个特征才有意义
            optimal_n_features = max(optimal_n_features, 3)  # 至少3个特征
            # 🚫 移除最大约束：不限制特征数量上限，让数据自己说话
        
        FeatSelection.log(
            f"[{current_symbol}] {imp_type} 最优特征数算法: "
            f"衰减法={optimal_decay}, 饱和法={optimal_saturation}, 平坦法={optimal_flat} -> 选择={optimal_n_features}"
        )
        
        return optimal_n_features
    
    @staticmethod
    def _find_by_relative_decay(marginal_gains):
        """方法1：基于边际增益相对衰减的判断"""
        if len(marginal_gains) < 5:
            return len(marginal_gains)
            
        # 找到前期的峰值增益（前50%的范围内）
        early_range = len(marginal_gains) // 2
        peak_gain = max(marginal_gains[:max(3, early_range)])
        
        # 🔧 折衷调整：峰值的5%（介于3%和10%之间）
        threshold = peak_gain * 0.05
        
        # 🔧 折衷调整：连续3次（介于2次和4次之间）
        flat_count = 0
        for i, gain in enumerate(marginal_gains[3:], start=3):
            if gain < threshold:
                flat_count += 1
                if flat_count >= 3:  # 连续3次
                    return i - 1  # 返回开始衰减前的位置
            else:
                flat_count = 0  # 重置计数器
        
        return len(marginal_gains)  # 🚫 移除上限约束：让算法自然选择
    
    @staticmethod
    def _find_by_saturation(cumulative_r2):
        """方法2：基于累积R²饱和度的判断"""
        if len(cumulative_r2) < 5:
            return len(cumulative_r2)
            
        # 计算R²增长率的变化
        growth_rates = []
        for i in range(1, len(cumulative_r2)):
            if cumulative_r2[i-1] > 0:
                growth_rate = (cumulative_r2[i] - cumulative_r2[i-1]) / cumulative_r2[i-1]
                growth_rates.append(growth_rate)
            else:
                growth_rates.append(0)
        
        if not growth_rates:
            return len(cumulative_r2)
        
        # 🔧 折衷调整：增长率阈值提高到1.5%
        low_growth_threshold = 0.015  # 1.5%
        consecutive_low = 0
        
        for i, rate in enumerate(growth_rates[2:], start=2):  # 从第3个特征开始
            if rate < low_growth_threshold:
                consecutive_low += 1
                if consecutive_low >= 3:  # 连续3次增长率小于1.5%
                    return i - 1
            else:
                consecutive_low = 0
        
        return len(cumulative_r2)  # 🚫 移除上限约束：让算法自然选择
    
    @staticmethod 
    def _find_by_flat_window(marginal_gains):
        """方法3：基于滑动窗口的平坦检测"""
        if len(marginal_gains) < 7:
            return len(marginal_gains)
            
        # 使用滑动窗口检测平坦区域
        window_size = 4
        for i in range(len(marginal_gains) - window_size + 1):
            window = marginal_gains[i:i + window_size]
            
            # 检查窗口内的方差是否很小（表示平坦）
            if len(window) > 1:
                import numpy as np
                window_std = np.std(window)
                window_mean = np.mean(window)
                
                # 如果标准差很小且均值也很小，认为是平坦区域
                if window_std < 0.005 and window_mean < 0.01:
                    return i + 2  # 返回平坦区域开始前的位置
        
        return len(marginal_gains)  # 🚫 移除上限约束：让算法自然选择


if __name__ == '__main__':
    # 测试代码
    from config import get_norm_params
    params = get_norm_params('linear', 'X')
    res = FeatPreprocessing.norm(np.array([1,2,3,4,5,6,7,8,9,10]), **params)
    print(res)
    
