"""
XentZ 配置管理 - 统一配置入口

🎯 重构后的配置体系设计：
- 框架配置 (Framework Config): 项目基础设施配置，全局稳定
- 任务配置 (Task Config): 具体任务参数，独立可变
- 统一接口: 提供简洁的配置访问和加载接口

使用方式:
    from config import settings, ROOT_DIR  # 导入框架配置和根目录
    from config import load_task_config    # 导入任务配置加载器

    # 动态加载任务配置:
    task_cfg = load_task_config('ts_l3_wfa')
"""
from dynaconf import Dynaconf
from pathlib import Path
import os

# ============================== 项目根目录和环境检测 ============================== #
# 获取项目根目录 (XentZ/)
ROOT_DIR = Path(__file__).parent.parent

# 环境检测：支持开发、测试、生产环境
ENV = os.getenv('XENTZ_ENV', 'development')

# ============================== 框架配置加载 ============================== #
# 实例化 Dynaconf，加载框架级应用配置
# 这些是项目基础设施配置：路径、数据库、缓存、计算资源等
settings = Dynaconf(
    envvar_prefix="XENTZ",
    root_path=str(ROOT_DIR),
    settings_files=[
        str(ROOT_DIR / "config/app/settings.toml"),
        str(ROOT_DIR / "config/app/.secrets.toml"),  # 可选的敏感信息
    ],
    load_dotenv=True,
    environments=True,  # 启用环境分层
    env_switcher="XENTZ_ENV",
    default_env="development",
    encoding="utf-8",
    silent=True,
)

# ============================== 路径配置 ============================== #
# 基于配置文件和环境变量的路径设置
def _get_path(key: str, default: str) -> Path:
    """获取路径配置，支持环境变量覆盖"""
    path_str = settings.get(key, default)
    # 支持相对路径（基于项目根目录）
    if not Path(path_str).is_absolute():
        return ROOT_DIR / path_str
    return Path(path_str)

REPORTS_DIR = _get_path('paths.reports_root', 'reports')
DATA_DIR = _get_path('paths.data_root', 'data')
FACTOR_ZOO_DIR = _get_path('paths.factorzoo_root', 'factorzoo_data')

# 确保关键目录存在
for dir_path in [REPORTS_DIR, DATA_DIR, FACTOR_ZOO_DIR]:
    dir_path.mkdir(parents=True, exist_ok=True)

# ============================== 任务配置加载器 ============================== #
def load_task_config(task_name: str, config_dir: str = None) -> Dynaconf:
    """
    加载任务配置文件

    Args:
        task_name: 任务名称，如 'ts_l3_wfa'
        config_dir: 配置目录，默认为 'config/tasks'

    Returns:
        Dynaconf: 任务配置对象
    """
    if config_dir is None:
        config_dir = ROOT_DIR / "config/tasks"
    else:
        config_dir = Path(config_dir)
        if not config_dir.is_absolute():
            config_dir = ROOT_DIR / config_dir

    config_file = config_dir / f"{task_name}.toml"

    if not config_file.exists():
        raise FileNotFoundError(f"任务配置文件不存在: {config_file}")

    return Dynaconf(
        settings_files=[str(config_file)],
        environments=True,
        env_switcher="XENTZ_ENV",
        default_env=ENV,
        encoding="utf-8",
        silent=True
    )


def get_config_template(template_name: str) -> dict:
    """
    获取配置模板

    Args:
        template_name: 模板名称，如 'wfa_validation'

    Returns:
        dict: 配置模板字典
    """
    templates = {
        'wfa_validation': {
            'wfa_params': {
                'lookback_window': 240,
                'step_size': 20,
                'min_periods': 120,
                'data_freq': 'daily'
            },
            'query_params': {
                'source_pipeline_step': 'L2',
                'factor_limit_per_batch': 100,
                'exclude_symbols': []
            },
            'output_params': {
                'report_dir': 'reports/L3_validation',
                'save_charts': True,
                'chart_format': 'png'
            }
        }
    }

    return templates.get(template_name, {})


# ============================== 向后兼容支持 ============================== #
# 延迟导入cfg_utils以避免循环导入
def _get_legacy_config():
    """延迟加载传统配置对象"""
    try:
        from .cfg_utils import (
            cfg_feat, cfg_mine, cfg_wfa, get_norm_params, mine_norm_params_X,
            mine_gparams, mine_runnum, fitness_params, sr_params, rankic_params
        )
        return {
            'cfg_feat': cfg_feat,
            'cfg_mine': cfg_mine,
            'cfg_wfa': cfg_wfa,
            'get_norm_params': get_norm_params,
            'mine_norm_params_X': mine_norm_params_X,
            'mine_gparams': mine_gparams,
            'mine_runnum': mine_runnum,
            'fitness_params': fitness_params,
            'sr_params': sr_params,
            'rankic_params': rankic_params
        }
    except ImportError:
        return {}

# 动态添加传统配置对象到模块命名空间
_legacy_configs = _get_legacy_config()
globals().update(_legacy_configs)

# ============================== 模块导出 ============================== #
__all__ = [
    # 核心配置
    'settings',           # dynaconf全局设置
    'ROOT_DIR',          # 项目根目录
    'ENV',               # 当前环境
    'REPORTS_DIR',       # 报告输出目录
    'DATA_DIR',          # 数据目录
    'FACTOR_ZOO_DIR',    # FactorZoo目录

    # 配置加载器
    'load_task_config',   # 任务配置加载器
    'get_config_template', # 配置模板获取器
] + list(_legacy_configs.keys())  # 动态添加传统配置对象
