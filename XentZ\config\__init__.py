"""
XentZ 配置管理 - 框架配置入口

🎯 新配置体系设计：
- 框架配置 (Framework Config): 在此文件中统一加载，全局稳定，低频修改
- 任务配置 (Task Config): 在具体脚本中动态加载，独立易变，高频修改

使用方式:
    from config import settings  # 导入框架配置
    
    # 在任务脚本中动态加载任务配置:
    task_cfg = Dynaconf(settings_files=['config/tasks/some_task.toml'])
"""
from dynaconf import Dynaconf
from pathlib import Path

# 获取项目根目录 (XentZ/)
# __file__ -> D:/.../XentZ/config/__init__.py
# Path(__file__).parent -> D:/.../XentZ/config
# Path(__file__).parent.parent -> D:/.../XentZ
ROOT_DIR = Path(__file__).parent.parent

# ============================== 框架配置加载 ============================== #
# 实例化 Dynaconf，专门加载框架级应用配置
# 这些是项目基础设施配置：路径、数据库、缓存、计算资源等
settings = Dynaconf(
    # 环境变量前缀，例如 XENTZ_DEBUG=true, XENTZ_ENV=production
    envvar_prefix="XENTZ",
    # 显式指定根目录, dynaconf会从这里开始寻找配置文件
    root_path=str(ROOT_DIR),
    # 🎯 核心修正：使用绝对路径，确保Dynaconf能准确找到配置文件
    settings_files=[
        str(ROOT_DIR / "config/app/settings.toml"),  # 主配置文件
        str(ROOT_DIR / "config/app/.secrets.toml"),   # 密钥/敏感信息 (可选)
    ],
    # 加载环境变量文件. 默认为 .env
    load_dotenv=True,
    # 启用环境分层, e.g., [default], [development], [production]
    environments=False,
    # 环境切换器和默认环境
    env_switcher="XENTZ_ENV",
    default_env="default",
    # 文件编码
    encoding="utf-8",   
    # 静默模式，避免缺少可选配置文件时报错 (如 .secrets.toml)
    silent=True,
)
REPORTS_DIR = Path(settings.get('paths.reports_root'))

# 导入cfg_utils中的配置对象和工具函数
from .cfg_utils import (
    cfg_feat,
    cfg_mine, 
    get_norm_params, 
    mine_norm_params_X, 
    mine_gparams, 
    mine_runnum,
    fitness_params,
    sr_params, 
    rankic_params
)

# 暴露所有配置相关的对象和函数
__all__ = [
    'settings',           # dynaconf全局设置
    'cfg_feat',           # 特征提取配置对象
    'cfg_mine',          # GP挖掘配置对象
    'REPORTS_DIR',       # 报告输出目录
    'ROOT_DIR',          # 项目根目录
    # 配置工具函数
    'get_norm_params',    # 通用归一化参数获取
    'mine_norm_params_X', # 获取X归一化参数
    'mine_gparams',      # 获取GP参数(带动态切换)
    'mine_runnum',       # 获取挖掘轮次
    'fitness_params',    # 获取适应度参数
    'sr_params',         # 获取夏普比率筛选参数
    'rankic_params',     # 获取RankIC筛选参数
]
