#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键格式转换
快速转换FactorZoo数据格式
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from factorzoo.scripts.format_converter import FormatConverter
from factorzoo.scripts.batch_tools import BatchTools

def main():
    print("🔄 FactorZoo格式转换")
    print("=" * 50)
    
    converter = FormatConverter()
    batch_tools = BatchTools()
    
    # 显示当前可用的数据
    batches = batch_tools.manager.get_available_batches()
    if batches:
        print(f"📊 当前有 {len(batches)} 个批次可用于转换")
        latest_batches = sorted(batches, reverse=True)[:3]
        print("最新批次:")
        for batch_id in latest_batches:
            try:
                batch_info = batch_tools.manager.get_batch_info(batch_id)
                symbol = batch_info.get('symbol', 'Unknown')
                creation_time = batch_info.get('creation_time', '')[:10]
                print(f"  - {batch_id} ({symbol}, {creation_time})")
            except:
                print(f"  - {batch_id}")
        print()
    
    # 操作菜单
    print("💡 转换选项:")
    print("1. 转换单个文件")
    print("2. 批量转换目录")
    print("3. 转换FactorZoo批次")
    print("4. 快速转换最新批次为CSV")
    print("5. 批量转换所有批次为CSV")
    print("0. 退出")
    
    try:
        choice = input("\n请选择操作 (0-5): ").strip()
        print()
        
        if choice == '0':
            print("👋 退出转换")
            return
        
        elif choice == '1':
            # 转换单个文件
            file_path = input("请输入文件路径: ").strip()
            if not file_path:
                print("❌ 路径不能为空")
                return
            
            print("目标格式:")
            print("1. CSV")
            print("2. Parquet")
            print("3. Feather")
            format_choice = input("请选择格式 (1-3): ").strip()
            
            format_map = {'1': 'csv', '2': 'parquet', '3': 'feather'}
            target_format = format_map.get(format_choice, 'csv')
            
            output_path = input("输出路径 (回车使用默认): ").strip() or None
            
            print(f"🔄 转换 {file_path} 为 {target_format.upper()}")
            converter.convert_file(file_path, output_path, target_format)
        
        elif choice == '2':
            # 批量转换目录
            dir_path = input("请输入目录路径: ").strip()
            if not dir_path:
                print("❌ 路径不能为空")
                return
            
            print("目标格式:")
            print("1. CSV")
            print("2. Parquet")
            format_choice = input("请选择格式 (1-2): ").strip()
            target_format = 'csv' if format_choice == '1' else 'parquet'
            
            recursive = input("是否递归处理子目录? (y/n): ").strip().lower() in ['y', 'yes', '是']
            
            print(f"🔄 批量转换目录 {dir_path} 为 {target_format.upper()}")
            converter.batch_convert_directory(dir_path, target_format, recursive)
        
        elif choice == '3':
            # 转换FactorZoo批次
            if not batches:
                print("❌ 暂无可用批次")
                return
            
            batch_id = input("请输入批次ID: ").strip()
            if not batch_id:
                print("❌ 批次ID不能为空")
                return
            
            print("目标格式:")
            print("1. CSV")
            print("2. Parquet")
            format_choice = input("请选择格式 (1-2): ").strip()
            target_format = 'csv' if format_choice == '1' else 'parquet'
            
            print(f"🔄 转换批次 {batch_id} 为 {target_format.upper()}")
            converter.convert_factorzoo_batch(batch_id, target_format)
        
        elif choice == '4':
            # 快速转换最新批次为CSV
            if not batches:
                print("❌ 暂无可用批次")
                return
            
            latest_batch = sorted(batches, reverse=True)[0]
            print(f"🚀 快速转换最新批次: {latest_batch}")
            
            confirm = input("确认转换? (y/n): ").strip().lower()
            if confirm in ['y', 'yes', '是']:
                converter.convert_factorzoo_batch(latest_batch, 'csv')
            else:
                print("❌ 取消转换")
        
        elif choice == '5':
            # 批量转换所有批次为CSV
            if not batches:
                print("❌ 暂无可用批次")
                return
            
            print(f"⚠️  将转换所有 {len(batches)} 个批次为CSV格式")
            confirm = input("确认执行? (输入 'YES' 确认): ").strip()
            
            if confirm == 'YES':
                print("🚀 开始批量转换...")
                success_count = 0
                
                for i, batch_id in enumerate(batches, 1):
                    print(f"\n[{i}/{len(batches)}] 转换批次: {batch_id}")
                    try:
                        converter.convert_factorzoo_batch(batch_id, 'csv')
                        success_count += 1
                    except Exception as e:
                        print(f"❌ 转换失败: {str(e)}")
                
                print(f"\n✅ 批量转换完成! 成功: {success_count}/{len(batches)}")
            else:
                print("❌ 取消批量转换")
        
        else:
            print("❌ 无效选择")
        
        # 显示转换统计
        print("\n" + "="*50)
        converter._show_stats()
        
    except KeyboardInterrupt:
        print("\n👋 退出转换")
    except Exception as e:
        print(f"❌ 操作失败: {str(e)}")

if __name__ == "__main__":
    main() 