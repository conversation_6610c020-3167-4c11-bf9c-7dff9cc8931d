#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
L3动态稳健性检验业务流程脚本
基于WFA(Walk-Forward Analysis)方法对L2阶段通过的因子进行动态稳健性验证

业务流程：
1. 加载配置参数
2. 查询L2阶段通过的因子
3. 批量执行WFA验证
4. 生成验证报告
5. 更新因子状态

"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
import time

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from common.cls_base import BaseObj
from config import load_task_config, ROOT_DIR, REPORTS_DIR
from factor.wfa_eval_utils import WFAValidator, WFAParams
from factor.factor_query_utils import factor_query_manager, batch_processor
from factor.report_generator import batch_report_manager
from factorzoo import factorzoo


class L3WFAValidator(BaseObj):
    """L3动态稳健性检验主控制器"""
    
    def __init__(self):
        """初始化L3验证器"""
        super().__init__()
        self.config = None
        self.wfa_evaluator = WFAValidator()
        self.validation_results = []
        self.log("L3动态稳健性检验器初始化完成", "INFO")
    
    def run_validation_pipeline(self, config_file: str = "config/tasks/ts_l3_wfa.toml") -> Dict[str, Any]:
        """
        执行完整的L3验证流程
        
        流程步骤：
        1. 加载和验证配置
        2. 查询L2通过的因子
        3. 批量执行WFA验证
        4. 汇总验证结果
        5. 生成报告和更新状态
        
        Args:
            config_file: 配置文件路径
            
        Returns:
            Dict[str, Any]: 验证结果汇总
        """
        self.log("开始L3动态稳健性检验流程", "INFO")
        pipeline_start_time = time.time()
        
        try:
            # 步骤1: 加载配置
            self.log("步骤1: 加载配置参数", "INFO")
            if not self._load_and_validate_config(config_file):
                return {"status": "failed", "error": "配置加载失败"}
            
            # 步骤2: 查询因子
            self.log("步骤2: 查询L2阶段通过的因子", "INFO")
            factors = self._query_l2_factors()
            if not factors:
                return {"status": "failed", "error": "未找到待验证因子"}
            
            # 步骤3: 批量验证
            self.log("步骤3: 批量执行WFA验证", "INFO")
            validation_results = self._batch_validate_factors(factors)
            
            # 步骤4: 汇总结果
            self.log("步骤4: 汇总验证结果", "INFO")
            summary = self._summarize_results(validation_results)
            
            # 步骤5: 生成验证报告（使用新的报告生成器）
            self.log("步骤5: 生成验证报告", "INFO")
            report_results = self._generate_comprehensive_reports(validation_results, summary)
            
            # 步骤6: 更新状态
            self.log("步骤6: 更新因子状态", "INFO")
            self._update_factor_status(validation_results)
            
            pipeline_time = time.time() - pipeline_start_time
            
            final_result = {
                "status": "success",
                "total_factors": len(factors),
                "validated_factors": len(validation_results),
                "passed_factors": summary.get("passed_count", 0),
                "failed_factors": summary.get("failed_count", 0),
                "pipeline_time": pipeline_time,
                "report_results": report_results,
                "summary": summary
            }
            
            self.log(f"L3验证流程完成: 总耗时{pipeline_time:.2f}秒", "INFO")
            return final_result
            
        except Exception as e:
            self.log(f"L3验证流程异常: {str(e)}", "ERROR")
            return {"status": "error", "error": str(e)}
    
    def _load_and_validate_config(self, config_file: str) -> bool:
        """加载和验证配置参数（使用新的配置系统）"""
        try:
            # 解析配置文件名
            if config_file.endswith('.toml'):
                task_name = Path(config_file).stem
            else:
                task_name = config_file

            # 使用统一的配置加载器
            self.config = load_task_config(task_name)

            # 验证必要参数（适配新的配置结构）
            required_params = [
                'factor_query.source_pipeline_step',
                'wfa.training_window',
                'wfa.step_size'
            ]

            for param in required_params:
                if not self._get_nested_config(param):
                    self.log(f"缺少必要配置参数: {param}", "ERROR")
                    return False

            self.log("配置参数验证通过", "DEBUG")
            return True

        except Exception as e:
            self.log(f"配置加载失败: {str(e)}", "ERROR")
            return False
    
    def _get_nested_config(self, param_path: str) -> Any:
        """获取嵌套配置参数"""
        keys = param_path.split('.')
        value = self.config
        
        for key in keys:
            if hasattr(value, key):
                value = getattr(value, key)
            else:
                return None
        
        return value
    
    def _query_l2_factors(self) -> List[Dict]:
        """查询L2阶段通过的因子（适配新配置结构）"""
        query_params = {
            'source_pipeline_step': self._get_nested_config('factor_query.source_pipeline_step'),
            'factor_limit_per_batch': self._get_nested_config('factor_query.factor_limit_per_batch') or 100,
            'status_filter': self._get_nested_config('factor_query.status_filter') or ['L2_PASSED'],
            'exclude_symbols': self._get_nested_config('factor_query.exclude_symbols') or []
        }
        
        factors = factor_query_manager.query_l2_passed_factors(query_params)
        
        if factors:
            self.log(f"查询到{len(factors)}个L2通过的因子", "INFO")
            
            # 按品种统计
            symbol_stats = {}
            for factor in factors:
                symbols = factor.get('symbols', ['unknown'])
                if isinstance(symbols, list) and symbols:
                    symbol = symbols[0]
                else:
                    symbol = str(symbols) if symbols else 'unknown'
                symbol_stats[symbol] = symbol_stats.get(symbol, 0) + 1
            
            self.log(f"品种分布: {symbol_stats}", "DEBUG")
        
        return factors
    
    def _batch_validate_factors(self, factors: List[Dict]) -> List[Dict]:
        """批量验证因子"""
        # 创建WFA验证函数
        def validate_single_factor(factor_info: Dict) -> Optional[Dict]:
            """验证单个因子"""
            factor_id = factor_info.get('factor_id', 'unknown')
            
            try:
                # 加载因子和价格数据
                factor_series, price_series = factor_query_manager.load_factor_and_price_data(factor_info)
                
                if factor_series.empty or price_series.empty:
                    self.log(f"因子数据为空，跳过验证: {factor_id}", "WARNING")
                    return None
                
                # 构建WFA参数（适配新配置结构）
                wfa_params = WFAParams(
                    lookback_window=self._get_nested_config('wfa.training_window'),
                    step_size=self._get_nested_config('wfa.step_size'),
                    min_periods=self._get_nested_config('wfa.min_periods') or 50,
                    data_freq='daily'  # 从数据集配置中获取
                )
                
                # 执行WFA验证
                validation_result = self.wfa_evaluator.run_wfa_validation(
                    factor_series=factor_series,
                    price_series=price_series,
                    wfa_params=wfa_params
                )
                
                # 添加因子信息
                validation_result['factor_info'] = factor_info
                validation_result['validation_time'] = datetime.now().isoformat()
                
                return validation_result
                
            except Exception as e:
                self.log(f"因子验证失败: {factor_id}, 错误: {str(e)}", "ERROR")
                return {
                    'factor_info': factor_info,
                    'status': 'error',
                    'error': str(e),
                    'validation_time': datetime.now().isoformat()
                }
        
        # 创建进度报告器
        progress_reporter = batch_processor.create_progress_reporter(log_interval=30)
        
        # 批量处理
        results = batch_processor.process_factors_batch(
            factors=factors,
            process_func=validate_single_factor,
            progress_callback=progress_reporter
        )
        
        # 过滤有效结果
        valid_results = [r for r in results if r is not None]
        
        self.log(f"批量验证完成: {len(valid_results)}/{len(factors)}个因子验证成功", "INFO")
        return valid_results
    
    def _summarize_results(self, validation_results: List[Dict]) -> Dict[str, Any]:
        """汇总验证结果"""
        if not validation_results:
            return {"passed_count": 0, "failed_count": 0, "error_count": 0}
        
        # 统计各种状态
        passed_count = sum(1 for r in validation_results if r.get('status') == 'passed')
        failed_count = sum(1 for r in validation_results if r.get('status') == 'failed')
        error_count = sum(1 for r in validation_results if r.get('status') == 'error')
        
        # 计算通过率
        total_count = len(validation_results)
        pass_rate = passed_count / total_count if total_count > 0 else 0
        
        # 收集绩效统计
        performance_stats = []
        for result in validation_results:
            if result.get('status') == 'passed' and 'performance_metrics' in result:
                performance_stats.append(result['performance_metrics'])
        
        # 计算平均绩效
        avg_metrics = {}
        if performance_stats:
            metric_keys = performance_stats[0].keys()
            for key in metric_keys:
                values = [stats[key] for stats in performance_stats if key in stats and stats[key] is not None]
                if values:
                    avg_metrics[f'avg_{key}'] = np.mean(values)
        
        summary = {
            "total_count": total_count,
            "passed_count": passed_count,
            "failed_count": failed_count,
            "error_count": error_count,
            "pass_rate": pass_rate,
            "average_metrics": avg_metrics,
            "validation_timestamp": datetime.now().isoformat()
        }
        
        self.log(f"结果汇总: 通过率{pass_rate*100:.1f}% ({passed_count}/{total_count})", "INFO")
        return summary
    
    def _generate_validation_report(self, summary: Dict, results: List[Dict]) -> str:
        """生成验证报告（使用新配置系统）"""
        # 创建报告目录（基于REPORTS_DIR）
        report_subdir = self._get_nested_config('output.save_detailed_reports') and 'L3_validation' or 'L3_validation'
        report_dir = REPORTS_DIR / report_subdir
        report_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成报告文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = report_dir / f'L3_WFA_validation_report_{timestamp}.txt'
        
        # 生成报告内容
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("L3动态稳健性检验报告\n")
            f.write("=" * 50 + "\n\n")
            
            # 汇总信息
            f.write("验证汇总:\n")
            f.write(f"  总因子数: {summary['total_count']}\n")
            f.write(f"  通过数: {summary['passed_count']}\n")
            f.write(f"  失败数: {summary['failed_count']}\n")
            f.write(f"  错误数: {summary['error_count']}\n")
            f.write(f"  通过率: {summary['pass_rate']*100:.1f}%\n\n")
            
            # 平均绩效
            if summary.get('average_metrics'):
                f.write("平均绩效指标:\n")
                for key, value in summary['average_metrics'].items():
                    f.write(f"  {key}: {value:.4f}\n")
                f.write("\n")
            
            # 详细结果
            f.write("详细验证结果:\n")
            f.write("-" * 30 + "\n")
            
            for i, result in enumerate(results, 1):
                factor_info = result.get('factor_info', {})
                factor_id = factor_info.get('factor_id', 'unknown')
                status = result.get('status', 'unknown')
                
                f.write(f"{i}. {factor_id}: {status}\n")
                
                if status == 'passed' and 'performance_metrics' in result:
                    metrics = result['performance_metrics']
                    f.write(f"   夏普比率: {metrics.get('sharpe_ratio', 'N/A'):.4f}\n")
                    f.write(f"   最大回撤: {metrics.get('max_drawdown', 'N/A'):.4f}\n")
                elif status == 'error':
                    f.write(f"   错误: {result.get('error', 'unknown')}\n")
                
                f.write("\n")
        
        self.log(f"验证报告已生成: {report_file}", "INFO")
        return str(report_file)

    def _generate_comprehensive_reports(self, validation_results: List[Dict],
                                      summary: Dict) -> Dict[str, Any]:
        """
        生成综合验证报告（集成quantstats和自定义图表）

        流程：
        1. 使用批量报告管理器生成完整报告
        2. 生成传统文本报告作为备份
        3. 返回报告生成结果

        Args:
            validation_results: 验证结果列表
            summary: 汇总统计

        Returns:
            Dict[str, Any]: 报告生成结果
        """
        self.log("开始生成综合验证报告", "INFO")

        try:
            # 步骤1: 使用批量报告管理器生成完整报告
            batch_result = batch_report_manager.generate_batch_reports(
                wfa_results=validation_results,
                config=self.config
            )

            # 步骤2: 生成传统文本报告作为备份
            text_report_path = self._generate_validation_report(summary, validation_results)

            # 步骤3: 整合报告结果
            if batch_result.get("status") == "success":
                report_info = batch_result["results"]
                report_info["text_report_path"] = text_report_path

                self.log(f"综合报告生成成功: {report_info.get('session_dir')}", "INFO")
                return report_info
            else:
                self.log(f"批量报告生成失败: {batch_result.get('error')}", "WARNING")
                # 返回基础报告信息
                return {
                    "status": "partial",
                    "text_report_path": text_report_path,
                    "error": batch_result.get("error")
                }

        except Exception as e:
            self.log(f"综合报告生成异常: {str(e)}", "ERROR")
            # 回退到基础报告
            text_report_path = self._generate_validation_report(summary, validation_results)
            return {
                "status": "fallback",
                "text_report_path": text_report_path,
                "error": str(e)
            }
    
    def _update_factor_status(self, validation_results: List[Dict]) -> None:
        """更新因子状态到FactorZoo"""
        self.log("开始更新因子状态", "DEBUG")
        
        update_count = 0
        for result in validation_results:
            factor_info = result.get('factor_info', {})
            factor_id = factor_info.get('factor_id')
            status = result.get('status')
            
            if not factor_id or not status:
                continue
            
            try:
                # 确定新状态
                if status == 'passed':
                    new_status = 'L3_PASSED'
                elif status == 'failed':
                    new_status = 'L3_FAILED'
                else:
                    new_status = 'L3_ERROR'
                
                # 更新到FactorZoo（这里需要实现具体的更新逻辑）
                # factorzoo.update_factor_status(factor_id, new_status)
                
                update_count += 1
                
            except Exception as e:
                self.log(f"更新因子状态失败: {factor_id}, 错误: {str(e)}", "ERROR")
        
        self.log(f"因子状态更新完成: {update_count}个因子", "INFO")


def main():
    """主函数"""
    print("🚀 启动L3动态稳健性检验流程")
    
    # 创建验证器
    validator = L3WFAValidator()
    
    # 执行验证流程
    result = validator.run_validation_pipeline()
    
    # 输出结果
    if result['status'] == 'success':
        print(f"✅ 验证流程完成")
        print(f"   总因子数: {result['total_factors']}")
        print(f"   通过因子数: {result['passed_factors']}")
        print(f"   失败因子数: {result['failed_factors']}")
        print(f"   总耗时: {result['pipeline_time']:.2f}秒")
        print(f"   报告路径: {result['report_path']}")
    else:
        print(f"❌ 验证流程失败: {result.get('error', 'unknown')}")


if __name__ == "__main__":
    main()
