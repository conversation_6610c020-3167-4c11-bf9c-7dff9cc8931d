2025-06-29 17:57 | INFO     | DatabaseReset.__init__: 数据库路径: D:\myquant\FZoo\database\factorzoo.sqlite
2025-06-29 17:57 | INFO     | DatabaseReset.__init__: Schema文件路径: D:\myquant\XentZ\factorzoo\schema.sql
2025-06-29 17:58 | INFO     | DatabaseReset.__init__: 数据库路径: D:\myquant\FZoo\database\factorzoo.sqlite
2025-06-29 17:58 | INFO     | DatabaseReset.__init__: Schema文件路径: d:\myquant\XentZ\factorzoo\schema.sql
2025-06-29 17:58 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-29 17:58 | INFO     | DatabaseReset.run_reset: 开始执行 XentZ 因子动物园数据库重置
2025-06-29 17:58 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-29 17:58 | INFO     | DatabaseReset.validate_files: 步骤1: 验证文件存在性...
2025-06-29 17:58 | INFO     | DatabaseReset.validate_files: Schema文件存在: d:\myquant\XentZ\factorzoo\schema.sql
2025-06-29 17:58 | INFO     | DatabaseReset.backup_database: 步骤2: 备份现有数据库...
2025-06-29 17:58 | INFO     | DatabaseReset.backup_database: 数据库已备份到: D:\myquant\FZoo\database\factorzoo.sqlite.backup_20250629_175804
2025-06-29 17:58 | INFO     | DatabaseReset.get_existing_tables: 步骤3: 获取现有表列表...
2025-06-29 17:58 | INFO     | DatabaseReset.get_existing_tables: 发现现有表 (11个): factor_batches, factor_categories, factor_evaluations, factor_performance_logs, factor_pool_members, factor_pools, factor_relationships, factor_tags, factor_values, factors, universes
2025-06-29 17:58 | INFO     | DatabaseReset.drop_all_tables: 步骤4: 删除现有表 (11个)...
2025-06-29 17:58 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_batches
2025-06-29 17:58 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_categories
2025-06-29 17:58 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_evaluations
2025-06-29 17:58 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_performance_logs
2025-06-29 17:58 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_pool_members
2025-06-29 17:58 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_pools
2025-06-29 17:58 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_relationships
2025-06-29 17:58 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_tags
2025-06-29 17:58 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_values
2025-06-29 17:58 | INFO     | DatabaseReset.drop_all_tables:   删除表: factors
2025-06-29 17:58 | INFO     | DatabaseReset.drop_all_tables:   删除表: universes
2025-06-29 17:58 | INFO     | DatabaseReset.drop_all_tables: 成功删除 11 个表, 0 个视图, 0 个索引
2025-06-29 17:58 | INFO     | DatabaseReset.execute_schema: 步骤5: 执行schema.sql重建表结构...
2025-06-29 17:58 | INFO     | DatabaseReset.execute_schema: 读取schema文件，长度: 25424 字符
2025-06-29 17:58 | ERROR    | DatabaseReset.execute_schema: 执行schema失败: FOREIGN KEY constraint failed
2025-06-29 17:58 | ERROR    | DatabaseReset.run_reset: ============================================================
2025-06-29 17:58 | ERROR    | DatabaseReset.run_reset: 数据库重置失败: FOREIGN KEY constraint failed
2025-06-29 17:58 | ERROR    | DatabaseReset.run_reset: ============================================================
2025-06-29 17:59 | INFO     | DatabaseReset.__init__: 数据库路径: D:\myquant\FZoo\database\factorzoo.sqlite
2025-06-29 17:59 | INFO     | DatabaseReset.__init__: Schema文件路径: D:\myquant\XentZ\factorzoo\schema.sql
2025-06-29 17:59 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-29 17:59 | INFO     | DatabaseReset.run_reset: 开始执行 XentZ 因子动物园数据库重置
2025-06-29 17:59 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-29 17:59 | INFO     | DatabaseReset.validate_files: 步骤1: 验证文件存在性...
2025-06-29 17:59 | INFO     | DatabaseReset.validate_files: Schema文件存在: D:\myquant\XentZ\factorzoo\schema.sql
2025-06-29 17:59 | INFO     | DatabaseReset.backup_database: 步骤2: 备份现有数据库...
2025-06-29 17:59 | INFO     | DatabaseReset.backup_database: 数据库已备份到: D:\myquant\FZoo\database\factorzoo.sqlite.backup_20250629_175924
2025-06-29 17:59 | INFO     | DatabaseReset.get_existing_tables: 步骤3: 获取现有表列表...
2025-06-29 17:59 | INFO     | DatabaseReset.get_existing_tables: 发现现有表 (0个): 
2025-06-29 17:59 | INFO     | DatabaseReset.drop_all_tables: 步骤4: 无表需要删除
2025-06-29 17:59 | INFO     | DatabaseReset.execute_schema: 步骤5: 执行schema.sql重建表结构...
2025-06-29 17:59 | INFO     | DatabaseReset.execute_schema: 读取schema文件，长度: 24886 字符
2025-06-29 17:59 | INFO     | DatabaseReset.execute_schema: Schema执行完成
2025-06-29 17:59 | INFO     | DatabaseReset.verify_tables: 步骤6: 验证表创建结果...
2025-06-29 17:59 | INFO     | DatabaseReset.verify_tables: 创建完成统计:
2025-06-29 17:59 | INFO     | DatabaseReset.verify_tables:   - 表 (9个): factor_batches, factor_categories, factor_evaluations, factor_pool_members, factor_pools, factor_relationships, factor_tags, factors, universes
2025-06-29 17:59 | INFO     | DatabaseReset.verify_tables:   - 视图 (4个): v_active_factors, v_batch_statistics, v_factor_performance_summary, v_performance_stats
2025-06-29 17:59 | INFO     | DatabaseReset.verify_tables:   - 索引 (25个): idx_evaluations_factor_id, idx_evaluations_method, idx_evaluations_period, idx_evaluations_sharpe, idx_factor_tags_tag_name, idx_factor_tags_tag_type, idx_factors_batch_id, idx_factors_computation_time, idx_factors_creation_date, idx_factors_creation_method, idx_factors_factor_type, idx_factors_generation_tool, idx_factors_market_type, idx_factors_pipeline_mode, idx_factors_pipeline_step, idx_factors_primary_category, idx_factors_resource_intensity, idx_factors_status, idx_factors_target_label, idx_factors_unique_expression, idx_pool_members_factor_id, idx_pool_members_pool_id, idx_pool_members_status, idx_universes_current, idx_universes_effective_date
2025-06-29 17:59 | INFO     | DatabaseReset.verify_tables: 所有核心表创建成功
2025-06-29 17:59 | INFO     | DatabaseReset.insert_default_data: 步骤7: 插入默认数据...
2025-06-29 17:59 | INFO     | DatabaseReset.insert_default_data: 默认数据插入完成
2025-06-29 17:59 | INFO     | DatabaseReset.insert_default_data:   - 因子分类: 10 个
2025-06-29 17:59 | INFO     | DatabaseReset.insert_default_data:   - 股票池: 2 个
2025-06-29 17:59 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-29 17:59 | INFO     | DatabaseReset.run_reset: 数据库重置完成！
2025-06-29 17:59 | INFO     | DatabaseReset.run_reset: 数据库路径: D:\myquant\FZoo\database\factorzoo.sqlite
2025-06-29 17:59 | INFO     | DatabaseReset.run_reset: 备份路径: D:\myquant\FZoo\database\factorzoo.sqlite.backup_20250629_175924
2025-06-29 17:59 | INFO     | DatabaseReset.run_reset: 创建统计: 9表 + 4视图 + 25索引
2025-06-29 17:59 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-29 18:00 | INFO     | DatabaseReset.__init__: 数据库路径: D:\myquant\FZoo\database\factorzoo.sqlite
2025-06-29 18:00 | INFO     | DatabaseReset.__init__: Schema文件路径: D:\myquant\XentZ\factorzoo\schema.sql
2025-06-29 18:00 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-29 18:00 | INFO     | DatabaseReset.run_reset: 开始执行 XentZ 因子动物园数据库重置
2025-06-29 18:00 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-29 18:00 | INFO     | DatabaseReset.validate_files: 步骤1: 验证文件存在性...
2025-06-29 18:00 | INFO     | DatabaseReset.validate_files: Schema文件存在: D:\myquant\XentZ\factorzoo\schema.sql
2025-06-29 18:00 | INFO     | DatabaseReset.backup_database: 步骤2: 备份现有数据库...
2025-06-29 18:00 | INFO     | DatabaseReset.backup_database: 数据库已备份到: D:\myquant\FZoo\database\factorzoo.sqlite.backup_20250629_180025
2025-06-29 18:00 | INFO     | DatabaseReset.get_existing_tables: 步骤3: 获取现有表列表...
2025-06-29 18:00 | INFO     | DatabaseReset.get_existing_tables: 发现现有表 (9个): factor_batches, factor_categories, factor_evaluations, factor_pool_members, factor_pools, factor_relationships, factor_tags, factors, universes
2025-06-29 18:00 | INFO     | DatabaseReset.drop_all_tables: 步骤4: 删除现有表 (9个)...
2025-06-29 18:00 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_batches
2025-06-29 18:00 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_categories
2025-06-29 18:00 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_evaluations
2025-06-29 18:00 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_pool_members
2025-06-29 18:00 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_pools
2025-06-29 18:00 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_relationships
2025-06-29 18:00 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_tags
2025-06-29 18:00 | INFO     | DatabaseReset.drop_all_tables:   删除表: factors
2025-06-29 18:00 | INFO     | DatabaseReset.drop_all_tables:   删除表: universes
2025-06-29 18:00 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_active_factors
2025-06-29 18:00 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_batch_statistics
2025-06-29 18:00 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_factor_performance_summary
2025-06-29 18:00 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_performance_stats
2025-06-29 18:00 | INFO     | DatabaseReset.drop_all_tables: 成功删除 9 个表, 4 个视图, 0 个索引
2025-06-29 18:00 | INFO     | DatabaseReset.execute_schema: 步骤5: 执行schema.sql重建表结构...
2025-06-29 18:00 | INFO     | DatabaseReset.execute_schema: 读取schema文件，长度: 24886 字符
2025-06-29 18:00 | INFO     | DatabaseReset.execute_schema: Schema执行完成
2025-06-29 18:00 | INFO     | DatabaseReset.verify_tables: 步骤6: 验证表创建结果...
2025-06-29 18:00 | INFO     | DatabaseReset.verify_tables: 创建完成统计:
2025-06-29 18:00 | INFO     | DatabaseReset.verify_tables:   - 表 (9个): factor_batches, factor_categories, factor_evaluations, factor_pool_members, factor_pools, factor_relationships, factor_tags, factors, universes
2025-06-29 18:00 | INFO     | DatabaseReset.verify_tables:   - 视图 (4个): v_active_factors, v_batch_statistics, v_factor_performance_summary, v_performance_stats
2025-06-29 18:00 | INFO     | DatabaseReset.verify_tables:   - 索引 (25个): idx_evaluations_factor_id, idx_evaluations_method, idx_evaluations_period, idx_evaluations_sharpe, idx_factor_tags_tag_name, idx_factor_tags_tag_type, idx_factors_batch_id, idx_factors_computation_time, idx_factors_creation_date, idx_factors_creation_method, idx_factors_factor_type, idx_factors_generation_tool, idx_factors_market_type, idx_factors_pipeline_mode, idx_factors_pipeline_step, idx_factors_primary_category, idx_factors_resource_intensity, idx_factors_status, idx_factors_target_label, idx_factors_unique_expression, idx_pool_members_factor_id, idx_pool_members_pool_id, idx_pool_members_status, idx_universes_current, idx_universes_effective_date
2025-06-29 18:00 | INFO     | DatabaseReset.verify_tables: 所有核心表创建成功
2025-06-29 18:00 | INFO     | DatabaseReset.insert_default_data: 步骤7: 插入默认数据...
2025-06-29 18:00 | INFO     | DatabaseReset.insert_default_data: 默认数据插入完成
2025-06-29 18:00 | INFO     | DatabaseReset.insert_default_data:   - 因子分类: 10 个
2025-06-29 18:00 | INFO     | DatabaseReset.insert_default_data:   - 股票池: 2 个
2025-06-29 18:00 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-29 18:00 | INFO     | DatabaseReset.run_reset: 数据库重置完成！
2025-06-29 18:00 | INFO     | DatabaseReset.run_reset: 数据库路径: D:\myquant\FZoo\database\factorzoo.sqlite
2025-06-29 18:00 | INFO     | DatabaseReset.run_reset: 备份路径: D:\myquant\FZoo\database\factorzoo.sqlite.backup_20250629_180025
2025-06-29 18:00 | INFO     | DatabaseReset.run_reset: 创建统计: 9表 + 4视图 + 25索引
2025-06-29 18:00 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-29 18:01 | INFO     | DatabaseReset.__init__: 数据库路径: D:\myquant\FZoo\database\factorzoo.sqlite
2025-06-29 18:01 | INFO     | DatabaseReset.__init__: Schema文件路径: d:\myquant\XentZ\factorzoo\schema.sql
2025-06-29 18:01 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-29 18:01 | INFO     | DatabaseReset.run_reset: 开始执行 XentZ 因子动物园数据库重置
2025-06-29 18:01 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-29 18:01 | INFO     | DatabaseReset.validate_files: 步骤1: 验证文件存在性...
2025-06-29 18:01 | INFO     | DatabaseReset.validate_files: Schema文件存在: d:\myquant\XentZ\factorzoo\schema.sql
2025-06-29 18:01 | INFO     | DatabaseReset.backup_database: 步骤2: 备份现有数据库...
2025-06-29 18:01 | INFO     | DatabaseReset.backup_database: 数据库已备份到: D:\myquant\FZoo\database\factorzoo.sqlite.backup_20250629_180144
2025-06-29 18:01 | INFO     | DatabaseReset.get_existing_tables: 步骤3: 获取现有表列表...
2025-06-29 18:01 | INFO     | DatabaseReset.get_existing_tables: 发现现有表 (9个): factor_batches, factor_categories, factor_evaluations, factor_pool_members, factor_pools, factor_relationships, factor_tags, factors, universes
2025-06-29 18:01 | INFO     | DatabaseReset.drop_all_tables: 步骤4: 删除现有表 (9个)...
2025-06-29 18:01 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_batches
2025-06-29 18:01 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_categories
2025-06-29 18:01 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_evaluations
2025-06-29 18:01 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_pool_members
2025-06-29 18:01 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_pools
2025-06-29 18:01 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_relationships
2025-06-29 18:01 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_tags
2025-06-29 18:01 | INFO     | DatabaseReset.drop_all_tables:   删除表: factors
2025-06-29 18:01 | INFO     | DatabaseReset.drop_all_tables:   删除表: universes
2025-06-29 18:01 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_active_factors
2025-06-29 18:01 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_batch_statistics
2025-06-29 18:01 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_factor_performance_summary
2025-06-29 18:01 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_performance_stats
2025-06-29 18:01 | INFO     | DatabaseReset.drop_all_tables: 成功删除 9 个表, 4 个视图, 0 个索引
2025-06-29 18:01 | INFO     | DatabaseReset.execute_schema: 步骤5: 执行schema.sql重建表结构...
2025-06-29 18:01 | INFO     | DatabaseReset.execute_schema: 读取schema文件，长度: 24886 字符
2025-06-29 18:01 | INFO     | DatabaseReset.execute_schema: Schema执行完成
2025-06-29 18:01 | INFO     | DatabaseReset.verify_tables: 步骤6: 验证表创建结果...
2025-06-29 18:01 | INFO     | DatabaseReset.verify_tables: 创建完成统计:
2025-06-29 18:01 | INFO     | DatabaseReset.verify_tables:   - 表 (9个): factor_batches, factor_categories, factor_evaluations, factor_pool_members, factor_pools, factor_relationships, factor_tags, factors, universes
2025-06-29 18:01 | INFO     | DatabaseReset.verify_tables:   - 视图 (4个): v_active_factors, v_batch_statistics, v_factor_performance_summary, v_performance_stats
2025-06-29 18:01 | INFO     | DatabaseReset.verify_tables:   - 索引 (25个): idx_evaluations_factor_id, idx_evaluations_method, idx_evaluations_period, idx_evaluations_sharpe, idx_factor_tags_tag_name, idx_factor_tags_tag_type, idx_factors_batch_id, idx_factors_computation_time, idx_factors_creation_date, idx_factors_creation_method, idx_factors_factor_type, idx_factors_generation_tool, idx_factors_market_type, idx_factors_pipeline_mode, idx_factors_pipeline_step, idx_factors_primary_category, idx_factors_resource_intensity, idx_factors_status, idx_factors_target_label, idx_factors_unique_expression, idx_pool_members_factor_id, idx_pool_members_pool_id, idx_pool_members_status, idx_universes_current, idx_universes_effective_date
2025-06-29 18:01 | INFO     | DatabaseReset.verify_tables: 所有核心表创建成功
2025-06-29 18:01 | INFO     | DatabaseReset.insert_default_data: 步骤7: 插入默认数据...
2025-06-29 18:01 | INFO     | DatabaseReset.insert_default_data: 默认数据插入完成
2025-06-29 18:01 | INFO     | DatabaseReset.insert_default_data:   - 因子分类: 10 个
2025-06-29 18:01 | INFO     | DatabaseReset.insert_default_data:   - 股票池: 2 个
2025-06-29 18:01 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-29 18:01 | INFO     | DatabaseReset.run_reset: 数据库重置完成！
2025-06-29 18:01 | INFO     | DatabaseReset.run_reset: 数据库路径: D:\myquant\FZoo\database\factorzoo.sqlite
2025-06-29 18:01 | INFO     | DatabaseReset.run_reset: 备份路径: D:\myquant\FZoo\database\factorzoo.sqlite.backup_20250629_180144
2025-06-29 18:01 | INFO     | DatabaseReset.run_reset: 创建统计: 9表 + 4视图 + 25索引
2025-06-29 18:01 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-29 18:02 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:02 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-29 18:02 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-29 18:02 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-29 18:02 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-29 18:02 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-29 18:02 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-29 18:02 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-29 18:02 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-29 18:02 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-29 18:02 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-29 18:02 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-29 18:02 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-29 18:02 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-29 18:02 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-29 18:02 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-29 18:02 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-29 18:02 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:02 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-29 18:03 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 3.59 seconds (00:00:03)
2025-06-29 18:03 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 1.00s user, 0.34s system
2025-06-29 18:03 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 63.43MB
2025-06-29 18:03 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 completed successfully
2025-06-29 18:03 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:03 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-29 18:03 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-29 18:03 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R01 took 0.05 seconds (00:00:00)
2025-06-29 18:03 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R01 CPU usage: 0.05s user, 0.00s system
2025-06-29 18:03 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R01 memory delta: 2.99MB
2025-06-29 18:03 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R01 completed successfully
2025-06-29 18:03 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:03 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R01 took 0.02 seconds (00:00:00)
2025-06-29 18:03 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R01 CPU usage: 0.05s user, 0.00s system
2025-06-29 18:03 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R01 memory delta: 0.02MB
2025-06-29 18:03 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R01 completed successfully
2025-06-29 18:03 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:03 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-06-29 18:03 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-06-29 18:03 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R01 memory delta: 0.00MB
2025-06-29 18:03 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R01 completed successfully
2025-06-29 18:03 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:03 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R01 took 8.77 seconds (00:00:08)
2025-06-29 18:03 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R01 CPU usage: 8.34s user, 0.97s system
2025-06-29 18:03 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R01 memory delta: 293.08MB
2025-06-29 18:03 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R01 completed successfully
2025-06-29 18:03 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:03 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 18:03 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 18:03 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R01 took 0.00 seconds (00:00:00)
2025-06-29 18:03 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-06-29 18:03 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R01 memory delta: 0.05MB
2025-06-29 18:03 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R01 completed successfully
2025-06-29 18:03 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:03 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-29 18:03 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 1.51 seconds (00:00:01)
2025-06-29 18:03 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.56s user, 0.06s system
2025-06-29 18:03 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 3.07MB
2025-06-29 18:03 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 completed successfully
2025-06-29 18:03 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:03 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-29 18:03 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-29 18:03 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R02 took 0.05 seconds (00:00:00)
2025-06-29 18:03 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R02 CPU usage: 0.05s user, 0.00s system
2025-06-29 18:03 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R02 memory delta: 4.23MB
2025-06-29 18:03 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R02 completed successfully
2025-06-29 18:03 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:03 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R02 took 0.03 seconds (00:00:00)
2025-06-29 18:03 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R02 CPU usage: 0.06s user, 0.00s system
2025-06-29 18:03 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R02 memory delta: -2.09MB
2025-06-29 18:03 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R02 completed successfully
2025-06-29 18:03 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:03 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R02 took 0.01 seconds (00:00:00)
2025-06-29 18:03 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-29 18:03 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-29 18:03 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R02 completed successfully
2025-06-29 18:03 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:03 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R02 took 0.07 seconds (00:00:00)
2025-06-29 18:03 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R02 CPU usage: 0.34s user, 0.08s system
2025-06-29 18:03 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R02 memory delta: 28.17MB
2025-06-29 18:03 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R02 completed successfully
2025-06-29 18:03 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:03 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 18:03 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 18:03 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R02 took 0.00 seconds (00:00:00)
2025-06-29 18:03 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-29 18:03 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-29 18:03 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R02 completed successfully
2025-06-29 18:03 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:03 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-29 18:03 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 0.60 seconds (00:00:00)
2025-06-29 18:03 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.48s user, 0.06s system
2025-06-29 18:03 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 0.52MB
2025-06-29 18:03 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 completed successfully
2025-06-29 18:03 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:03 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-29 18:03 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-29 18:03 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R03 took 0.05 seconds (00:00:00)
2025-06-29 18:03 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R03 CPU usage: 0.05s user, 0.00s system
2025-06-29 18:03 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R03 memory delta: -2.06MB
2025-06-29 18:03 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R03 completed successfully
2025-06-29 18:03 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:03 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R03 took 0.02 seconds (00:00:00)
2025-06-29 18:03 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R03 CPU usage: 0.03s user, 0.00s system
2025-06-29 18:03 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-29 18:03 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R03 completed successfully
2025-06-29 18:03 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:03 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R03 took 0.01 seconds (00:00:00)
2025-06-29 18:03 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R03 CPU usage: 0.00s user, 0.00s system
2025-06-29 18:03 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R03 memory delta: 2.11MB
2025-06-29 18:03 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R03 completed successfully
2025-06-29 18:03 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:03 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R03 took 0.07 seconds (00:00:00)
2025-06-29 18:03 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R03 CPU usage: 0.27s user, 0.09s system
2025-06-29 18:03 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R03 memory delta: 17.06MB
2025-06-29 18:03 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R03 completed successfully
2025-06-29 18:03 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:03 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 18:03 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 18:03 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R03 took 0.00 seconds (00:00:00)
2025-06-29 18:03 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R03 CPU usage: 0.00s user, 0.00s system
2025-06-29 18:03 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-29 18:03 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R03 completed successfully
2025-06-29 18:03 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-29 18:03 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-29 18:03 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:03 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-06-29 18:03 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-06-29 18:03 | INFO     | ResMonitor.end_timer: CALC_F_VALUES_SH510050 took 0.00 seconds (00:00:00)
2025-06-29 18:03 | INFO     | ResMonitor.end_timer_cpu: CALC_F_VALUES_SH510050 CPU usage: 0.02s user, 0.00s system
2025-06-29 18:03 | INFO     | ResMonitor.end_memory_monitor: CALC_F_VALUES_SH510050 memory delta: 0.00MB
2025-06-29 18:03 | INFO     | FactorMonitorContext.__exit__: CALC_F_VALUES_SH510050 completed successfully
2025-06-29 18:03 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:03 | INFO     | FactorLoader.get_fct_df: 开始计算2个因子表达式
2025-06-29 18:03 | INFO     | FactorLoader.get_fct_df: 返回结果: 2个指定因子
2025-06-29 18:03 | INFO     | ResMonitor.end_timer: CALC_PJ_VALUES_SH510050 took 0.00 seconds (00:00:00)
2025-06-29 18:03 | INFO     | ResMonitor.end_timer_cpu: CALC_PJ_VALUES_SH510050 CPU usage: 0.00s user, 0.00s system
2025-06-29 18:03 | INFO     | ResMonitor.end_memory_monitor: CALC_PJ_VALUES_SH510050 memory delta: 0.00MB
2025-06-29 18:03 | INFO     | FactorMonitorContext.__exit__: CALC_PJ_VALUES_SH510050 completed successfully
2025-06-29 18:03 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:03 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: GP_SH510050_20250629_L0_366378
2025-06-29 18:03 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: GP_SH510050_20250629_L0_366378 耗时177.1ms
2025-06-29 18:03 | INFO     | ResMonitor.end_timer: PERSIST_SH510050 took 0.18 seconds (00:00:00)
2025-06-29 18:03 | INFO     | ResMonitor.end_timer_cpu: PERSIST_SH510050 CPU usage: 0.05s user, 0.02s system
2025-06-29 18:03 | INFO     | ResMonitor.end_memory_monitor: PERSIST_SH510050 memory delta: 6.71MB
2025-06-29 18:03 | INFO     | FactorMonitorContext.__exit__: PERSIST_SH510050 completed successfully
2025-06-29 18:03 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:03 | INFO     | ResMonitor.end_timer: F_GP_GP_SH510050_20250629_L0_366378_label_1_001 took 0.07 seconds (00:00:00)
2025-06-29 18:03 | INFO     | ResMonitor.end_timer_cpu: F_GP_GP_SH510050_20250629_L0_366378_label_1_001 CPU usage: 0.00s user, 0.02s system
2025-06-29 18:03 | INFO     | ResMonitor.end_memory_monitor: F_GP_GP_SH510050_20250629_L0_366378_label_1_001 memory delta: 0.01MB
2025-06-29 18:03 | INFO     | FactorMonitorContext.__exit__: F_GP_GP_SH510050_20250629_L0_366378_label_1_001 completed successfully
2025-06-29 18:03 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:03 | INFO     | ResMonitor.end_timer: F_GP_PJ_GP_SH510050_20250629_L0_PJ_366378_label_1_001 took 0.10 seconds (00:00:00)
2025-06-29 18:03 | INFO     | ResMonitor.end_timer_cpu: F_GP_PJ_GP_SH510050_20250629_L0_PJ_366378_label_1_001 CPU usage: 0.00s user, 0.02s system
2025-06-29 18:03 | INFO     | ResMonitor.end_memory_monitor: F_GP_PJ_GP_SH510050_20250629_L0_PJ_366378_label_1_001 memory delta: 0.00MB
2025-06-29 18:03 | INFO     | FactorMonitorContext.__exit__: F_GP_PJ_GP_SH510050_20250629_L0_PJ_366378_label_1_001 completed successfully
2025-06-29 18:03 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:03 | INFO     | ResMonitor.end_timer: F_GP_PJ_GP_SH510050_20250629_L0_PJ_366378_label_1_002 took 0.10 seconds (00:00:00)
2025-06-29 18:03 | INFO     | ResMonitor.end_timer_cpu: F_GP_PJ_GP_SH510050_20250629_L0_PJ_366378_label_1_002 CPU usage: 0.02s user, 0.00s system
2025-06-29 18:03 | INFO     | ResMonitor.end_memory_monitor: F_GP_PJ_GP_SH510050_20250629_L0_PJ_366378_label_1_002 memory delta: 2.11MB
2025-06-29 18:03 | INFO     | FactorMonitorContext.__exit__: F_GP_PJ_GP_SH510050_20250629_L0_PJ_366378_label_1_002 completed successfully
2025-06-29 18:03 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250629_L0_366378 took 15.83 seconds (00:00:15)
2025-06-29 18:03 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250629_L0_366378 CPU usage: 11.47s user, 1.66s system
2025-06-29 18:03 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250629_L0_366378 memory delta: 420.18MB
2025-06-29 18:03 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250629_L0_366378 completed successfully
2025-06-29 18:05 | INFO     | DatabaseReset.__init__: 数据库路径: D:\myquant\FZoo\database\factorzoo.sqlite
2025-06-29 18:05 | INFO     | DatabaseReset.__init__: Schema文件路径: D:\myquant\XentZ\factorzoo\schema.sql
2025-06-29 18:05 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-29 18:05 | INFO     | DatabaseReset.run_reset: 开始执行 XentZ 因子动物园数据库重置
2025-06-29 18:05 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-29 18:05 | INFO     | DatabaseReset.validate_files: 步骤1: 验证文件存在性...
2025-06-29 18:05 | INFO     | DatabaseReset.validate_files: Schema文件存在: D:\myquant\XentZ\factorzoo\schema.sql
2025-06-29 18:05 | INFO     | DatabaseReset.backup_database: 步骤2: 备份现有数据库...
2025-06-29 18:05 | INFO     | DatabaseReset.backup_database: 数据库已备份到: D:\myquant\FZoo\database\factorzoo.sqlite.backup_20250629_180544
2025-06-29 18:05 | INFO     | DatabaseReset.get_existing_tables: 步骤3: 获取现有表列表...
2025-06-29 18:05 | INFO     | DatabaseReset.get_existing_tables: 发现现有表 (9个): factor_batches, factor_categories, factor_evaluations, factor_pool_members, factor_pools, factor_relationships, factor_tags, factors, universes
2025-06-29 18:05 | INFO     | DatabaseReset.drop_all_tables: 步骤4: 删除现有表 (9个)...
2025-06-29 18:05 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_batches
2025-06-29 18:05 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_categories
2025-06-29 18:05 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_evaluations
2025-06-29 18:05 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_pool_members
2025-06-29 18:05 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_pools
2025-06-29 18:05 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_relationships
2025-06-29 18:05 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_tags
2025-06-29 18:05 | INFO     | DatabaseReset.drop_all_tables:   删除表: factors
2025-06-29 18:05 | INFO     | DatabaseReset.drop_all_tables:   删除表: universes
2025-06-29 18:05 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_active_factors
2025-06-29 18:05 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_batch_statistics
2025-06-29 18:05 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_factor_performance_summary
2025-06-29 18:05 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_performance_stats
2025-06-29 18:05 | INFO     | DatabaseReset.drop_all_tables: 成功删除 9 个表, 4 个视图, 0 个索引
2025-06-29 18:05 | INFO     | DatabaseReset.execute_schema: 步骤5: 执行schema.sql重建表结构...
2025-06-29 18:05 | INFO     | DatabaseReset.execute_schema: 读取schema文件，长度: 26860 字符
2025-06-29 18:05 | INFO     | DatabaseReset.execute_schema: Schema执行完成
2025-06-29 18:05 | INFO     | DatabaseReset.verify_tables: 步骤6: 验证表创建结果...
2025-06-29 18:05 | INFO     | DatabaseReset.verify_tables: 创建完成统计:
2025-06-29 18:05 | INFO     | DatabaseReset.verify_tables:   - 表 (11个): factor_batches, factor_categories, factor_evaluations, factor_performance_logs, factor_pool_members, factor_pools, factor_relationships, factor_tags, factor_values, factors, universes
2025-06-29 18:05 | INFO     | DatabaseReset.verify_tables:   - 视图 (4个): v_active_factors, v_batch_statistics, v_factor_performance_summary, v_performance_stats
2025-06-29 18:05 | INFO     | DatabaseReset.verify_tables:   - 索引 (34个): idx_evaluations_factor_id, idx_evaluations_method, idx_evaluations_period, idx_evaluations_sharpe, idx_factor_performance_logs_batch_id, idx_factor_performance_logs_memory, idx_factor_performance_logs_operation_type, idx_factor_performance_logs_start_time, idx_factor_performance_logs_symbol, idx_factor_performance_logs_total_time, idx_factor_tags_tag_name, idx_factor_tags_tag_type, idx_factor_values_date, idx_factor_values_factor_id, idx_factor_values_symbol, idx_factors_batch_id, idx_factors_computation_time, idx_factors_creation_date, idx_factors_creation_method, idx_factors_factor_type, idx_factors_generation_tool, idx_factors_market_type, idx_factors_pipeline_mode, idx_factors_pipeline_step, idx_factors_primary_category, idx_factors_resource_intensity, idx_factors_status, idx_factors_target_label, idx_factors_unique_expression, idx_pool_members_factor_id, idx_pool_members_pool_id, idx_pool_members_status, idx_universes_current, idx_universes_effective_date
2025-06-29 18:05 | INFO     | DatabaseReset.verify_tables: 所有核心表创建成功
2025-06-29 18:05 | INFO     | DatabaseReset.insert_default_data: 步骤7: 插入默认数据...
2025-06-29 18:05 | INFO     | DatabaseReset.insert_default_data: 默认数据插入完成
2025-06-29 18:05 | INFO     | DatabaseReset.insert_default_data:   - 因子分类: 10 个
2025-06-29 18:05 | INFO     | DatabaseReset.insert_default_data:   - 股票池: 2 个
2025-06-29 18:05 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-29 18:05 | INFO     | DatabaseReset.run_reset: 数据库重置完成！
2025-06-29 18:05 | INFO     | DatabaseReset.run_reset: 数据库路径: D:\myquant\FZoo\database\factorzoo.sqlite
2025-06-29 18:05 | INFO     | DatabaseReset.run_reset: 备份路径: D:\myquant\FZoo\database\factorzoo.sqlite.backup_20250629_180544
2025-06-29 18:05 | INFO     | DatabaseReset.run_reset: 创建统计: 11表 + 4视图 + 34索引
2025-06-29 18:05 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-29 18:10 | INFO     | DatabaseReset.__init__: 数据库路径: D:\myquant\FZoo\database\factorzoo.sqlite
2025-06-29 18:10 | INFO     | DatabaseReset.__init__: Schema文件路径: D:\myquant\XentZ\factorzoo\schema.sql
2025-06-29 18:10 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-29 18:10 | INFO     | DatabaseReset.run_reset: 开始执行 XentZ 因子动物园数据库重置
2025-06-29 18:10 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-29 18:10 | INFO     | DatabaseReset.validate_files: 步骤1: 验证文件存在性...
2025-06-29 18:10 | INFO     | DatabaseReset.validate_files: Schema文件存在: D:\myquant\XentZ\factorzoo\schema.sql
2025-06-29 18:10 | INFO     | DatabaseReset.backup_database: 步骤2: 备份现有数据库...
2025-06-29 18:10 | INFO     | DatabaseReset.backup_database: 数据库已备份到: D:\myquant\FZoo\database\factorzoo.sqlite.backup_20250629_181049
2025-06-29 18:10 | INFO     | DatabaseReset.get_existing_tables: 步骤3: 获取现有表列表...
2025-06-29 18:10 | INFO     | DatabaseReset.get_existing_tables: 发现现有表 (11个): factor_batches, factor_categories, factor_evaluations, factor_performance_logs, factor_pool_members, factor_pools, factor_relationships, factor_tags, factor_values, factors, universes
2025-06-29 18:10 | INFO     | DatabaseReset.drop_all_tables: 步骤4: 删除现有表 (11个)...
2025-06-29 18:10 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_batches
2025-06-29 18:10 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_categories
2025-06-29 18:10 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_evaluations
2025-06-29 18:10 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_performance_logs
2025-06-29 18:10 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_pool_members
2025-06-29 18:10 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_pools
2025-06-29 18:10 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_relationships
2025-06-29 18:10 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_tags
2025-06-29 18:10 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_values
2025-06-29 18:10 | INFO     | DatabaseReset.drop_all_tables:   删除表: factors
2025-06-29 18:10 | INFO     | DatabaseReset.drop_all_tables:   删除表: universes
2025-06-29 18:10 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_active_factors
2025-06-29 18:10 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_batch_statistics
2025-06-29 18:10 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_factor_performance_summary
2025-06-29 18:10 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_performance_stats
2025-06-29 18:10 | INFO     | DatabaseReset.drop_all_tables: 成功删除 11 个表, 4 个视图, 0 个索引
2025-06-29 18:10 | INFO     | DatabaseReset.execute_schema: 步骤5: 执行schema.sql重建表结构...
2025-06-29 18:10 | INFO     | DatabaseReset.execute_schema: 读取schema文件，长度: 26452 字符
2025-06-29 18:10 | INFO     | DatabaseReset.execute_schema: Schema执行完成
2025-06-29 18:10 | INFO     | DatabaseReset.verify_tables: 步骤6: 验证表创建结果...
2025-06-29 18:10 | INFO     | DatabaseReset.verify_tables: 创建完成统计:
2025-06-29 18:10 | INFO     | DatabaseReset.verify_tables:   - 表 (11个): factor_batches, factor_categories, factor_evaluations, factor_performance_logs, factor_pool_members, factor_pools, factor_relationships, factor_tags, factor_values, factors, universes
2025-06-29 18:10 | INFO     | DatabaseReset.verify_tables:   - 视图 (4个): v_active_factors, v_batch_statistics, v_factor_performance_summary, v_performance_stats
2025-06-29 18:10 | INFO     | DatabaseReset.verify_tables:   - 索引 (32个): idx_evaluations_factor_id, idx_evaluations_method, idx_evaluations_period, idx_evaluations_sharpe, idx_factor_performance_logs_batch_id, idx_factor_performance_logs_memory, idx_factor_performance_logs_operation_type, idx_factor_performance_logs_start_time, idx_factor_performance_logs_total_time, idx_factor_tags_tag_name, idx_factor_tags_tag_type, idx_factor_values_date, idx_factor_values_factor_id, idx_factors_batch_id, idx_factors_computation_time, idx_factors_creation_date, idx_factors_creation_method, idx_factors_factor_type, idx_factors_generation_tool, idx_factors_market_type, idx_factors_pipeline_mode, idx_factors_pipeline_step, idx_factors_primary_category, idx_factors_resource_intensity, idx_factors_status, idx_factors_target_label, idx_factors_unique_expression, idx_pool_members_factor_id, idx_pool_members_pool_id, idx_pool_members_status, idx_universes_current, idx_universes_effective_date
2025-06-29 18:10 | INFO     | DatabaseReset.verify_tables: 所有核心表创建成功
2025-06-29 18:10 | INFO     | DatabaseReset.insert_default_data: 步骤7: 插入默认数据...
2025-06-29 18:10 | INFO     | DatabaseReset.insert_default_data: 默认数据插入完成
2025-06-29 18:10 | INFO     | DatabaseReset.insert_default_data:   - 因子分类: 10 个
2025-06-29 18:10 | INFO     | DatabaseReset.insert_default_data:   - 股票池: 2 个
2025-06-29 18:10 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-29 18:10 | INFO     | DatabaseReset.run_reset: 数据库重置完成！
2025-06-29 18:10 | INFO     | DatabaseReset.run_reset: 数据库路径: D:\myquant\FZoo\database\factorzoo.sqlite
2025-06-29 18:10 | INFO     | DatabaseReset.run_reset: 备份路径: D:\myquant\FZoo\database\factorzoo.sqlite.backup_20250629_181049
2025-06-29 18:10 | INFO     | DatabaseReset.run_reset: 创建统计: 11表 + 4视图 + 32索引
2025-06-29 18:10 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-29 18:18 | INFO     | DatabaseReset.__init__: 数据库路径: D:\myquant\FZoo\database\factorzoo.sqlite
2025-06-29 18:18 | INFO     | DatabaseReset.__init__: Schema文件路径: d:\myquant\XentZ\factorzoo\schema.sql
2025-06-29 18:18 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-29 18:18 | INFO     | DatabaseReset.run_reset: 开始执行 XentZ 因子动物园数据库重置
2025-06-29 18:18 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-29 18:18 | INFO     | DatabaseReset.validate_files: 步骤1: 验证文件存在性...
2025-06-29 18:18 | INFO     | DatabaseReset.validate_files: Schema文件存在: d:\myquant\XentZ\factorzoo\schema.sql
2025-06-29 18:18 | INFO     | DatabaseReset.backup_database: 步骤2: 备份现有数据库...
2025-06-29 18:18 | INFO     | DatabaseReset.backup_database: 数据库已备份到: D:\myquant\FZoo\database\factorzoo.sqlite.backup_20250629_181803
2025-06-29 18:18 | INFO     | DatabaseReset.get_existing_tables: 步骤3: 获取现有表列表...
2025-06-29 18:18 | INFO     | DatabaseReset.get_existing_tables: 发现现有表 (11个): factor_batches, factor_categories, factor_evaluations, factor_performance_logs, factor_pool_members, factor_pools, factor_relationships, factor_tags, factor_values, factors, universes
2025-06-29 18:18 | INFO     | DatabaseReset.drop_all_tables: 步骤4: 删除现有表 (11个)...
2025-06-29 18:18 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_batches
2025-06-29 18:18 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_categories
2025-06-29 18:18 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_evaluations
2025-06-29 18:18 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_performance_logs
2025-06-29 18:18 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_pool_members
2025-06-29 18:18 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_pools
2025-06-29 18:18 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_relationships
2025-06-29 18:18 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_tags
2025-06-29 18:18 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_values
2025-06-29 18:18 | INFO     | DatabaseReset.drop_all_tables:   删除表: factors
2025-06-29 18:18 | INFO     | DatabaseReset.drop_all_tables:   删除表: universes
2025-06-29 18:18 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_active_factors
2025-06-29 18:18 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_batch_statistics
2025-06-29 18:18 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_factor_performance_summary
2025-06-29 18:18 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_performance_stats
2025-06-29 18:18 | INFO     | DatabaseReset.drop_all_tables: 成功删除 11 个表, 4 个视图, 0 个索引
2025-06-29 18:18 | INFO     | DatabaseReset.execute_schema: 步骤5: 执行schema.sql重建表结构...
2025-06-29 18:18 | INFO     | DatabaseReset.execute_schema: 读取schema文件，长度: 26452 字符
2025-06-29 18:18 | INFO     | DatabaseReset.execute_schema: Schema执行完成
2025-06-29 18:18 | INFO     | DatabaseReset.verify_tables: 步骤6: 验证表创建结果...
2025-06-29 18:18 | INFO     | DatabaseReset.verify_tables: 创建完成统计:
2025-06-29 18:18 | INFO     | DatabaseReset.verify_tables:   - 表 (11个): factor_batches, factor_categories, factor_evaluations, factor_performance_logs, factor_pool_members, factor_pools, factor_relationships, factor_tags, factor_values, factors, universes
2025-06-29 18:18 | INFO     | DatabaseReset.verify_tables:   - 视图 (4个): v_active_factors, v_batch_statistics, v_factor_performance_summary, v_performance_stats
2025-06-29 18:18 | INFO     | DatabaseReset.verify_tables:   - 索引 (32个): idx_evaluations_factor_id, idx_evaluations_method, idx_evaluations_period, idx_evaluations_sharpe, idx_factor_performance_logs_batch_id, idx_factor_performance_logs_memory, idx_factor_performance_logs_operation_type, idx_factor_performance_logs_start_time, idx_factor_performance_logs_total_time, idx_factor_tags_tag_name, idx_factor_tags_tag_type, idx_factor_values_date, idx_factor_values_factor_id, idx_factors_batch_id, idx_factors_computation_time, idx_factors_creation_date, idx_factors_creation_method, idx_factors_factor_type, idx_factors_generation_tool, idx_factors_market_type, idx_factors_pipeline_mode, idx_factors_pipeline_step, idx_factors_primary_category, idx_factors_resource_intensity, idx_factors_status, idx_factors_target_label, idx_factors_unique_expression, idx_pool_members_factor_id, idx_pool_members_pool_id, idx_pool_members_status, idx_universes_current, idx_universes_effective_date
2025-06-29 18:18 | INFO     | DatabaseReset.verify_tables: 所有核心表创建成功
2025-06-29 18:18 | INFO     | DatabaseReset.insert_default_data: 步骤7: 插入默认数据...
2025-06-29 18:18 | INFO     | DatabaseReset.insert_default_data: 默认数据插入完成
2025-06-29 18:18 | INFO     | DatabaseReset.insert_default_data:   - 因子分类: 10 个
2025-06-29 18:18 | INFO     | DatabaseReset.insert_default_data:   - 股票池: 2 个
2025-06-29 18:18 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-29 18:18 | INFO     | DatabaseReset.run_reset: 数据库重置完成！
2025-06-29 18:18 | INFO     | DatabaseReset.run_reset: 数据库路径: D:\myquant\FZoo\database\factorzoo.sqlite
2025-06-29 18:18 | INFO     | DatabaseReset.run_reset: 备份路径: D:\myquant\FZoo\database\factorzoo.sqlite.backup_20250629_181803
2025-06-29 18:18 | INFO     | DatabaseReset.run_reset: 创建统计: 11表 + 4视图 + 32索引
2025-06-29 18:18 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-29 18:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:18 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-29 18:18 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-29 18:18 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-29 18:18 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-29 18:18 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-29 18:18 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-29 18:18 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-29 18:18 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-29 18:18 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-29 18:18 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-29 18:18 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-29 18:18 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-29 18:18 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-29 18:18 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-29 18:18 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-29 18:18 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-29 18:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:18 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-29 18:18 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 3.91 seconds (00:00:03)
2025-06-29 18:18 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 2.14s user, 1.20s system
2025-06-29 18:18 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 331.88MB
2025-06-29 18:18 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 completed successfully
2025-06-29 18:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:18 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-29 18:18 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-29 18:18 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R01 took 0.05 seconds (00:00:00)
2025-06-29 18:18 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R01 CPU usage: 0.05s user, 0.00s system
2025-06-29 18:18 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R01 memory delta: 1.90MB
2025-06-29 18:18 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R01 completed successfully
2025-06-29 18:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:18 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R01 took 0.03 seconds (00:00:00)
2025-06-29 18:18 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R01 CPU usage: 0.03s user, 0.00s system
2025-06-29 18:18 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R01 memory delta: 0.39MB
2025-06-29 18:18 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R01 completed successfully
2025-06-29 18:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:18 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-06-29 18:18 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R01 CPU usage: 0.02s user, 0.00s system
2025-06-29 18:18 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R01 memory delta: -1.98MB
2025-06-29 18:18 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R01 completed successfully
2025-06-29 18:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:18 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R01 took 8.78 seconds (00:00:08)
2025-06-29 18:18 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R01 CPU usage: 8.77s user, 1.08s system
2025-06-29 18:18 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R01 memory delta: 359.55MB
2025-06-29 18:18 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R01 completed successfully
2025-06-29 18:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:18 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 18:18 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 18:18 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-06-29 18:18 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-06-29 18:18 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R01 memory delta: 0.05MB
2025-06-29 18:18 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R01 completed successfully
2025-06-29 18:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:18 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-29 18:18 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 0.60 seconds (00:00:00)
2025-06-29 18:18 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.47s user, 0.06s system
2025-06-29 18:18 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 4.60MB
2025-06-29 18:18 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 completed successfully
2025-06-29 18:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:18 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-29 18:18 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-29 18:18 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R02 took 0.05 seconds (00:00:00)
2025-06-29 18:18 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R02 CPU usage: 0.05s user, 0.02s system
2025-06-29 18:18 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R02 memory delta: -0.43MB
2025-06-29 18:18 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R02 completed successfully
2025-06-29 18:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:18 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R02 took 0.02 seconds (00:00:00)
2025-06-29 18:18 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R02 CPU usage: 0.03s user, 0.00s system
2025-06-29 18:18 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R02 memory delta: -2.11MB
2025-06-29 18:18 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R02 completed successfully
2025-06-29 18:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:18 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R02 took 0.01 seconds (00:00:00)
2025-06-29 18:18 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R02 CPU usage: 0.02s user, 0.00s system
2025-06-29 18:18 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R02 memory delta: 1.04MB
2025-06-29 18:18 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R02 completed successfully
2025-06-29 18:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:18 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R02 took 0.08 seconds (00:00:00)
2025-06-29 18:18 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R02 CPU usage: 0.06s user, 0.00s system
2025-06-29 18:18 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R02 memory delta: 19.36MB
2025-06-29 18:18 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R02 completed successfully
2025-06-29 18:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:18 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 18:18 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 18:18 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R02 took 0.01 seconds (00:00:00)
2025-06-29 18:18 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R02 CPU usage: 0.02s user, 0.00s system
2025-06-29 18:18 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-29 18:18 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R02 completed successfully
2025-06-29 18:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:18 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-29 18:18 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 0.60 seconds (00:00:00)
2025-06-29 18:18 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.45s user, 0.12s system
2025-06-29 18:18 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 0.09MB
2025-06-29 18:18 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 completed successfully
2025-06-29 18:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:18 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-29 18:18 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-29 18:18 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R03 took 0.05 seconds (00:00:00)
2025-06-29 18:18 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R03 CPU usage: 0.05s user, 0.00s system
2025-06-29 18:18 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R03 memory delta: 1.55MB
2025-06-29 18:18 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R03 completed successfully
2025-06-29 18:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:18 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R03 took 0.02 seconds (00:00:00)
2025-06-29 18:18 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R03 CPU usage: 0.03s user, 0.02s system
2025-06-29 18:18 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R03 memory delta: 0.41MB
2025-06-29 18:18 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R03 completed successfully
2025-06-29 18:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:18 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R03 took 0.01 seconds (00:00:00)
2025-06-29 18:18 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R03 CPU usage: 0.00s user, 0.00s system
2025-06-29 18:18 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-29 18:18 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R03 completed successfully
2025-06-29 18:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:18 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R03 took 0.07 seconds (00:00:00)
2025-06-29 18:18 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R03 CPU usage: 0.23s user, 0.17s system
2025-06-29 18:18 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R03 memory delta: 17.62MB
2025-06-29 18:18 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R03 completed successfully
2025-06-29 18:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:18 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 18:18 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 18:18 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R03 took 0.00 seconds (00:00:00)
2025-06-29 18:18 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R03 CPU usage: 0.00s user, 0.00s system
2025-06-29 18:18 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R03 memory delta: 2.11MB
2025-06-29 18:18 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R03 completed successfully
2025-06-29 18:18 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-29 18:18 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-29 18:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:18 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-06-29 18:18 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-06-29 18:18 | INFO     | ResMonitor.end_timer: CALC_F_VALUES_SH510050 took 0.00 seconds (00:00:00)
2025-06-29 18:18 | INFO     | ResMonitor.end_timer_cpu: CALC_F_VALUES_SH510050 CPU usage: 0.00s user, 0.00s system
2025-06-29 18:18 | INFO     | ResMonitor.end_memory_monitor: CALC_F_VALUES_SH510050 memory delta: 0.00MB
2025-06-29 18:18 | INFO     | FactorMonitorContext.__exit__: CALC_F_VALUES_SH510050 completed successfully
2025-06-29 18:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:18 | INFO     | FactorLoader.get_fct_df: 开始计算2个因子表达式
2025-06-29 18:18 | INFO     | FactorLoader.get_fct_df: 返回结果: 2个指定因子
2025-06-29 18:18 | INFO     | ResMonitor.end_timer: CALC_PJ_VALUES_SH510050 took 0.00 seconds (00:00:00)
2025-06-29 18:18 | INFO     | ResMonitor.end_timer_cpu: CALC_PJ_VALUES_SH510050 CPU usage: 0.00s user, 0.00s system
2025-06-29 18:18 | INFO     | ResMonitor.end_memory_monitor: CALC_PJ_VALUES_SH510050 memory delta: 2.11MB
2025-06-29 18:18 | INFO     | FactorMonitorContext.__exit__: CALC_PJ_VALUES_SH510050 completed successfully
2025-06-29 18:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:18 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: GP_SH510050_20250629_L0_211730
2025-06-29 18:18 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: GP_SH510050_20250629_L0_211730 耗时81.6ms
2025-06-29 18:18 | INFO     | ResMonitor.end_timer: PERSIST_SH510050 took 0.08 seconds (00:00:00)
2025-06-29 18:18 | INFO     | ResMonitor.end_timer_cpu: PERSIST_SH510050 CPU usage: 0.05s user, 0.02s system
2025-06-29 18:18 | INFO     | ResMonitor.end_memory_monitor: PERSIST_SH510050 memory delta: 7.69MB
2025-06-29 18:18 | INFO     | FactorMonitorContext.__exit__: PERSIST_SH510050 completed successfully
2025-06-29 18:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:18 | INFO     | ResMonitor.end_timer: F_GP_GP_SH510050_20250629_L0_211730_label_1_001 took 0.08 seconds (00:00:00)
2025-06-29 18:18 | INFO     | ResMonitor.end_timer_cpu: F_GP_GP_SH510050_20250629_L0_211730_label_1_001 CPU usage: 0.02s user, 0.00s system
2025-06-29 18:18 | INFO     | ResMonitor.end_memory_monitor: F_GP_GP_SH510050_20250629_L0_211730_label_1_001 memory delta: 0.01MB
2025-06-29 18:18 | INFO     | FactorMonitorContext.__exit__: F_GP_GP_SH510050_20250629_L0_211730_label_1_001 completed successfully
2025-06-29 18:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:18 | INFO     | ResMonitor.end_timer: F_GP_PJ_GP_SH510050_20250629_L0_PJ_211730_label_1_001 took 0.06 seconds (00:00:00)
2025-06-29 18:18 | INFO     | ResMonitor.end_timer_cpu: F_GP_PJ_GP_SH510050_20250629_L0_PJ_211730_label_1_001 CPU usage: 0.02s user, 0.00s system
2025-06-29 18:18 | INFO     | ResMonitor.end_memory_monitor: F_GP_PJ_GP_SH510050_20250629_L0_PJ_211730_label_1_001 memory delta: 2.11MB
2025-06-29 18:18 | INFO     | FactorMonitorContext.__exit__: F_GP_PJ_GP_SH510050_20250629_L0_PJ_211730_label_1_001 completed successfully
2025-06-29 18:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 18:18 | INFO     | ResMonitor.end_timer: F_GP_PJ_GP_SH510050_20250629_L0_PJ_211730_label_1_002 took 0.05 seconds (00:00:00)
2025-06-29 18:18 | INFO     | ResMonitor.end_timer_cpu: F_GP_PJ_GP_SH510050_20250629_L0_PJ_211730_label_1_002 CPU usage: 0.00s user, 0.00s system
2025-06-29 18:18 | INFO     | ResMonitor.end_memory_monitor: F_GP_PJ_GP_SH510050_20250629_L0_PJ_211730_label_1_002 memory delta: -0.52MB
2025-06-29 18:18 | INFO     | FactorMonitorContext.__exit__: F_GP_PJ_GP_SH510050_20250629_L0_PJ_211730_label_1_002 completed successfully
2025-06-29 18:18 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250629_L0_211730 took 16.56 seconds (00:00:16)
2025-06-29 18:18 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250629_L0_211730 CPU usage: 12.75s user, 2.69s system
2025-06-29 18:18 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250629_L0_211730 memory delta: 752.11MB
2025-06-29 18:18 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250629_L0_211730 completed successfully
2025-06-29 22:13 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-29 22:13 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-29 22:14 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-29 22:14 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-29 22:14 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-29 22:14 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-29 22:14 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:14 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250628_L0_607982/L0
2025-06-29 22:14 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250628_L0_607982/L0 耗时117.5ms
2025-06-29 22:14 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:14 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 22:14 | INFO     | ResMonitor.end_timer: L2_CORR_SH510050 took 0.01 seconds (00:00:00)
2025-06-29 22:14 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_SH510050 CPU usage: 0.02s user, 0.00s system
2025-06-29 22:14 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_SH510050 memory delta: 0.26MB
2025-06-29 22:14 | INFO     | FactorMonitorContext.__exit__: L2_CORR_SH510050 completed successfully
2025-06-29 22:14 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:14 | INFO     | ResMonitor.end_timer: PERSIST_L2_SH510050 took 0.00 seconds (00:00:00)
2025-06-29 22:14 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L2_SH510050 CPU usage: 0.00s user, 0.00s system
2025-06-29 22:14 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L2_SH510050 memory delta: 0.00MB
2025-06-29 22:14 | ERROR    | FactorMonitorContext.__exit__: PERSIST_L2_SH510050 failed with error: name 'corr_threshold' is not defined
2025-06-29 22:14 | INFO     | ResMonitor.end_timer: L2_CORR_FILTER_1751206499449715 took 0.24 seconds (00:00:00)
2025-06-29 22:14 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_FILTER_1751206499449715 CPU usage: 0.06s user, 0.02s system
2025-06-29 22:14 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_FILTER_1751206499449715 memory delta: 16.22MB
2025-06-29 22:14 | INFO     | MonitorContext.__exit__: L2_CORR_FILTER_1751206499449715 completed successfully
2025-06-29 22:16 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-29 22:16 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-29 22:16 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-29 22:16 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-29 22:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:16 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250628_L0_607982/L0
2025-06-29 22:16 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250628_L0_607982/L0 耗时24.2ms
2025-06-29 22:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:16 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 22:16 | INFO     | ResMonitor.end_timer: L2_CORR_SH510050 took 0.00 seconds (00:00:00)
2025-06-29 22:16 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_SH510050 CPU usage: 0.00s user, 0.00s system
2025-06-29 22:16 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_SH510050 memory delta: 0.32MB
2025-06-29 22:16 | INFO     | FactorMonitorContext.__exit__: L2_CORR_SH510050 completed successfully
2025-06-29 22:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:16 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: L2_CORR_SH510050_20250629_294581
2025-06-29 22:16 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: L2_CORR_SH510050_20250629_294581 耗时86.5ms
2025-06-29 22:16 | INFO     | ResMonitor.end_timer: PERSIST_L2_SH510050 took 0.09 seconds (00:00:00)
2025-06-29 22:16 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L2_SH510050 CPU usage: 0.03s user, 0.00s system
2025-06-29 22:16 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L2_SH510050 memory delta: 2.36MB
2025-06-29 22:16 | INFO     | FactorMonitorContext.__exit__: PERSIST_L2_SH510050 completed successfully
2025-06-29 22:16 | INFO     | ResMonitor.end_timer: L2_CORR_FILTER_1751206561294581 took 0.21 seconds (00:00:00)
2025-06-29 22:16 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_FILTER_1751206561294581 CPU usage: 0.06s user, 0.02s system
2025-06-29 22:16 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_FILTER_1751206561294581 memory delta: 18.25MB
2025-06-29 22:16 | INFO     | MonitorContext.__exit__: L2_CORR_FILTER_1751206561294581 completed successfully
2025-06-29 22:23 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-29 22:23 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-29 22:23 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:23 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_211730/L0
2025-06-29 22:23 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_211730/L0 耗时55.8ms
2025-06-29 22:23 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_366378/L0
2025-06-29 22:23 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_366378/L0 耗时44.4ms
2025-06-29 22:23 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250628_L0_607982/L0
2025-06-29 22:23 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250628_L0_607982/L0 耗时4.0ms
2025-06-29 22:23 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:23 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 22:23 | INFO     | ResMonitor.end_timer: L2_CORR_SH510050 took 0.00 seconds (00:00:00)
2025-06-29 22:23 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_SH510050 CPU usage: 0.00s user, 0.00s system
2025-06-29 22:23 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_SH510050 memory delta: 0.20MB
2025-06-29 22:23 | INFO     | FactorMonitorContext.__exit__: L2_CORR_SH510050 completed successfully
2025-06-29 22:23 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:23 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: L2_CORR_SH510050_20250629_602482
2025-06-29 22:23 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: L2_CORR_SH510050_20250629_602482 耗时64.1ms
2025-06-29 22:23 | INFO     | ResMonitor.end_timer: PERSIST_L2_SH510050 took 0.07 seconds (00:00:00)
2025-06-29 22:23 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L2_SH510050 CPU usage: 0.02s user, 0.02s system
2025-06-29 22:23 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L2_SH510050 memory delta: 2.27MB
2025-06-29 22:23 | INFO     | FactorMonitorContext.__exit__: PERSIST_L2_SH510050 completed successfully
2025-06-29 22:23 | INFO     | ResMonitor.end_timer: L2_CORR_FILTER_1751206994602482 took 0.27 seconds (00:00:00)
2025-06-29 22:23 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_FILTER_1751206994602482 CPU usage: 0.05s user, 0.05s system
2025-06-29 22:23 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_FILTER_1751206994602482 memory delta: 20.99MB
2025-06-29 22:23 | INFO     | MonitorContext.__exit__: L2_CORR_FILTER_1751206994602482 completed successfully
2025-06-29 22:24 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-29 22:24 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-29 22:24 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-29 22:24 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-29 22:24 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:24 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250628_L0_607982/L0
2025-06-29 22:24 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250628_L0_607982/L0 耗时26.0ms
2025-06-29 22:24 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:24 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 22:24 | INFO     | ResMonitor.end_timer: L2_CORR_SH510050 took 0.00 seconds (00:00:00)
2025-06-29 22:24 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_SH510050 CPU usage: 0.00s user, 0.00s system
2025-06-29 22:24 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_SH510050 memory delta: 0.25MB
2025-06-29 22:24 | INFO     | FactorMonitorContext.__exit__: L2_CORR_SH510050 completed successfully
2025-06-29 22:24 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:24 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: L2_CORR_SH510050_20250629_370817
2025-06-29 22:24 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: L2_CORR_SH510050_20250629_370817 耗时90.7ms
2025-06-29 22:24 | INFO     | ResMonitor.end_timer: PERSIST_L2_SH510050 took 0.09 seconds (00:00:00)
2025-06-29 22:24 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L2_SH510050 CPU usage: 0.02s user, 0.00s system
2025-06-29 22:24 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L2_SH510050 memory delta: 2.44MB
2025-06-29 22:24 | INFO     | FactorMonitorContext.__exit__: PERSIST_L2_SH510050 completed successfully
2025-06-29 22:24 | INFO     | ResMonitor.end_timer: L2_CORR_FILTER_1751207085370817 took 0.26 seconds (00:00:00)
2025-06-29 22:24 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_FILTER_1751207085370817 CPU usage: 0.05s user, 0.03s system
2025-06-29 22:24 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_FILTER_1751207085370817 memory delta: 18.06MB
2025-06-29 22:24 | INFO     | MonitorContext.__exit__: L2_CORR_FILTER_1751207085370817 completed successfully
2025-06-29 22:25 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-29 22:25 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-29 22:25 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-29 22:25 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-29 22:25 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:25 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250628_L0_607982/L0
2025-06-29 22:25 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250628_L0_607982/L0 耗时23.8ms
2025-06-29 22:25 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:25 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 22:25 | INFO     | ResMonitor.end_timer: L2_CORR_SH510050 took 0.00 seconds (00:00:00)
2025-06-29 22:25 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_SH510050 CPU usage: 0.00s user, 0.00s system
2025-06-29 22:25 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_SH510050 memory delta: 0.25MB
2025-06-29 22:25 | INFO     | FactorMonitorContext.__exit__: L2_CORR_SH510050 completed successfully
2025-06-29 22:25 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:25 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: L2_CORR_SH510050_20250629_238180
2025-06-29 22:25 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: L2_CORR_SH510050_20250629_238180 耗时62.1ms
2025-06-29 22:25 | INFO     | ResMonitor.end_timer: PERSIST_L2_SH510050 took 0.06 seconds (00:00:00)
2025-06-29 22:25 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L2_SH510050 CPU usage: 0.02s user, 0.02s system
2025-06-29 22:25 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L2_SH510050 memory delta: 2.20MB
2025-06-29 22:25 | INFO     | FactorMonitorContext.__exit__: PERSIST_L2_SH510050 completed successfully
2025-06-29 22:25 | INFO     | ResMonitor.end_timer: L2_CORR_FILTER_1751207109238180 took 0.24 seconds (00:00:00)
2025-06-29 22:25 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_FILTER_1751207109238180 CPU usage: 0.02s user, 0.05s system
2025-06-29 22:25 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_FILTER_1751207109238180 memory delta: 18.15MB
2025-06-29 22:25 | INFO     | MonitorContext.__exit__: L2_CORR_FILTER_1751207109238180 completed successfully
2025-06-29 22:25 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-29 22:25 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-29 22:25 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-29 22:25 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-29 22:25 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:25 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250628_L0_607982/L0
2025-06-29 22:25 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250628_L0_607982/L0 耗时25.0ms
2025-06-29 22:25 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:25 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 22:25 | INFO     | ResMonitor.end_timer: L2_CORR_SH510050 took 0.01 seconds (00:00:00)
2025-06-29 22:25 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_SH510050 CPU usage: 0.00s user, 0.00s system
2025-06-29 22:25 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_SH510050 memory delta: 0.27MB
2025-06-29 22:25 | INFO     | FactorMonitorContext.__exit__: L2_CORR_SH510050 completed successfully
2025-06-29 22:25 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:25 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: L2_CORR_SH510050_20250629_123757
2025-06-29 22:25 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: L2_CORR_SH510050_20250629_123757 耗时19.9ms
2025-06-29 22:25 | INFO     | ResMonitor.end_timer: PERSIST_L2_SH510050 took 0.02 seconds (00:00:00)
2025-06-29 22:25 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L2_SH510050 CPU usage: 0.00s user, 0.02s system
2025-06-29 22:25 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L2_SH510050 memory delta: 2.49MB
2025-06-29 22:25 | INFO     | FactorMonitorContext.__exit__: PERSIST_L2_SH510050 completed successfully
2025-06-29 22:25 | INFO     | ResMonitor.end_timer: L2_CORR_FILTER_1751207130123757 took 0.17 seconds (00:00:00)
2025-06-29 22:25 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_FILTER_1751207130123757 CPU usage: 0.03s user, 0.06s system
2025-06-29 22:25 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_FILTER_1751207130123757 memory delta: 19.72MB
2025-06-29 22:25 | INFO     | MonitorContext.__exit__: L2_CORR_FILTER_1751207130123757 completed successfully
2025-06-29 22:26 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-29 22:26 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-29 22:26 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:26 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_211730/L0
2025-06-29 22:26 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_211730/L0 耗时22.8ms
2025-06-29 22:26 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_366378/L0
2025-06-29 22:26 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_366378/L0 耗时3.9ms
2025-06-29 22:26 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250628_L0_607982/L0
2025-06-29 22:26 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250628_L0_607982/L0 耗时3.9ms
2025-06-29 22:26 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:26 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 22:26 | INFO     | ResMonitor.end_timer: L2_CORR_SH510050 took 0.00 seconds (00:00:00)
2025-06-29 22:26 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_SH510050 CPU usage: 0.02s user, 0.00s system
2025-06-29 22:26 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_SH510050 memory delta: 0.20MB
2025-06-29 22:26 | INFO     | FactorMonitorContext.__exit__: L2_CORR_SH510050 completed successfully
2025-06-29 22:26 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:26 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: L2_CORR_SH510050_20250629_166383
2025-06-29 22:26 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: L2_CORR_SH510050_20250629_166383 耗时20.5ms
2025-06-29 22:26 | INFO     | ResMonitor.end_timer: PERSIST_L2_SH510050 took 0.02 seconds (00:00:00)
2025-06-29 22:26 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L2_SH510050 CPU usage: 0.00s user, 0.00s system
2025-06-29 22:26 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L2_SH510050 memory delta: 2.22MB
2025-06-29 22:26 | INFO     | FactorMonitorContext.__exit__: PERSIST_L2_SH510050 completed successfully
2025-06-29 22:26 | INFO     | ResMonitor.end_timer: L2_CORR_FILTER_1751207196166383 took 0.19 seconds (00:00:00)
2025-06-29 22:26 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_FILTER_1751207196166383 CPU usage: 0.06s user, 0.05s system
2025-06-29 22:26 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_FILTER_1751207196166383 memory delta: 22.17MB
2025-06-29 22:26 | INFO     | MonitorContext.__exit__: L2_CORR_FILTER_1751207196166383 completed successfully
2025-06-29 22:28 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-29 22:28 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-29 22:28 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 22:28 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 22:28 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 22:28 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 22:28 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 22:28 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 22:33 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-29 22:33 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-29 22:33 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:33 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_211730/L0
2025-06-29 22:33 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_211730/L0 耗时24.5ms
2025-06-29 22:33 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_366378/L0
2025-06-29 22:33 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_366378/L0 耗时3.8ms
2025-06-29 22:33 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250628_L0_607982/L0
2025-06-29 22:33 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250628_L0_607982/L0 耗时4.1ms
2025-06-29 22:33 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:33 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 22:33 | INFO     | ResMonitor.end_timer: L2_CORR_SH510050 took 0.00 seconds (00:00:00)
2025-06-29 22:33 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_SH510050 CPU usage: 0.00s user, 0.00s system
2025-06-29 22:33 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_SH510050 memory delta: 0.19MB
2025-06-29 22:33 | INFO     | FactorMonitorContext.__exit__: L2_CORR_SH510050 completed successfully
2025-06-29 22:33 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:33 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: L2_CORR_SH510050_20250629_463885
2025-06-29 22:33 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: L2_CORR_SH510050_20250629_463885 耗时8.3ms
2025-06-29 22:33 | INFO     | ResMonitor.end_timer: PERSIST_L2_SH510050 took 0.01 seconds (00:00:00)
2025-06-29 22:33 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L2_SH510050 CPU usage: 0.00s user, 0.00s system
2025-06-29 22:33 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L2_SH510050 memory delta: 2.20MB
2025-06-29 22:33 | INFO     | FactorMonitorContext.__exit__: PERSIST_L2_SH510050 completed successfully
2025-06-29 22:33 | INFO     | ResMonitor.end_timer: L2_CORR_FILTER_1751207612463885 took 0.20 seconds (00:00:00)
2025-06-29 22:33 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_FILTER_1751207612463885 CPU usage: 0.08s user, 0.02s system
2025-06-29 22:33 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_FILTER_1751207612463885 memory delta: 21.28MB
2025-06-29 22:33 | INFO     | MonitorContext.__exit__: L2_CORR_FILTER_1751207612463885 completed successfully
2025-06-29 22:36 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-29 22:36 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-29 22:36 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:36 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_211730/L0
2025-06-29 22:36 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_211730/L0 耗时22.9ms
2025-06-29 22:36 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_366378/L0
2025-06-29 22:36 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_366378/L0 耗时4.7ms
2025-06-29 22:36 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250628_L0_607982/L0
2025-06-29 22:36 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250628_L0_607982/L0 耗时4.1ms
2025-06-29 22:36 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:36 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 22:36 | INFO     | ResMonitor.end_timer: L2_CORR_SH510050 took 0.00 seconds (00:00:00)
2025-06-29 22:36 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_SH510050 CPU usage: 0.02s user, 0.00s system
2025-06-29 22:36 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_SH510050 memory delta: 0.19MB
2025-06-29 22:36 | INFO     | FactorMonitorContext.__exit__: L2_CORR_SH510050 completed successfully
2025-06-29 22:36 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:36 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: L2_CORR_SH510050_20250629_712333
2025-06-29 22:36 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: L2_CORR_SH510050_20250629_712333 耗时51.4ms
2025-06-29 22:36 | INFO     | ResMonitor.end_timer: PERSIST_L2_SH510050 took 0.05 seconds (00:00:00)
2025-06-29 22:36 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L2_SH510050 CPU usage: 0.00s user, 0.03s system
2025-06-29 22:36 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L2_SH510050 memory delta: 2.04MB
2025-06-29 22:36 | INFO     | FactorMonitorContext.__exit__: PERSIST_L2_SH510050 completed successfully
2025-06-29 22:36 | INFO     | ResMonitor.end_timer: L2_CORR_FILTER_1751207794712333 took 0.22 seconds (00:00:00)
2025-06-29 22:36 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_FILTER_1751207794712333 CPU usage: 0.03s user, 0.06s system
2025-06-29 22:36 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_FILTER_1751207794712333 memory delta: 21.23MB
2025-06-29 22:36 | INFO     | MonitorContext.__exit__: L2_CORR_FILTER_1751207794712333 completed successfully
2025-06-29 22:37 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-29 22:37 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-29 22:37 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:37 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_211730/L0
2025-06-29 22:37 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_211730/L0 耗时23.4ms
2025-06-29 22:37 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_366378/L0
2025-06-29 22:37 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_366378/L0 耗时3.9ms
2025-06-29 22:37 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250628_L0_607982/L0
2025-06-29 22:37 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250628_L0_607982/L0 耗时4.0ms
2025-06-29 22:37 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:37 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 22:37 | INFO     | ResMonitor.end_timer: L2_CORR_SH510050 took 0.00 seconds (00:00:00)
2025-06-29 22:37 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_SH510050 CPU usage: 0.00s user, 0.00s system
2025-06-29 22:37 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_SH510050 memory delta: 0.21MB
2025-06-29 22:37 | INFO     | FactorMonitorContext.__exit__: L2_CORR_SH510050 completed successfully
2025-06-29 22:37 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:37 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: L2_CORR_SH510050_20250629_627662
2025-06-29 22:37 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: L2_CORR_SH510050_20250629_627662 耗时10.2ms
2025-06-29 22:37 | INFO     | ResMonitor.end_timer: PERSIST_L2_SH510050 took 0.01 seconds (00:00:00)
2025-06-29 22:37 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L2_SH510050 CPU usage: 0.00s user, 0.00s system
2025-06-29 22:37 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L2_SH510050 memory delta: 2.15MB
2025-06-29 22:37 | INFO     | FactorMonitorContext.__exit__: PERSIST_L2_SH510050 completed successfully
2025-06-29 22:37 | INFO     | ResMonitor.end_timer: L2_CORR_FILTER_1751207844627662 took 0.24 seconds (00:00:00)
2025-06-29 22:37 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_FILTER_1751207844627662 CPU usage: 0.02s user, 0.03s system
2025-06-29 22:37 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_FILTER_1751207844627662 memory delta: 21.05MB
2025-06-29 22:37 | INFO     | MonitorContext.__exit__: L2_CORR_FILTER_1751207844627662 completed successfully
2025-06-29 22:39 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-29 22:39 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-29 22:39 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:39 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_211730/L0
2025-06-29 22:39 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_211730/L0 耗时31.0ms
2025-06-29 22:39 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_366378/L0
2025-06-29 22:39 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_366378/L0 耗时4.0ms
2025-06-29 22:39 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250628_L0_607982/L0
2025-06-29 22:39 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250628_L0_607982/L0 耗时4.0ms
2025-06-29 22:39 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:39 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 22:39 | INFO     | ResMonitor.end_timer: L2_CORR_SH510050 took 0.01 seconds (00:00:00)
2025-06-29 22:39 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_SH510050 CPU usage: 0.00s user, 0.00s system
2025-06-29 22:39 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_SH510050 memory delta: 0.19MB
2025-06-29 22:39 | INFO     | FactorMonitorContext.__exit__: L2_CORR_SH510050 completed successfully
2025-06-29 22:39 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:39 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: L2_CORR_SH510050_20250629_828144
2025-06-29 22:39 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: L2_CORR_SH510050_20250629_828144 耗时71.2ms
2025-06-29 22:39 | INFO     | ResMonitor.end_timer: PERSIST_L2_SH510050 took 0.07 seconds (00:00:00)
2025-06-29 22:39 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L2_SH510050 CPU usage: 0.02s user, 0.00s system
2025-06-29 22:39 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L2_SH510050 memory delta: 2.24MB
2025-06-29 22:39 | INFO     | FactorMonitorContext.__exit__: PERSIST_L2_SH510050 completed successfully
2025-06-29 22:39 | INFO     | ResMonitor.end_timer: L2_CORR_FILTER_1751207940828144 took 0.24 seconds (00:00:00)
2025-06-29 22:39 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_FILTER_1751207940828144 CPU usage: 0.03s user, 0.00s system
2025-06-29 22:39 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_FILTER_1751207940828144 memory delta: 21.49MB
2025-06-29 22:39 | INFO     | MonitorContext.__exit__: L2_CORR_FILTER_1751207940828144 completed successfully
2025-06-29 22:51 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:51 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-29 22:51 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-29 22:51 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-29 22:51 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-29 22:51 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-29 22:51 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-29 22:51 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-29 22:51 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-29 22:51 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-29 22:51 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-29 22:51 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-29 22:51 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-29 22:51 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-29 22:51 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-29 22:51 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-29 22:51 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-29 22:51 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:51 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-29 22:51 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 3.71 seconds (00:00:03)
2025-06-29 22:51 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 0.75s user, 0.28s system
2025-06-29 22:51 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 61.54MB
2025-06-29 22:51 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 completed successfully
2025-06-29 22:51 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:51 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-29 22:51 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-29 22:51 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R01 took 0.05 seconds (00:00:00)
2025-06-29 22:51 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R01 CPU usage: 0.03s user, 0.00s system
2025-06-29 22:51 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R01 memory delta: 1.87MB
2025-06-29 22:51 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R01 completed successfully
2025-06-29 22:51 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:51 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R01 took 0.03 seconds (00:00:00)
2025-06-29 22:51 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R01 CPU usage: 0.02s user, 0.02s system
2025-06-29 22:51 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R01 memory delta: 0.03MB
2025-06-29 22:51 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R01 completed successfully
2025-06-29 22:51 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:51 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-06-29 22:51 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R01 CPU usage: 0.00s user, 0.02s system
2025-06-29 22:51 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R01 memory delta: 2.20MB
2025-06-29 22:51 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R01 completed successfully
2025-06-29 22:51 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:52 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R01 took 8.79 seconds (00:00:08)
2025-06-29 22:52 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R01 CPU usage: 8.41s user, 1.27s system
2025-06-29 22:52 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R01 memory delta: 301.31MB
2025-06-29 22:52 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R01 completed successfully
2025-06-29 22:52 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:52 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 22:52 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 22:52 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-06-29 22:52 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R01 CPU usage: 0.02s user, 0.00s system
2025-06-29 22:52 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R01 memory delta: 2.04MB
2025-06-29 22:52 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R01 completed successfully
2025-06-29 22:52 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:52 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-29 22:52 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 0.59 seconds (00:00:00)
2025-06-29 22:52 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.44s user, 0.09s system
2025-06-29 22:52 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 3.58MB
2025-06-29 22:52 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 completed successfully
2025-06-29 22:52 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:52 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-29 22:52 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-29 22:52 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R02 took 0.05 seconds (00:00:00)
2025-06-29 22:52 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R02 CPU usage: 0.05s user, 0.02s system
2025-06-29 22:52 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R02 memory delta: 0.01MB
2025-06-29 22:52 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R02 completed successfully
2025-06-29 22:52 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:52 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R02 took 0.03 seconds (00:00:00)
2025-06-29 22:52 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R02 CPU usage: 0.02s user, 0.00s system
2025-06-29 22:52 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R02 memory delta: 2.11MB
2025-06-29 22:52 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R02 completed successfully
2025-06-29 22:52 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:52 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R02 took 0.01 seconds (00:00:00)
2025-06-29 22:52 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-29 22:52 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R02 memory delta: 2.11MB
2025-06-29 22:52 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R02 completed successfully
2025-06-29 22:52 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:52 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R02 took 0.07 seconds (00:00:00)
2025-06-29 22:52 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R02 CPU usage: 0.11s user, 0.02s system
2025-06-29 22:52 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R02 memory delta: 19.25MB
2025-06-29 22:52 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R02 completed successfully
2025-06-29 22:52 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:52 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 22:52 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 22:52 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R02 took 0.00 seconds (00:00:00)
2025-06-29 22:52 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-29 22:52 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-29 22:52 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R02 completed successfully
2025-06-29 22:52 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:52 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-29 22:52 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 0.59 seconds (00:00:00)
2025-06-29 22:52 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.50s user, 0.00s system
2025-06-29 22:52 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: -2.04MB
2025-06-29 22:52 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 completed successfully
2025-06-29 22:52 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:52 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-29 22:52 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-29 22:52 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R03 took 0.05 seconds (00:00:00)
2025-06-29 22:52 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R03 CPU usage: 0.05s user, 0.02s system
2025-06-29 22:52 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R03 memory delta: 0.42MB
2025-06-29 22:52 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R03 completed successfully
2025-06-29 22:52 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:52 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R03 took 0.02 seconds (00:00:00)
2025-06-29 22:52 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R03 CPU usage: 0.02s user, 0.00s system
2025-06-29 22:52 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R03 memory delta: 0.58MB
2025-06-29 22:52 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R03 completed successfully
2025-06-29 22:52 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:52 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R03 took 0.01 seconds (00:00:00)
2025-06-29 22:52 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R03 CPU usage: 0.00s user, 0.00s system
2025-06-29 22:52 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R03 memory delta: -1.05MB
2025-06-29 22:52 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R03 completed successfully
2025-06-29 22:52 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:52 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R03 took 0.06 seconds (00:00:00)
2025-06-29 22:52 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R03 CPU usage: 0.30s user, 0.08s system
2025-06-29 22:52 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R03 memory delta: 24.79MB
2025-06-29 22:52 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R03 completed successfully
2025-06-29 22:52 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:52 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 22:52 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 22:52 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R03 took 0.01 seconds (00:00:00)
2025-06-29 22:52 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R03 CPU usage: 0.00s user, 0.00s system
2025-06-29 22:52 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-29 22:52 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R03 completed successfully
2025-06-29 22:52 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-29 22:52 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-29 22:52 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:52 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-06-29 22:52 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-06-29 22:52 | INFO     | ResMonitor.end_timer: CALC_F_VALUES_SH510050 took 0.00 seconds (00:00:00)
2025-06-29 22:52 | INFO     | ResMonitor.end_timer_cpu: CALC_F_VALUES_SH510050 CPU usage: 0.02s user, 0.00s system
2025-06-29 22:52 | INFO     | ResMonitor.end_memory_monitor: CALC_F_VALUES_SH510050 memory delta: 0.00MB
2025-06-29 22:52 | INFO     | FactorMonitorContext.__exit__: CALC_F_VALUES_SH510050 completed successfully
2025-06-29 22:52 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:52 | INFO     | FactorLoader.get_fct_df: 开始计算2个因子表达式
2025-06-29 22:52 | INFO     | FactorLoader.get_fct_df: 返回结果: 2个指定因子
2025-06-29 22:52 | INFO     | ResMonitor.end_timer: CALC_PJ_VALUES_SH510050 took 0.01 seconds (00:00:00)
2025-06-29 22:52 | INFO     | ResMonitor.end_timer_cpu: CALC_PJ_VALUES_SH510050 CPU usage: 0.02s user, 0.00s system
2025-06-29 22:52 | INFO     | ResMonitor.end_memory_monitor: CALC_PJ_VALUES_SH510050 memory delta: 0.00MB
2025-06-29 22:52 | INFO     | FactorMonitorContext.__exit__: CALC_PJ_VALUES_SH510050 completed successfully
2025-06-29 22:52 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:52 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: GP_SH510050_20250629_L0_277461
2025-06-29 22:52 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: GP_SH510050_20250629_L0_277461 耗时183.0ms
2025-06-29 22:52 | INFO     | ResMonitor.end_timer: PERSIST_SH510050 took 0.18 seconds (00:00:00)
2025-06-29 22:52 | INFO     | ResMonitor.end_timer_cpu: PERSIST_SH510050 CPU usage: 0.09s user, 0.00s system
2025-06-29 22:52 | INFO     | ResMonitor.end_memory_monitor: PERSIST_SH510050 memory delta: 3.81MB
2025-06-29 22:52 | INFO     | FactorMonitorContext.__exit__: PERSIST_SH510050 completed successfully
2025-06-29 22:52 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:52 | INFO     | ResMonitor.end_timer: F_GP_GP_SH510050_20250629_L0_277461_label_1_001 took 0.00 seconds (00:00:00)
2025-06-29 22:52 | INFO     | ResMonitor.end_timer_cpu: F_GP_GP_SH510050_20250629_L0_277461_label_1_001 CPU usage: 0.00s user, 0.00s system
2025-06-29 22:52 | INFO     | ResMonitor.end_memory_monitor: F_GP_GP_SH510050_20250629_L0_277461_label_1_001 memory delta: 0.01MB
2025-06-29 22:52 | INFO     | FactorMonitorContext.__exit__: F_GP_GP_SH510050_20250629_L0_277461_label_1_001 completed successfully
2025-06-29 22:52 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:52 | INFO     | ResMonitor.end_timer: F_GP_PJ_GP_SH510050_20250629_L0_PJ_277461_label_1_001 took 0.00 seconds (00:00:00)
2025-06-29 22:52 | INFO     | ResMonitor.end_timer_cpu: F_GP_PJ_GP_SH510050_20250629_L0_PJ_277461_label_1_001 CPU usage: 0.00s user, 0.00s system
2025-06-29 22:52 | INFO     | ResMonitor.end_memory_monitor: F_GP_PJ_GP_SH510050_20250629_L0_PJ_277461_label_1_001 memory delta: 0.00MB
2025-06-29 22:52 | INFO     | FactorMonitorContext.__exit__: F_GP_PJ_GP_SH510050_20250629_L0_PJ_277461_label_1_001 completed successfully
2025-06-29 22:52 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:52 | INFO     | ResMonitor.end_timer: F_GP_PJ_GP_SH510050_20250629_L0_PJ_277461_label_1_002 took 0.00 seconds (00:00:00)
2025-06-29 22:52 | INFO     | ResMonitor.end_timer_cpu: F_GP_PJ_GP_SH510050_20250629_L0_PJ_277461_label_1_002 CPU usage: 0.00s user, 0.00s system
2025-06-29 22:52 | INFO     | ResMonitor.end_memory_monitor: F_GP_PJ_GP_SH510050_20250629_L0_PJ_277461_label_1_002 memory delta: 0.00MB
2025-06-29 22:52 | INFO     | FactorMonitorContext.__exit__: F_GP_PJ_GP_SH510050_20250629_L0_PJ_277461_label_1_002 completed successfully
2025-06-29 22:52 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250629_L0_277461 took 16.46 seconds (00:00:16)
2025-06-29 22:52 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250629_L0_277461 CPU usage: 11.00s user, 1.95s system
2025-06-29 22:52 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250629_L0_277461 memory delta: 424.31MB
2025-06-29 22:52 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250629_L0_277461 completed successfully
2025-06-29 22:52 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-29 22:52 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-29 22:52 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:52 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_211730/L0
2025-06-29 22:52 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_211730/L0 耗时24.6ms
2025-06-29 22:52 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_277461/L0
2025-06-29 22:52 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_277461/L0 耗时5.0ms
2025-06-29 22:52 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_366378/L0
2025-06-29 22:52 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_366378/L0 耗时4.0ms
2025-06-29 22:52 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250628_L0_607982/L0
2025-06-29 22:52 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250628_L0_607982/L0 耗时31.6ms
2025-06-29 22:52 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:52 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-29 22:52 | INFO     | ResMonitor.end_timer: L2_CORR_SH510050 took 0.01 seconds (00:00:00)
2025-06-29 22:52 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_SH510050 CPU usage: 0.00s user, 0.00s system
2025-06-29 22:52 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_SH510050 memory delta: 0.18MB
2025-06-29 22:52 | INFO     | FactorMonitorContext.__exit__: L2_CORR_SH510050 completed successfully
2025-06-29 22:52 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 22:52 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: L2_CORR_SH510050_20250629_484934
2025-06-29 22:52 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: L2_CORR_SH510050_20250629_484934 耗时27.0ms
2025-06-29 22:52 | INFO     | ResMonitor.end_timer: PERSIST_L2_SH510050 took 0.03 seconds (00:00:00)
2025-06-29 22:52 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L2_SH510050 CPU usage: 0.02s user, 0.00s system
2025-06-29 22:52 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L2_SH510050 memory delta: 2.35MB
2025-06-29 22:52 | INFO     | FactorMonitorContext.__exit__: PERSIST_L2_SH510050 completed successfully
2025-06-29 22:52 | INFO     | ResMonitor.end_timer: L2_CORR_FILTER_1751208756484934 took 0.24 seconds (00:00:00)
2025-06-29 22:52 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_FILTER_1751208756484934 CPU usage: 0.08s user, 0.02s system
2025-06-29 22:52 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_FILTER_1751208756484934 memory delta: 21.92MB
2025-06-29 22:52 | INFO     | MonitorContext.__exit__: L2_CORR_FILTER_1751208756484934 completed successfully
2025-06-29 23:04 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-29 23:04 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-29 23:04 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-29 23:04 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-29 23:04 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 23:04 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250628_L0_607982/L0
2025-06-29 23:04 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250628_L0_607982/L0 耗时25.0ms
2025-06-29 23:04 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 23:04 | INFO     | ResMonitor.end_timer: L2_LASSO_SH510050 took 0.00 seconds (00:00:00)
2025-06-29 23:04 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_SH510050 CPU usage: 0.00s user, 0.00s system
2025-06-29 23:04 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_SH510050 memory delta: 0.20MB
2025-06-29 23:04 | INFO     | FactorMonitorContext.__exit__: L2_LASSO_SH510050 completed successfully
2025-06-29 23:04 | INFO     | ResMonitor.end_timer: L2_LASSO_FILTER_1751209482126806 took 0.07 seconds (00:00:00)
2025-06-29 23:04 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_FILTER_1751209482126806 CPU usage: 0.02s user, 0.02s system
2025-06-29 23:04 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_FILTER_1751209482126806 memory delta: 15.98MB
2025-06-29 23:04 | INFO     | MonitorContext.__exit__: L2_LASSO_FILTER_1751209482126806 completed successfully
2025-06-29 23:04 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-29 23:04 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-29 23:04 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 23:04 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_211730/L0
2025-06-29 23:04 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_211730/L0 耗时24.0ms
2025-06-29 23:04 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_277461/L0
2025-06-29 23:04 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_277461/L0 耗时5.0ms
2025-06-29 23:04 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_366378/L0
2025-06-29 23:04 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_366378/L0 耗时5.0ms
2025-06-29 23:04 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250628_L0_607982/L0
2025-06-29 23:04 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250628_L0_607982/L0 耗时4.0ms
2025-06-29 23:04 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 23:04 | INFO     | ResMonitor.end_timer: L2_LASSO_SH510050 took 0.00 seconds (00:00:00)
2025-06-29 23:04 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_SH510050 CPU usage: 0.02s user, 0.00s system
2025-06-29 23:04 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_SH510050 memory delta: 0.15MB
2025-06-29 23:04 | INFO     | FactorMonitorContext.__exit__: L2_LASSO_SH510050 completed successfully
2025-06-29 23:04 | INFO     | ResMonitor.end_timer: L2_LASSO_FILTER_1751209494814385 took 0.09 seconds (00:00:00)
2025-06-29 23:04 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_FILTER_1751209494814385 CPU usage: 0.03s user, 0.02s system
2025-06-29 23:04 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_FILTER_1751209494814385 memory delta: 19.55MB
2025-06-29 23:04 | INFO     | MonitorContext.__exit__: L2_LASSO_FILTER_1751209494814385 completed successfully
2025-06-29 23:06 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-29 23:06 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-29 23:09 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-29 23:09 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-29 23:09 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 23:09 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_211730/L0
2025-06-29 23:09 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_211730/L0 耗时23.4ms
2025-06-29 23:09 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_277461/L0
2025-06-29 23:09 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_277461/L0 耗时5.0ms
2025-06-29 23:09 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_366378/L0
2025-06-29 23:09 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_366378/L0 耗时4.0ms
2025-06-29 23:09 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250628_L0_607982/L0
2025-06-29 23:09 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250628_L0_607982/L0 耗时3.1ms
2025-06-29 23:09 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-29 23:09 | INFO     | ResMonitor.end_timer: L2_LASSO_SH510050 took 0.00 seconds (00:00:00)
2025-06-29 23:09 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_SH510050 CPU usage: 0.00s user, 0.00s system
2025-06-29 23:09 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_SH510050 memory delta: 0.14MB
2025-06-29 23:09 | INFO     | FactorMonitorContext.__exit__: L2_LASSO_SH510050 completed successfully
2025-06-29 23:09 | INFO     | ResMonitor.end_timer: L2_LASSO_FILTER_1751209793021969 took 0.08 seconds (00:00:00)
2025-06-29 23:09 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_FILTER_1751209793021969 CPU usage: 0.03s user, 0.03s system
2025-06-29 23:09 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_FILTER_1751209793021969 memory delta: 19.91MB
2025-06-29 23:09 | INFO     | MonitorContext.__exit__: L2_LASSO_FILTER_1751209793021969 completed successfully
