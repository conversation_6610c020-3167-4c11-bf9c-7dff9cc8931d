"""站点级自定义补丁

此模块会在 **每个 Python 解释器启动** 时由 `site` 自动导入。
它负责确保旧版 / 最小化安装的 scikit-learn 也拥有 `BaseEstimator.__sklearn_tags__`，
从而避免 `super` object has no attribute '__sklearn_tags__' 的兼容性错误。
"""
from __future__ import annotations

import warnings

try:
    from sklearn.base import BaseEstimator
except Exception as exc:  # pragma: no cover
    warnings.warn(
        f"[sitecustomize] 无法导入 scikit-learn ({exc!r})，\n"
        "如果后续代码需要 sklearn，仍会触发 ImportError。",
        RuntimeWarning,
    )
else:
    if not hasattr(BaseEstimator, "__sklearn_tags__"):
        def _dummy_sklearn_tags(self):  # noqa: D401
            """Return empty sklearn tags (自动注入)."""
            return {}

        setattr(BaseEstimator, "__sklearn_tags__", _dummy_sklearn_tags)  # type: ignore[attr-defined]
        warnings.warn(
            "[sitecustomize] 已为 sklearn.base.BaseEstimator 注入缺失的 __sklearn_tags__()，"
            "避免兼容性错误。",
            RuntimeWarning,
        ) 