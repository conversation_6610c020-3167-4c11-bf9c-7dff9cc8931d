from dataclasses import dataclass, field, asdict
from typing import List
# 添加项目根目录到 Python 路径
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from datafeed.hku_dataloader import HKUDataloader
from common.cls_base import BaseObj

@dataclass
class Task(BaseObj):
    name:str = ''
    # =========== 基本参数配置 ==========
    init_cash = 10 * 10000
    start_date: str = '20100101'
    end_date: str = None
    freq: str = 'D'  # 频率: D, W, M, 1min, 5min, 15min, 30min, 60min
    recover: str = 'EQUAL_BACKWARD'  # NO_RECOVER, FORWARD, BACKWARD, EQUAL_FORWARD, EQUAL_BACKWARD
    benchmark: str = '510300.SH'  # 基准品种代码, 用于计算收益率, 一般为沪深300指数

    # # =========== 手续费/滑点 - NOTHING - 测试用 ============
    # commission: float = 0.0000    # 万1(0.01%)
    # stamp_duty: float = 0         # ETF不收印花税
    # slippage_type: str = 'fixed'  # ETF用固定点数
    # slippage: float = 0.00        # 1分钱
        
    # =========== 手续费/滑点 - ETF ============
    commission: float = 0.0001    # 万1(0.01%)
    stamp_duty: float = 0         # ETF不收印花税
    slippage_type: str = 'fixed'  # ETF用固定点数
    slippage: float = 0.002        # 0.2分钱

    # # =========== 手续费/滑点 - 股票 ============ 
    # commission: float = 0.0001     # 万1(0.01%)
    # stamp_duty: float = 0.0005     # 万5
    # slippage_type: str = 'perc'    # 股票用百分比
    # slippage: float = 0.0002       # 万2(0.02%)
    
    # ============ 品种/列/特征 ==========
    symbols: List[str] = field(default_factory=list)
    columns: List[str] = field(default_factory=list)
    feature_names: list = field(default_factory=list)
    feature_exprs: list = field(default_factory=list)

    def load_datas_and_calc_exprs(self):  # 父类: 自动加载品种数据和特征数据
        df_all = HKUDataloader.load_df_all(self.symbols, 
                                  set_index=True, 
                                  start_date=self.start_date, 
                                  end_date=self.end_date,
                                  freq = self.freq, 
                                  recover=self.recover)
        df_all = HKUDataloader.calc_expr_df_all(df_all, self.feature_exprs, self.feature_names)
        df_all.dropna(inplace=True)  # 对结果影响非常重要! 会删掉一些新品种的数据
        return df_all
       
if __name__ == '__main__':
    t = TaskAlgo()
    t.symbols = [
        '399975.SZ',  # '512880.SH',  # 证券ETF
        'H30184.CSI',  # '512480.SH',  # 半导体ETF
        '399976.SZ',  # '515030.SH',  # 新能车
        '930697.CSI',  # '159996.SZ',  # 家电ETF
        '399989.SZ',  # '512170.SH',  # 医药ETF
        '000922.SH'  # '515080.SH',  # 中证红利
    ]
    e = BacktraderEngine(t)
    print()

