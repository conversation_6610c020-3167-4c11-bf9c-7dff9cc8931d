#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整初始化FactorZoo数据库
"""

import sqlite3
import os
from pathlib import Path

def init_complete_database():
    """完整初始化数据库"""
    print("🔧 开始完整初始化FactorZoo数据库...")
    
    db_path = Path("D:/myquant/FZoo/factorzoo.db")
    db_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 完整的数据库schema
    schema_sql = """
    -- =====================================================
    -- XentZ 因子动物园管理系统 - 完整建库脚本
    -- =====================================================

    -- 开始事务
    BEGIN TRANSACTION;

    -- 启用外键约束
    PRAGMA foreign_keys = ON;

    -- =====================================================
    -- 1. 批次管理表
    -- =====================================================
    CREATE TABLE IF NOT EXISTS factor_batches (
        batch_id TEXT PRIMARY KEY,
        batch_name TEXT,
        creation_tool TEXT NOT NULL,
        source_symbols TEXT,
        source_frequencies TEXT,
        source_date_ranges TEXT,
        source_universe TEXT,
        generation_params TEXT,
        total_generated INTEGER DEFAULT 0,
        l0_count INTEGER DEFAULT 0,
        l1_count INTEGER DEFAULT 0,
        l2_count INTEGER DEFAULT 0,
        l3_count INTEGER DEFAULT 0,
        start_time DATETIME,
        end_time DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );

    -- =====================================================
    -- 2. 股票池定义表
    -- =====================================================
    CREATE TABLE IF NOT EXISTS universes (
        universe_id TEXT NOT NULL,
        version INTEGER NOT NULL,
        universe_name TEXT NOT NULL,
        universe_type TEXT NOT NULL,
        static_symbols TEXT,
        selection_criteria TEXT,
        update_frequency TEXT,
        effective_date DATE NOT NULL,
        expiry_date DATE,
        is_current BOOLEAN DEFAULT FALSE,
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (universe_id, version)
    );

    -- =====================================================
    -- 3. 因子分类表
    -- =====================================================
    CREATE TABLE IF NOT EXISTS factor_categories (
        category_id TEXT PRIMARY KEY,
        category_name TEXT NOT NULL,
        parent_category TEXT,
        category_level INTEGER DEFAULT 1,
        description TEXT,
        sort_order INTEGER DEFAULT 0,
        FOREIGN KEY (parent_category) REFERENCES factor_categories(category_id)
    );

    -- =====================================================
    -- 4. 因子表达式主表
    -- =====================================================
    CREATE TABLE IF NOT EXISTS factors (
        factor_id TEXT PRIMARY KEY,
        factor_name TEXT NOT NULL,
        factor_expression TEXT NOT NULL,
        factor_type TEXT NOT NULL CHECK (factor_type IN ('time_series', 'cross_section', 'panel')),
        market_type TEXT,
        data_source_type TEXT NOT NULL CHECK (data_source_type IN ('single_symbol', 'multi_symbol', 'universe')),
        symbols TEXT NOT NULL,
        frequencies TEXT NOT NULL,
        date_ranges TEXT NOT NULL,
        universe_id TEXT,
        universe_version INTEGER,
        cross_section_scope TEXT,
        ranking_method TEXT,
        rebalance_frequency TEXT,
        creation_method TEXT NOT NULL CHECK (creation_method IN (
            'auto_generation', 'manual_creation', 'paper_reference', 'experience_based'
        )),
        generation_tool TEXT,
        pipeline_mode TEXT CHECK (pipeline_mode IN (
            'auto_pipeline', 'manual_pipeline', 'direct_submit', 'hybrid'
        )),
        batch_id TEXT,
        target_label TEXT,
        creation_date DATE NOT NULL,
        creator TEXT,
        reference_info TEXT,
        primary_category TEXT NOT NULL,
        secondary_category TEXT,
        signal_type TEXT,
        status TEXT DEFAULT 'active' CHECK (status IN ('active', 'deprecated', 'testing', 'disabled')),
        pipeline_step TEXT DEFAULT 'L0',
        selection_reason TEXT,
        complexity_score INTEGER,
        computation_cost TEXT,
        last_computation_time_ms INTEGER,
        avg_computation_time_ms INTEGER,
        max_memory_usage_mb REAL,
        resource_intensity TEXT CHECK (resource_intensity IN ('low', 'medium', 'high')),
        performance_notes TEXT,
        last_performance_update DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (batch_id) REFERENCES factor_batches(batch_id),
        FOREIGN KEY (universe_id, universe_version) REFERENCES universes(universe_id, version),
        FOREIGN KEY (primary_category) REFERENCES factor_categories(category_id),
        FOREIGN KEY (secondary_category) REFERENCES factor_categories(category_id)
    );

    -- =====================================================
    -- 5. 因子评价表
    -- =====================================================
    CREATE TABLE IF NOT EXISTS factor_evaluations (
        eval_id TEXT PRIMARY KEY,
        factor_id TEXT NOT NULL,
        evaluation_name TEXT,
        evaluation_method TEXT NOT NULL,
        evaluator TEXT,
        evaluation_period_start DATE NOT NULL,
        evaluation_period_end DATE NOT NULL,
        benchmark_symbol TEXT,
        evaluation_params TEXT,
        total_return REAL,
        annual_return REAL,
        excess_return REAL,
        sharpe_ratio REAL,
        sortino_ratio REAL,
        calmar_ratio REAL,
        information_ratio REAL,
        max_drawdown REAL,
        volatility REAL,
        downside_volatility REAL,
        var_95 REAL,
        cvar_95 REAL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (factor_id) REFERENCES factors(factor_id)
    );

    -- =====================================================
    -- 6. 因子池表
    -- =====================================================
    CREATE TABLE IF NOT EXISTS factor_pools (
        pool_id TEXT PRIMARY KEY,
        pool_name TEXT NOT NULL,
        pool_type TEXT NOT NULL CHECK (pool_type IN ('research', 'production', 'backtest', 'archive')),
        description TEXT,
        creation_criteria TEXT,
        max_factors INTEGER,
        current_count INTEGER DEFAULT 0,
        status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'deprecated')),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );

    -- =====================================================
    -- 7. 因子池成员表
    -- =====================================================
    CREATE TABLE IF NOT EXISTS factor_pool_members (
        member_id TEXT PRIMARY KEY,
        pool_id TEXT NOT NULL,
        factor_id TEXT NOT NULL,
        member_status TEXT DEFAULT 'active' CHECK (member_status IN ('active', 'inactive', 'deprecated')),
        added_date DATE NOT NULL,
        added_by TEXT,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (pool_id) REFERENCES factor_pools(pool_id),
        FOREIGN KEY (factor_id) REFERENCES factors(factor_id)
    );

    -- =====================================================
    -- 8. 因子标签表
    -- =====================================================
    CREATE TABLE IF NOT EXISTS factor_tags (
        tag_id TEXT PRIMARY KEY,
        factor_id TEXT NOT NULL,
        tag_name TEXT NOT NULL,
        tag_value TEXT,
        tag_type TEXT CHECK (tag_type IN ('technical', 'business', 'risk', 'performance', 'system')),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (factor_id) REFERENCES factors(factor_id)
    );

    -- =====================================================
    -- 9. 因子关系表
    -- =====================================================
    CREATE TABLE IF NOT EXISTS factor_relationships (
        relationship_id TEXT PRIMARY KEY,
        source_factor_id TEXT NOT NULL,
        target_factor_id TEXT NOT NULL,
        relationship_type TEXT NOT NULL CHECK (relationship_type IN ('correlation', 'dependency', 'derivation', 'similarity')),
        strength REAL,
        direction TEXT CHECK (direction IN ('positive', 'negative', 'neutral')),
        confidence REAL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (source_factor_id) REFERENCES factors(factor_id),
        FOREIGN KEY (target_factor_id) REFERENCES factors(factor_id)
    );

    -- =====================================================
    -- 10. 因子性能日志表
    -- =====================================================
    CREATE TABLE IF NOT EXISTS factor_performance_logs (
        log_id TEXT PRIMARY KEY,
        batch_id TEXT,
        factor_id TEXT,
        operation_type TEXT NOT NULL,
        symbol TEXT,
        data_size INTEGER,
        start_time DATETIME NOT NULL,
        end_time DATETIME,
        total_time_ms INTEGER,
        cpu_time_user_ms INTEGER,
        cpu_time_system_ms INTEGER,
        max_memory_mb REAL,
        memory_delta_mb REAL,
        status TEXT DEFAULT 'success',
        error_message TEXT,
        context_info TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (batch_id) REFERENCES factor_batches(batch_id),
        FOREIGN KEY (factor_id) REFERENCES factors(factor_id)
    );

    -- =====================================================
    -- 11. 因子值存储表
    -- =====================================================
    CREATE TABLE IF NOT EXISTS factor_values (
        value_id TEXT PRIMARY KEY,
        factor_id TEXT NOT NULL,
        symbol TEXT NOT NULL,
        date DATE NOT NULL,
        value REAL,
        quality_score REAL,
        data_source TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (factor_id) REFERENCES factors(factor_id)
    );

    -- =====================================================
    -- 创建索引
    -- =====================================================
    CREATE INDEX IF NOT EXISTS idx_factors_batch_id ON factors(batch_id);
    CREATE INDEX IF NOT EXISTS idx_factors_creation_date ON factors(creation_date);
    CREATE INDEX IF NOT EXISTS idx_factors_pipeline_step ON factors(pipeline_step);
    CREATE INDEX IF NOT EXISTS idx_factors_status ON factors(status);
    CREATE INDEX IF NOT EXISTS idx_factors_primary_category ON factors(primary_category);
    CREATE INDEX IF NOT EXISTS idx_factors_factor_type ON factors(factor_type);
    CREATE INDEX IF NOT EXISTS idx_factors_market_type ON factors(market_type);
    CREATE INDEX IF NOT EXISTS idx_factors_creation_method ON factors(creation_method);
    CREATE INDEX IF NOT EXISTS idx_factors_generation_tool ON factors(generation_tool);
    CREATE INDEX IF NOT EXISTS idx_factors_pipeline_mode ON factors(pipeline_mode);
    CREATE INDEX IF NOT EXISTS idx_factors_target_label ON factors(target_label);
    CREATE INDEX IF NOT EXISTS idx_factors_computation_time ON factors(last_computation_time_ms);
    CREATE INDEX IF NOT EXISTS idx_factors_resource_intensity ON factors(resource_intensity);
    
    CREATE UNIQUE INDEX IF NOT EXISTS idx_factors_unique_expression ON factors(
        factor_expression, symbols, frequencies, date_ranges
    );
    
    CREATE INDEX IF NOT EXISTS idx_evaluations_factor_id ON factor_evaluations(factor_id);
    CREATE INDEX IF NOT EXISTS idx_evaluations_period ON factor_evaluations(evaluation_period_start, evaluation_period_end);
    CREATE INDEX IF NOT EXISTS idx_evaluations_method ON factor_evaluations(evaluation_method);
    CREATE INDEX IF NOT EXISTS idx_evaluations_sharpe ON factor_evaluations(sharpe_ratio);
    
    CREATE INDEX IF NOT EXISTS idx_pool_members_pool_id ON factor_pool_members(pool_id);
    CREATE INDEX IF NOT EXISTS idx_pool_members_factor_id ON factor_pool_members(factor_id);
    CREATE INDEX IF NOT EXISTS idx_pool_members_status ON factor_pool_members(member_status);
    
    CREATE INDEX IF NOT EXISTS idx_factor_tags_tag_name ON factor_tags(tag_name);
    CREATE INDEX IF NOT EXISTS idx_factor_tags_tag_type ON factor_tags(tag_type);
    
    CREATE INDEX IF NOT EXISTS idx_universes_current ON universes(universe_id, is_current);
    CREATE INDEX IF NOT EXISTS idx_universes_effective_date ON universes(effective_date);
    
    CREATE INDEX IF NOT EXISTS idx_factor_performance_logs_operation_type ON factor_performance_logs(operation_type);
    CREATE INDEX IF NOT EXISTS idx_factor_performance_logs_total_time ON factor_performance_logs(total_time_ms);
    CREATE INDEX IF NOT EXISTS idx_factor_performance_logs_memory ON factor_performance_logs(max_memory_mb);
    CREATE INDEX IF NOT EXISTS idx_factor_performance_logs_batch_id ON factor_performance_logs(batch_id);
    CREATE INDEX IF NOT EXISTS idx_factor_performance_logs_symbol ON factor_performance_logs(symbol);
    CREATE INDEX IF NOT EXISTS idx_factor_performance_logs_start_time ON factor_performance_logs(start_time);
    
    CREATE INDEX IF NOT EXISTS idx_factor_values_factor_id ON factor_values(factor_id);
    CREATE INDEX IF NOT EXISTS idx_factor_values_symbol ON factor_values(symbol);
    CREATE INDEX IF NOT EXISTS idx_factor_values_date ON factor_values(date);

    -- =====================================================
    -- 插入默认数据
    -- =====================================================
    
    -- 插入因子分类
    INSERT OR IGNORE INTO factor_categories (category_id, category_name, parent_category, category_level, description, sort_order) VALUES
    ('TECHNICAL', '技术指标', NULL, 1, '基于价格和成交量的技术分析指标', 1),
    ('MOMENTUM', '动量指标', 'TECHNICAL', 2, '价格动量相关指标', 2),
    ('VOLATILITY', '波动率指标', 'TECHNICAL', 2, '价格波动率相关指标', 3),
    ('VOLUME', '成交量指标', 'TECHNICAL', 2, '成交量相关指标', 4),
    ('TREND', '趋势指标', 'TECHNICAL', 2, '趋势识别指标', 5),
    ('MEAN_REVERSION', '均值回归', 'TECHNICAL', 2, '均值回归策略指标', 6),
    ('CROSS_SECTIONAL', '截面因子', NULL, 1, '跨股票比较的因子', 7),
    ('VALUATION', '估值因子', 'CROSS_SECTIONAL', 2, '估值相关指标', 8),
    ('QUALITY', '质量因子', 'CROSS_SECTIONAL', 2, '公司质量指标', 9),
    ('SIZE', '规模因子', 'CROSS_SECTIONAL', 2, '公司规模指标', 10);

    -- 插入股票池
    INSERT OR IGNORE INTO universes (universe_id, version, universe_name, universe_type, selection_criteria, update_frequency, effective_date, is_current, description) VALUES
    ('CSI300', 1, '沪深300', 'index_components', '{"index_code": "000300.SH", "source": "index_provider"}', 'quarterly', '2020-01-01', TRUE, '沪深300指数成分股'),
    ('CSI500', 1, '中证500', 'index_components', '{"index_code": "000905.SH", "source": "index_provider"}', 'quarterly', '2020-01-01', TRUE, '中证500指数成分股'),
    ('CSI1000', 1, '中证1000', 'index_components', '{"index_code": "000852.SH", "source": "index_provider"}', 'quarterly', '2020-01-01', TRUE, '中证1000指数成分股');

    -- 提交事务
    COMMIT;
    """
    
    try:
        with sqlite3.connect(str(db_path)) as conn:
            conn.executescript(schema_sql)
            conn.commit()
            print("✅ 数据库schema创建成功")
            
            # 验证表创建
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            table_names = [t[0] for t in tables]
            
            print(f"📊 数据库包含 {len(table_names)} 个表:")
            for table in sorted(table_names):
                print(f"   - {table}")
            
            # 特别检查factor_performance_logs表
            if 'factor_performance_logs' in table_names:
                print("✅ factor_performance_logs表存在")
                cursor.execute("PRAGMA table_info(factor_performance_logs)")
                columns = cursor.fetchall()
                print(f"   表包含 {len(columns)} 列")
            else:
                print("❌ factor_performance_logs表不存在")
                return False
                
            return True
                
    except Exception as e:
        print(f"❌ 数据库初始化失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 开始完整初始化FactorZoo数据库...\n")
    
    success = init_complete_database()
    
    print("\n" + "="*60)
    if success:
        print("🎉 数据库完整初始化成功！")
        print("✅ 所有必要的表已创建")
        print("✅ 默认数据已插入")
        print("✅ 索引已创建")
        print("\n现在可以正常运行因子挖掘脚本了！")
    else:
        print("❌ 数据库初始化失败，请检查错误信息")
    
    print("="*60)

if __name__ == "__main__":
    main() 