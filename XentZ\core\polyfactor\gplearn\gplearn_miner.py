''' 定义FactorMiner类, 实现遗传算法挖掘因子, 并实现一些筛选方法 '''
from typing import Tuple, Optional
import numpy as np
import pandas as pd
from scipy.stats import skew, kurtosis
from datetime import datetime
from common.cls_base import BaseObj
from sklearn.linear_model import LinearRegression
from core.polyfactor.gplearn import SymbolicTransformer, _fitness_map
from config import REPORTS_DIR
from config.enums import GP_PARAMS
from datafeed.features.feature_utils import FeatPreprocessing, calc_df_by_symbol
from factor.factor_utils import FactorPerf, warmup_jit_functions
# import jzal_pro.utils.expr_utils as eu
# import jzal_pro.utils.perf_utils as pu


MINER_PATH = REPORTS_DIR.joinpath('miner')
MINER_PATH.mkdir(parents=True, exist_ok=True)

class FctsGPMiner(BaseObj):
    ''' 适用: 单品种+单周期 的多因子 挖掘+筛选 '''

    @staticmethod
    def mine(X_train: pd.DataFrame, feature_names: list, y_train: pd.Series, 
             y_raw: pd.Series, symbol: str, func_set: list, 
             job_num=None, g_uid=None, **kwargs) -> list:
        ''' gplearn因子挖掘 '''
        job_num = job_num or -1
        
        ST_gplearn = SymbolicTransformer(
            **GP_PARAMS, function_set=func_set, feature_names=feature_names,
            n_jobs=job_num, metric=_fitness_map['sr_rolling_segmented']
        )
        
        FctsGPMiner.log(f'start mining on {symbol}...')
        X = np.nan_to_num(X_train, nan=0.0, posinf=0.0, neginf=0.0, copy=False)
        y = np.nan_to_num(y_train, nan=0.0, posinf=0.0, neginf=0.0, copy=False).reshape(-1)
        y_raw = np.nan_to_num(y_raw, nan=0.0, posinf=0.0, neginf=0.0, copy=False)
        
        # 正确的kwargs传递方式
        fit_params = {'y_raw': y_raw, 'symbol': symbol}
        fit_params.update(kwargs)
        ST_gplearn.fit(X, y, **fit_params)
        
        results = []
        for i, bp in enumerate(ST_gplearn._best_programs):
            results.append({
                'factor': f'alpha_{i+1}',
                'fitness': bp.fitness_, 
                'expression': str(bp), 
                'depth': bp.depth_, 
                'length': bp.length_
            })
        
        df = pd.DataFrame(results).set_index('factor')
        df = df.sort_values('fitness', ascending=False).drop_duplicates('expression')
        
        if g_uid:
            filename = f'{symbol}_{g_uid}_fcts_gp_{datetime.now().strftime("%y%m%d_%H")}.csv'
            df.to_csv(MINER_PATH / filename)
            
        return df['expression'].tolist()

    @staticmethod
    def select_by_sharp(a_fcts_df: pd.DataFrame, y_train: pd.Series, y_raw_se: pd.Series, 
                        symbol: str,  g_uid=None, **kwargs) -> Tuple[pd.DataFrame, pd.DataFrame]:
        ''' 基于夏普比率筛选因子 '''
        if a_fcts_df.empty:
            return pd.DataFrame(), pd.DataFrame()
        
        # 从kwargs获取参数，提供默认值
        sr_threshold = kwargs.get('sr_thresh', 0.8)
        pjsr_threshold = kwargs.get('pjsr_thresh', 0.2)
        day_bars = kwargs.get('day_bars', 1)
        ann_days = kwargs.get('ann_days', 252)
        fixed_return = kwargs.get('fixed_return', 0.03)
        fee_rate = kwargs.get('fee_rate', 0.002)
        nextbar_open = kwargs.get('nextbar_open', True)
        norm_pos_params = kwargs.get('norm_pos_params', None)  # 明确异常处理

        # 数据预处理 - 完全保持原逻辑
        a_fcts_df = a_fcts_df.copy()
        y = np.nan_to_num(y_train, nan=0.0, posinf=0.0, neginf=0.0, copy=False)
        y_raw_se = y_raw_se.replace([np.inf, -np.inf, np.nan], 0.0)
        assert a_fcts_df.index.equals(y_raw_se.index)
        y_raw = y_raw_se.values
        
        # 转换数组 - 保持原逻辑
        a_fcts_np = np.array(a_fcts_df).reshape(-1, a_fcts_df.shape[1])
        a_rets_np = np.zeros((a_fcts_np.shape[0], a_fcts_np.shape[1]), dtype=float)
        
        # 因子循环处理 - 完全保持原逻辑
        for i in range(a_fcts_np.shape[1]):
            X = a_fcts_np[:, i].reshape(-1, 1)
            model = LinearRegression()
            model.fit(X, y) # X/y都是norm过的!!
            pos_np = model.predict(X)
            if norm_pos_params is None:
                FctsGPMiner.log("norm_pos_params is required for select_by_sharp", level='ERROR')
                raise ValueError
            pos_se = pd.Series(FeatPreprocessing.norm(pos_np, **norm_pos_params))
            
            if nextbar_open:
                cost = pos_se.shift(1).fillna(0).diff().abs() * fee_rate
                ret = pos_se.shift(1).fillna(0) * y_raw - cost.fillna(0)
            else:
                ret = pos_se * y_raw - pos_se.diff(1).abs() * fee_rate
            
            ret = ret.replace([np.inf, np.nan, -np.inf], 0.0)
            a_rets_np[:, i] = ret
        
        # 计算夏普比率 - 保持原逻辑
        sr_np = FactorPerf.calc_sr(a_rets_np, day_bars, ann_days, fixed_return)
        fcts_list = a_fcts_df.columns.tolist()
        
        # 构建结果DataFrame - 简化但保持逻辑
        sr_dict = {fct: sr for fct, sr in zip(fcts_list, sr_np)}
        sr_df = pd.DataFrame(sr_dict, index=[None])
        sr_df_T = sr_df[[col for col in sr_df.columns if col not in ('ret', 'ret_open')]].T
        sr_df_T = sr_df_T.rename(columns={None: 'sr'})
        sr_df_T = sr_df_T.sort_values(by='sr', ascending=False).dropna()
        sr_selected_df = sr_df_T[sr_df_T['sr'] >= sr_threshold].copy()
        srpj_selected_df = sr_df_T[(sr_df_T['sr'] >= pjsr_threshold) & (sr_df_T['sr'] < sr_threshold)].copy()
        if g_uid is not None:
            timestamp = datetime.now().strftime('%y%m%d_%H')
            sr_selected_df.to_csv(MINER_PATH / f'{symbol}_{g_uid}_fcts_sr_{timestamp}.csv')
            srpj_selected_df.to_csv(MINER_PATH / f'{symbol}_{g_uid}_fcts_srpj_{timestamp}.csv')
        
        return sr_selected_df, srpj_selected_df
    
    @staticmethod
    @calc_df_by_symbol
    def select_by_skew(df: pd.DataFrame, *, skew_threshold: float = 0.5, 
                       g_uid: Optional[str] = None, current_symbol: str = '', **kwargs) ->list:
        skew_selected = []
        if df.empty:
            return skew_selected
        df = df.copy()
        for col in df.columns:
            sk = skew(df[col])
            if abs(sk)<=skew_threshold:
                skew_selected.append({col:sk})
        skew_selected = [list(a_dict.keys())[0] for a_dict in skew_selected]
        if g_uid is not None:
            file_name = '{}_{}_fcts_skew_{}.csv'.format(current_symbol, g_uid, datetime.now().strftime('%y%m%d_%H'))
            path_name = MINER_PATH.joinpath(file_name)
            skew_df = pd.DataFrame([(key, value) for d in skew_selected for key, value in d.items()])
            if not skew_df.empty:
                skew_df.columns = ['Factor', 'skew']
            skew_df.to_csv(path_name)
            
        return skew_selected

    @staticmethod
    @calc_df_by_symbol
    def select_by_kurt(df: pd.DataFrame, *, kurt_threshold: float = 5, 
                       g_uid: Optional[str] = None, current_symbol: str = '', **kwargs) ->list:
        kurt_selected = []
        if df.empty:
            return kurt_selected
        df = df.copy()
        for col in df.columns:
            kt = kurtosis(df[col])
            if kt<=kurt_threshold:
                kurt_selected.append({col:kt})
        kurt_selected = [list(a_dict.keys())[0] for a_dict in kurt_selected]
        if g_uid is not None:
            file_name = '{}_{}_fcts_kurt_{}.csv'.format(current_symbol, g_uid, datetime.now().strftime('%y%m%d_%H'))
            path_name = MINER_PATH.joinpath(file_name)
            kurt_df = pd.DataFrame([(key, value) for d in kurt_selected for key, value in d.items()])
            if not kurt_df.empty:
                kurt_df.columns = ['Factor', 'kurt']
            kurt_df.to_csv(path_name)
            
        return kurt_selected
    
    @staticmethod
    @calc_df_by_symbol
    def select_by_corr(df: pd.DataFrame, y_norm_se: pd.Series, corr_threshold: float = 0.3, 
                       g_uid: Optional[str] = None, current_symbol: str = '', **kwargs) -> list: # Xv经验值0.3
        ''' L2corr调用 - 定义为类方法
            a_fcts_df        : 训练集, [index(date), fct1, fct2, ret] -- 不含基础数据,如OHLC, volume, volume_norm, rsi_6,...
            corr_threshold   : threshold of the absolute value of correlation between factors (e.g., 0.75)
        '''
        FctsGPMiner.log("start compute corr...")
        if df.empty:
            return []
        
        X: pd.DataFrame = df.copy()
        y: pd.Series = y_norm_se.copy()
        # 确保所有特征均为数值类型
        num_features = X.select_dtypes(include=['number']).columns
        if len(num_features) != X.shape[1]:
            X = X[num_features]
            FctsGPMiner.log(f"包含非数值列,仅用数值列计算corr...被忽略的列有：{set(X.columns) - set(num_features)}",level='ERROR')
        # 计算协方差矩阵
        X_corr_matrix = X.corr().where(np.triu(np.ones(X.corr().shape), k=1).astype(bool))
        factor_list_2 = num_features.tolist()
        # 遍历上三角相关系数矩阵，移除高度相关的因子
        for fct_1, fct_2 in zip(*np.triu_indices_from(X_corr_matrix.values, k=1)):
            corr_value = X_corr_matrix.iloc[fct_1, fct_2]
            if pd.notna(corr_value): # 检查 corr_value 是否为 NaN
                corr_value = float(corr_value)  # 将 corr_value 转换为浮点数
                if abs(corr_value) > corr_threshold: # corr_value大于阈值的两个因子,进一步判断
                    corr_with_y_1 = X[num_features[fct_1]].corr(y)
                    corr_with_y_2 = X[num_features[fct_2]].corr(y)
                    # 保留与y相关性较大的因子
                    factor_to_remove = num_features[fct_2] if abs(corr_with_y_1) >= abs(corr_with_y_2) else num_features[fct_1]
                    if factor_to_remove in factor_list_2:
                        factor_list_2.remove(factor_to_remove)      
        if g_uid is not None:
            file_name = '{}_{}_fcts_corr_{}.csv'.format(current_symbol, g_uid, datetime.now().strftime('%y%m%d_%H'))
            path_name = MINER_PATH.joinpath(file_name)
            df[factor_list_2].corr().to_csv(path_name)

        return factor_list_2

    @staticmethod
    @calc_df_by_symbol
    def select_by_rolling_rankic(fct_df: pd.DataFrame, 
                                 y_norm_se: pd.Series, # LABEL
                                 y_raw_t_return_se: pd.Series,
                                 current_symbol: str = '', 
                                 g_uid: Optional[str] = None, 
                                 **kwargs) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """ L2group调用 - 定义为类方法
        根据因子值的 滚动RankIC平均值 选择优质因子
        
        Args:
            a_fcts_df: 训练集, [index(date), fct1, fct2, ret, ret_open], 值为各单因子值
            y_raw_t_return_se: T_Delay期未来收益率(真实,未经norm的,已TRADE_ON_OPEN逻辑处理过)
            window: 滚动窗口天数
            min_period: 最小所需样本数
            top_pct: RankIC均值筛选前pct(如20%)
            pj_pct: PJ池RankIC均值筛选剩余的前pct(如20%)
            g_uid: 可选的唯一标识符,用于保存结果
            
        Returns:
            Tuple[pd.DataFrame, pd.DataFrame]: (优质因子DataFrame, PJ池因子DataFrame)
            每个DataFrame包含因子名称和对应的r1_score, r2_score, final_score
        """
        if fct_df.empty:
            return pd.DataFrame(), pd.DataFrame()
        
        df = fct_df.copy()
        ''' ====    从kwargs获取参数    ==== '''
        window = kwargs.get('window', 240)
        min_period = kwargs.get('min_period', 120)
        top_pct = kwargs.get('top_pct', 0.2)
        pj_pct = kwargs.get('pj_pct', 0.2)
        norm_pos_params = kwargs.get('norm_pos_params', None)  # 明确异常处理
        
        ''' ====    实现r1: 滚动rankic绝对值的平均数    ==== '''    
        # 1. 获取每个因子的滚动RankIC序列        
        # 1. 获取因子列名并确保索引一致性
        fct_cols = [col for col in df.columns if col not in ['ret', 'ret_open']]
        assert df.index.equals(y_norm_se.index)
        assert df.index.equals(y_raw_t_return_se.index)
        
        # 2. 数据预处理
        y_raw_t_return_se = y_raw_t_return_se.replace([np.inf, -np.inf, np.nan], 0.0)
        y_raw_t = y_raw_t_return_se.values.ravel()
        y = np.nan_to_num(y_norm_se, nan=0.0, posinf=0.0, neginf=0.0, copy=False)
        
        # 2. 对每个因子进行模型预测和仓位映射
        pos_matrix = np.zeros((len(df), len(fct_cols)))
        for i, fct in enumerate(fct_cols):
            X = np.array(df[fct]).reshape(-1, 1)
            model = LinearRegression()
            model.fit(X, y)  # y是标准化后的label
            y_pred = model.predict(X)
            if norm_pos_params is None:
                FctsGPMiner.log("norm_pos_params is required for select_by_rolling_rankic", level='ERROR')
                raise ValueError
            pos_np = FeatPreprocessing.norm(y_pred, **norm_pos_params)
            pos_matrix[:, i] = pos_np.ravel()
        # 3. 计算滚动RankIC
        # NOTE: !! 用了JIT,先预热
        warmup_jit_functions()
        rolling_rankic_matrix = FactorPerf.calc_rolling_rankic(
            pos_matrix,
            y_raw_t,
            window=window,
            min_periods=min_period
        )

        # 4. 计算r1分数：滚动RankIC绝对值的平均数（已在[0,1]范围内）
        r1_scores = {}
        for i, fct in enumerate(fct_cols):
            r1_score = np.nanmean(np.abs(rolling_rankic_matrix[:, i]))
            r1_scores[fct] = r1_score
        
        ''' ====    实现r2: 时序因子稳定性    ==== '''
        # 1. 计算未来收益的符号（label），注意对齐长度
        ret_signs = np.sign(np.array(y_raw_t))  # 明确转换为numpy数组
        ret_signs = ret_signs[window-1:]  # 从window-1开始截取，与rolling_rankic_matrix对齐
        
        # 2. 计算每个因子的时序稳定性得分
        r2_scores = {}
        for i, fct in enumerate(fct_cols):
            # 获取当前因子的滚动RankIC序列
            fct_rankic = rolling_rankic_matrix[:, i]
            rankic_signs = np.sign(fct_rankic)
            
            # 确保valid_mask与ret_signs长度一致
            valid_mask = ~np.isnan(fct_rankic)[:len(ret_signs)]
            rankic_signs = rankic_signs[:len(ret_signs)]
            
            if np.sum(valid_mask) > 0:
                # 计算同向的比例
                valid_rankic_signs = rankic_signs[valid_mask]
                valid_ret_signs = ret_signs[valid_mask]
                
                same_direction = np.sum((valid_rankic_signs * valid_ret_signs) > 0)
                total_valid = len(valid_rankic_signs)
                r2_score = same_direction / total_valid
            else:
                r2_score = 0.0
                
            r2_scores[fct] = r2_score
        
        # 合并r1和r2得分
        final_scores = {}
        for fct in fct_cols:
            final_scores[fct] = r1_scores[fct] * r2_scores[fct]
        # 转换为DataFrame并排序
        rankic_df = pd.DataFrame({
            'r1_score': r1_scores,
            'r2_score': r2_scores,
            'sic': final_scores
        })
        rankic_df = rankic_df.sort_values('sic', ascending=False)

        # 选择优质因子和PJ池因子
        n_total = len(rankic_df)
        n_top = int(n_total * top_pct)
        n_pj = int((n_total - n_top) * pj_pct)
        
        top_factors = rankic_df.head(n_top)
        pj_factors = rankic_df.iloc[n_top:n_top + n_pj]

        # 保存结果(如果需要)
        if g_uid is not None:
            file_name = '{}_{}_fcts_rankic_{}.csv'.format(
                current_symbol, g_uid, datetime.now().strftime('%y%m%d_%H')
            )

            path_name = MINER_PATH.joinpath(file_name)
            rankic_df.to_csv(path_name)
            
        return top_factors, pj_factors