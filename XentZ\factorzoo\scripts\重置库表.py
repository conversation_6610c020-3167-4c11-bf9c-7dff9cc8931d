#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XentZ 因子动物园 - 数据库重置脚本
功能：删除当前数据库内的所有表，并严格按照schema.sql来重建库表
用途：获得一个全新的因子库初始状态
创建时间：2025-01-27
"""

import os
import sys
import sqlite3
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from factorzoo.config_zoo import FACTOR_ZOO_CONFIG
from common.cls_base import BaseObj

class DatabaseReset(BaseObj):
    """数据库重置工具类"""
    
    def __init__(self):
        """初始化配置"""
        super().__init__()
        self.db_path = FACTOR_ZOO_CONFIG['database_path']
        self.schema_path = Path(__file__).parent.parent / "schema.sql"
        
        self.log(f"数据库路径: {self.db_path}")
        self.log(f"Schema文件路径: {self.schema_path}")
    
    def validate_files(self):
        """验证必要文件是否存在"""
        self.log("步骤1: 验证文件存在性...")
        
        if not self.schema_path.exists():
            raise FileNotFoundError(f"Schema文件不存在: {self.schema_path}")
        
        self.log(f"Schema文件存在: {self.schema_path}")
        
        # 创建数据库目录（如果不存在）
        db_dir = Path(self.db_path).parent
        if not db_dir.exists():
            db_dir.mkdir(parents=True, exist_ok=True)
            self.log(f"创建数据库目录: {db_dir}")
        
        return True
    
    def backup_database(self):
        """备份现有数据库（如果存在）"""
        self.log("步骤2: 备份现有数据库...")
        
        if not Path(self.db_path).exists():
            self.log("数据库文件不存在，无需备份")
            return None
        
        # 创建备份文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"{self.db_path}.backup_{timestamp}"
        
        try:
            # 复制数据库文件
            import shutil
            shutil.copy2(self.db_path, backup_path)
            self.log(f"数据库已备份到: {backup_path}")
            return backup_path
        except Exception as e:
            self.log(f"备份失败: {e}", level="ERROR")
            raise
    
    def get_existing_tables(self):
        """获取现有表列表"""
        self.log("步骤3: 获取现有表列表...")
        
        if not Path(self.db_path).exists():
            self.log("数据库文件不存在，无现有表")
            return []
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取所有表名
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
                ORDER BY name
            """)
            
            tables = [row[0] for row in cursor.fetchall()]
            conn.close()
            
            self.log(f"发现现有表 ({len(tables)}个): {', '.join(tables)}")
            return tables
            
        except Exception as e:
            self.log(f"获取表列表失败: {e}", level="ERROR")
            raise
    
    def drop_all_tables(self, tables):
        """删除所有现有表"""
        if not tables:
            self.log("步骤4: 无表需要删除")
            return
        
        self.log(f"步骤4: 删除现有表 ({len(tables)}个)...")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 禁用外键约束（删除时）
            cursor.execute("PRAGMA foreign_keys = OFF")
            
            # 删除所有表
            for table in tables:
                self.log(f"  删除表: {table}")
                cursor.execute(f"DROP TABLE IF EXISTS {table}")
            
            # 删除所有视图
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='view'
                ORDER BY name
            """)
            views = [row[0] for row in cursor.fetchall()]
            
            for view in views:
                self.log(f"  删除视图: {view}")
                cursor.execute(f"DROP VIEW IF EXISTS {view}")
            
            # 删除所有索引
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='index' AND name NOT LIKE 'sqlite_%'
                ORDER BY name
            """)
            indexes = [row[0] for row in cursor.fetchall()]
            
            for index in indexes:
                self.log(f"  删除索引: {index}")
                cursor.execute(f"DROP INDEX IF EXISTS {index}")
            
            conn.commit()
            conn.close()
            
            self.log(f"成功删除 {len(tables)} 个表, {len(views)} 个视图, {len(indexes)} 个索引")
            
        except Exception as e:
            self.log(f"删除表失败: {e}", level="ERROR")
            raise
    
    def execute_schema(self):
        """执行schema.sql重建表结构"""
        self.log("步骤5: 执行schema.sql重建表结构...")
        
        try:
            # 读取schema.sql文件
            with open(self.schema_path, 'r', encoding='utf-8') as f:
                schema_sql = f.read()
            
            self.log(f"读取schema文件，长度: {len(schema_sql)} 字符")
            
            # 连接数据库并执行
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 启用外键约束
            cursor.execute("PRAGMA foreign_keys = ON")
            
            # 执行完整的schema脚本
            cursor.executescript(schema_sql)
            
            conn.commit()
            conn.close()
            
            self.log("Schema执行完成")
            
        except Exception as e:
            self.log(f"执行schema失败: {e}", level="ERROR")
            raise
    
    def verify_tables(self):
        """验证表创建结果"""
        self.log("步骤6: 验证表创建结果...")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取所有表
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
                ORDER BY name
            """)
            tables = [row[0] for row in cursor.fetchall()]
            
            # 获取所有视图
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='view'
                ORDER BY name
            """)
            views = [row[0] for row in cursor.fetchall()]
            
            # 获取所有索引
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='index' AND name NOT LIKE 'sqlite_%'
                ORDER BY name
            """)
            indexes = [row[0] for row in cursor.fetchall()]
            
            conn.close()
            
            self.log(f"创建完成统计:")
            self.log(f"  - 表 ({len(tables)}个): {', '.join(tables)}")
            self.log(f"  - 视图 ({len(views)}个): {', '.join(views)}")
            self.log(f"  - 索引 ({len(indexes)}个): {', '.join(indexes)}")
            
            # 验证核心表是否存在
            expected_tables = [
                'factor_batches', 'universes', 'factor_categories', 'factors',
                'factor_evaluations', 'factor_pools', 'factor_pool_members',
                'factor_tags', 'factor_relationships'
            ]
            
            missing_tables = [t for t in expected_tables if t not in tables]
            if missing_tables:
                self.log(f"警告: 缺少预期表: {missing_tables}", level="WARNING")
            else:
                self.log("所有核心表创建成功")
            
            return len(tables), len(views), len(indexes)
            
        except Exception as e:
            self.log(f"验证失败: {e}", level="ERROR")
            raise
    
    def insert_default_data(self):
        """插入默认数据"""
        self.log("步骤7: 插入默认数据...")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 插入默认因子分类
            default_categories = [
                ('TECHNICAL', '技术指标', None, 1, '基于价格、成交量的技术指标', 1),
                ('FUNDAMENTAL', '基本面', None, 1, '基于财务数据的基本面指标', 2),
                ('MOMENTUM', '动量因子', 'TECHNICAL', 2, '价格动量相关指标', 1),
                ('MEAN_REVERSION', '均值回归', 'TECHNICAL', 2, '均值回归相关指标', 2),
                ('VOLATILITY', '波动率', 'TECHNICAL', 2, '波动率相关指标', 3),
                ('VOLUME', '成交量', 'TECHNICAL', 2, '成交量相关指标', 4),
                ('PROFITABILITY', '盈利能力', 'FUNDAMENTAL', 2, '盈利能力指标', 1),
                ('GROWTH', '成长性', 'FUNDAMENTAL', 2, '成长性指标', 2),
                ('VALUATION', '估值', 'FUNDAMENTAL', 2, '估值指标', 3),
                ('QUALITY', '质量', 'FUNDAMENTAL', 2, '财务质量指标', 4),
            ]
            
            for cat_data in default_categories:
                cursor.execute("""
                    INSERT OR REPLACE INTO factor_categories 
                    (category_id, category_name, parent_category, category_level, description, sort_order)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, cat_data)
            
            # 插入默认股票池
            cursor.execute("""
                INSERT OR REPLACE INTO universes 
                (universe_id, version, universe_name, universe_type, static_symbols, 
                 effective_date, is_current, description)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                'CSI300', 1, '沪深300', 'index_components', '[]',
                '2020-01-01', True, '沪深300指数成分股'
            ))
            
            cursor.execute("""
                INSERT OR REPLACE INTO universes 
                (universe_id, version, universe_name, universe_type, static_symbols, 
                 effective_date, is_current, description)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                'CSI500', 1, '中证500', 'index_components', '[]',
                '2020-01-01', True, '中证500指数成分股'
            ))
            
            conn.commit()
            conn.close()
            
            self.log(f"默认数据插入完成")
            self.log(f"  - 因子分类: {len(default_categories)} 个")
            self.log(f"  - 股票池: 2 个")
            
        except Exception as e:
            self.log(f"插入默认数据失败: {e}", level="ERROR")
            raise
    
    def run_reset(self):
        """执行完整的重置流程"""
        self.log("=" * 60)
        self.log("开始执行 XentZ 因子动物园数据库重置")
        self.log("=" * 60)
        
        try:
            # 1. 验证文件
            self.validate_files()
            
            # 2. 备份数据库
            backup_path = self.backup_database()
            
            # 3. 获取现有表
            existing_tables = self.get_existing_tables()
            
            # 4. 删除所有表
            self.drop_all_tables(existing_tables)
            
            # 5. 执行schema
            self.execute_schema()
            
            # 6. 验证结果
            table_count, view_count, index_count = self.verify_tables()
            
            # 7. 插入默认数据
            self.insert_default_data()
            
            # 完成总结
            self.log("=" * 60)
            self.log("数据库重置完成！")
            self.log(f"数据库路径: {self.db_path}")
            if backup_path:
                self.log(f"备份路径: {backup_path}")
            self.log(f"创建统计: {table_count}表 + {view_count}视图 + {index_count}索引")
            self.log("=" * 60)
            
            return True
            
        except Exception as e:
            self.log("=" * 60, level="ERROR")
            self.log(f"数据库重置失败: {e}", level="ERROR")
            self.log("=" * 60, level="ERROR")
            raise


def main():
    """主函数"""
    print("XentZ 因子动物园 - 数据库重置工具")
    print("警告：此操作将删除所有现有数据！")
    
    # 确认操作
    confirm = input("确认要重置数据库吗？(输入 'YES' 确认): ")
    if confirm != 'YES':
        print("操作已取消")
        return
    
    try:
        reset_tool = DatabaseReset()
        reset_tool.run_reset()
        print("\n✓ 数据库重置成功完成！")
        
    except Exception as e:
        print(f"\n✗ 重置失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 