#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试批次管理文件查看功能
演示如何查看批次文件内容
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from factorzoo.scripts.batch_tools import BatchTools

def test_file_viewer():
    """测试文件查看功能"""
    print("📁 批次文件查看器测试")
    print("=" * 50)
    
    tools = BatchTools()
    
    # 获取可用批次
    all_batches = tools.manager.get_available_batches()
    
    if not all_batches:
        print("❌ 暂无批次数据，无法进行测试")
        return
    
    # 选择第一个批次进行测试
    test_batch = all_batches[0]
    print(f"🧪 测试批次: {test_batch}")
    print()
    
    # 显示文件列表
    file_list = tools.show_batch_files(test_batch)
    
    if not file_list:
        print("❌ 该批次没有文件")
        return
    
    # 自动查看第一个文件的内容
    print(f"\n🔍 自动查看第一个文件内容...")
    test_file = file_list[0]
    
    print(f"📄 正在查看: {test_file.name}")
    result = tools.show_file_content(test_file, head_rows=5)
    
    # 根据返回结果类型进行不同处理
    if hasattr(result, 'shape'):  # DataFrame
        print(f"\n✅ 成功读取DataFrame，形状: {result.shape}")
        print(f"📋 列数: {len(result.columns)}")
        print(f"📊 行数: {len(result)}")
        
        # 显示数据类型
        print(f"\n📈 数据类型概览:")
        print(result.dtypes.value_counts().to_string())
        
    elif isinstance(result, list):  # 文本文件
        print(f"\n✅ 成功读取文本文件，共 {len(result)} 行")
        
    elif isinstance(result, dict):  # JSON文件
        print(f"\n✅ 成功读取JSON文件，包含 {len(result)} 个键")
        
    else:
        print(f"\n✅ 文件内容读取完成")

def main():
    """主函数"""
    try:
        test_file_viewer()
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 