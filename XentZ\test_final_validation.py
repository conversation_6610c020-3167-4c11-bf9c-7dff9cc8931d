#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XentZ Factor Storage 最终验证测试 - 统一版本
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

print("XentZ FactorValueManager 统一版本最终验证")
print("=" * 50)

# 测试所有核心模块导入
test_results = {}

# 1. 测试性能配置
try:
    from factorzoo.config_cache import get_performance_config
    config = get_performance_config()
    config.optimize_for_workload('production')
    test_results['配置系统'] = True
    print("配置系统: 通过")
except Exception as e:
    test_results['配置系统'] = False
    print(f"配置系统: 失败 - {e}")

# 2. 测试高性能缓存
try:
    from factorzoo.cache import get_performance_cache
    cache = get_performance_cache()
    cache.clear_all_caches()
    test_results['缓存系统'] = True
    print("缓存系统: 通过")
except Exception as e:
    test_results['缓存系统'] = False
    print(f"缓存系统: 失败 - {e}")

# 3. 测试并行管理
try:
    from factorzoo.parallel import get_parallel_manager
    parallel = get_parallel_manager()
    stats = parallel.get_comprehensive_stats()
    test_results['并行系统'] = True
    print("并行系统: 通过")
except Exception as e:
    test_results['并行系统'] = False
    print(f"并行系统: 失败 - {e}")

# 4. 测试性能监控
try:
    from factorzoo.profiler import get_performance_monitor
    monitor = get_performance_monitor()
    monitor.start_monitoring()
    monitor.stop_monitoring()
    test_results['监控系统'] = True
    print("监控系统: 通过")
except Exception as e:
    test_results['监控系统'] = False
    print(f"监控系统: 失败 - {e}")

# 5. 测试统一因子管理器 - 基础模式
try:
    from factorzoo.factor_value_manager import FactorValueManager
    basic_manager = FactorValueManager(enable_performance_optimization=False)
    batches = basic_manager.get_available_batches()
    test_results['基础管理器'] = True
    print("基础管理器: 通过")
except Exception as e:
    test_results['基础管理器'] = False
    print(f"基础管理器: 失败 - {e}")

# 6. 测试统一因子管理器 - 高性能模式
try:
    from factorzoo.factor_value_manager import get_factor_value_manager
    perf_manager = get_factor_value_manager(enable_optimization=True)
    
    # 测试性能报告
    report = perf_manager.get_performance_report()
    
    # 测试批量加载功能
    batch_requests = [
        {'batch_id': 'test1', 'pipeline_step': 'L2'},
        {'batch_id': 'test2', 'pipeline_step': 'L3'}
    ]
    results = perf_manager.batch_load_multiple(batch_requests)
    
    test_results['高性能管理器'] = True
    print("高性能管理器: 通过")
except Exception as e:
    test_results['高性能管理器'] = False
    print(f"高性能管理器: 失败 - {e}")

# 7. 测试自动回退机制
try:
    # 模拟性能组件不可用的情况
    from factorzoo.factor_value_manager import FactorValueManager
    
    # 创建一个新实例来测试回退
    manager_with_fallback = FactorValueManager(enable_performance_optimization=True)
    
    # 应该能正常工作，无论优化是否可用
    info = manager_with_fallback.get_batch_info('test')
    test_results['自动回退'] = True
    print("自动回退: 通过")
except Exception as e:
    test_results['自动回退'] = False
    print(f"自动回退: 失败 - {e}")

# 统计结果
passed = sum(test_results.values())
total = len(test_results)
success_rate = passed / total * 100

print("\n" + "=" * 50)
print("统一版本验证结果")
print("=" * 50)
print(f"总测试数: {total}")
print(f"通过测试: {passed}")
print(f"失败测试: {total - passed}")
print(f"成功率: {success_rate:.1f}%")

if success_rate >= 80:
    print("\n*** 统一版本验证成功! ***")
    print("单一文件集成所有功能完成")
    
    print("\n核心特性:")
    if test_results.get('配置系统'):
        print("  ✓ 多场景性能配置")
    if test_results.get('缓存系统'):
        print("  ✓ 三级智能缓存")
    if test_results.get('并行系统'):
        print("  ✓ 并行加载优化")
    if test_results.get('监控系统'):
        print("  ✓ 实时性能监控")
    if test_results.get('基础管理器'):
        print("  ✓ 基础模式 (轻量级)")
    if test_results.get('高性能管理器'):
        print("  ✓ 高性能模式 (企业级)")
    if test_results.get('自动回退'):
        print("  ✓ 智能回退机制")
    
    print("\n使用方式:")
    print("  # 高性能模式 (推荐)")
    print("  from factorzoo.factor_value_manager import get_factor_value_manager")
    print("  manager = get_factor_value_manager()")
    print("")
    print("  # 基础模式 (调试用)")
    print("  from factorzoo.factor_value_manager import FactorValueManager")
    print("  manager = FactorValueManager(enable_performance_optimization=False)")
    
    print("\n架构优势:")
    print("  * 单一文件维护，降低复杂度")
    print("  * 自动优化检测和回退")
    print("  * 完全向后兼容")
    print("  * 渐进式功能启用")
    
    print("\n项目简化完成!")
    
else:
    print(f"\n警告: 部分测试失败 ({success_rate:.1f}%)")
    print("请检查失败的模块:")
    for name, passed in test_results.items():
        if not passed:
            print(f"  × {name}")

print("\n统一版本验证完成!") 