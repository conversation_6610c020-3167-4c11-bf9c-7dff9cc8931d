# XentZ 快速入门指南

## 1. 环境准备

### 1.1 系统要求
- Python 3.12+
- Windows 10/11 (推荐) 或 Linux
- 内存 8GB+ (推荐16GB+)
- 硬盘空间 10GB+

### 1.2 安装依赖

```bash
# 克隆项目
git clone <project_url>
cd XentZ

# 安装核心依赖
pip install hikyuu pandas numpy loguru dynaconf psutil

# 安装特征工程依赖
pip install tsfresh ta-lib scikit-learn scipy

# 安装回测框架
pip install backtrader

# 安装可视化依赖
pip install matplotlib seaborn plotly tqdm

# 安装项目
pip install -e .
```

### 1.3 初始化配置

```python
# 设置环境变量（可选）
import os
os.environ["ENV_FOR_DYNACONF"] = "default"  # 或 "prod"
```

## 2. 基础使用

### 2.1 数据加载

```python
from hikyuu.interactive import *
from datafeed.hku_dataloader import HKUDataloader

# 加载单个品种数据
df_single = HKUDataloader.load_df_all(
    symbols=['SH000300'], 
    start_date='20200101',
    end_date='20231231',
    freq='D',
    set_index=True
)

# 加载多个品种数据
df_multi = HKUDataloader.load_df_all(
    symbols=['SH000300', 'SZ399006', 'SH000016'], 
    start_date='20200101',
    freq='D',
    set_index=False  # 包含symbol列
)

print(f"加载了 {len(df_multi)} 条数据")
print(f"包含品种: {df_multi['symbol'].unique()}")
```

### 2.2 数据预处理

```python
from datafeed.features.feature_utils import FeatPreprocessing

# 缺失值处理
df_filled = FeatPreprocessing.fill_missing(
    df_multi, method='ffill', limit=5
)

# 异常值处理
df_clipped = FeatPreprocessing.mad_clip_df(
    df_filled, k=3
)

# 数据标准化
df_normalized = FeatPreprocessing.norm_df(
    df_clipped, 
    window=500,
    algomode=0,  # Z-Score标准化
    demean=True
)

print("数据预处理完成")
```

### 2.3 特征工程

```python
from datafeed.features.feature_utils import FeatEngineering

# 创建滞后特征
df_lag = FeatEngineering.create_lag_features(
    df_multi,
    lags=[1, 5, 10],
    column_value='close'
)

# 创建滚动窗口特征  
df_rolling = FeatEngineering.create_rolling_features(
    df_multi,
    windows=[5, 10, 20],
    funcs=['mean', 'std', 'max', 'min'],
    column_value='close'
)

# TSFresh特征提取（可选，需要较长时间）
# df_tsfresh = FeatEngineering.extract_tsfresh_features(
#     df_multi,
#     feature_set='efficient',
#     column_value='close',
#     n_jobs=2
# )

print("特征工程完成")
```

### 2.4 配置管理

```python
from config.settings import *

# 查看当前配置
norm_params = get_norm_params('linear', 'X')
print(f"标准化参数: {norm_params}")

missing_mode = get_missing_mode()
print(f"缺失值处理模式: {missing_mode}")

# 动态更新配置
success = update_config(1000, 'norm', 'window')
print(f"配置更新成功: {success}")
```

## 3. 进阶功能

### 3.1 自定义特征函数

```python
from datafeed.features.feature_utils import calc_df_by_symbol
import pandas as pd

@calc_df_by_symbol
def calculate_rsi(df: pd.DataFrame, window: int = 14, **kwargs):
    """计算RSI指标"""
    current_symbol = kwargs.get('current_symbol')
    
    # 计算价格变化
    delta = df['close'].diff()
    
    # 分离上涨和下跌
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    
    # 计算平均收益和损失
    avg_gain = gain.rolling(window=window).mean()
    avg_loss = loss.rolling(window=window).mean()
    
    # 计算RSI
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    
    df_result = df.copy()
    df_result['rsi'] = rsi
    
    return df_result

# 使用自定义函数
df_rsi = calculate_rsi(df_multi, window=14)
print("RSI计算完成")
```

### 3.2 性能监控

```python
from common.cls_base import ResMonitor

# 性能监控示例
rm = ResMonitor()

rm.start_timer("feature_processing")
rm.log_memory_usage("开始特征处理")

# 执行特征处理
df_features = FeatEngineering.create_rolling_features(
    df_multi, windows=[5, 10, 20], column_value='close'
)

rm.log_memory_usage("特征处理完成")
elapsed = rm.end_timer("feature_processing")
print(f"处理耗时: {elapsed}")
```

### 3.3 数据分析和可视化

```python
from datafeed.features.feature_utils import FeatDistribution

# 统计分析
stats_result = FeatDistribution.analyze_stats(
    df_normalized,
    save_path="output/stats_analysis.csv"
)
print("统计分析完成，结果已保存")

# 绘制分布图（需要配置保存路径）
# FeatDistribution.plot_hist(
#     df_normalized,
#     save_path="output/distribution.png",
#     grid_size=(4, 3)
# )

# 绘制累积曲线
# FeatDistribution.plot_curve(
#     df_normalized,
#     save_path="output/curves.png", 
#     show_cumulative=True
# )
```

## 4. 策略开发

### 4.1 Hikyuu策略示例

```python
# Hikyuu策略开发示例
from hikyuu.interactive import *

# 配置Hikyuu环境
sm.setParam("baseInfoDriverParameter", {"test": "pytest"})
sm.setParam("kDataDriverParameter", {"type": "memory"})
sm.load()

# 获取股票对象
stock = sm['SH000300']

# 简单移动平均策略
def create_ma_strategy():
    # 市场有效条件：总是有效
    cn = crtCN_False()
    
    # 信号指示器：双均线策略
    ma5 = MA(5)
    ma20 = MA(20)
    sg = crtSG_Cross(ma5, ma20)
    
    # 止损策略
    st = crtST_FixedPercent(0.03)  # 3%止损
    
    # 资金管理
    mm = crtMM_FixedCount(100)  # 固定100股
    
    # 创建交易系统
    sys = crtSYS_Simple(sg, mm, ev=cn, st=st)
    
    return sys

# 运行策略
sys = create_ma_strategy()
result = sys.run(stock, Query(-100))
print(f"交易记录数: {len(sys.transactionList)}")
```

### 4.2 BackTrader策略示例

```python
import backtrader as bt
from datafeed.hku_dataloader import HKUDataloader

class MAStrategy(bt.Strategy):
    params = (
        ('ma_period', 20),
    )
    
    def __init__(self):
        self.ma = bt.indicators.MovingAverageSimple(
            self.data.close, period=self.params.ma_period
        )
        
    def next(self):
        if not self.position:
            if self.data.close[0] > self.ma[0]:
                self.buy()
        else:
            if self.data.close[0] < self.ma[0]:
                self.sell()

# 准备数据
df_bt = HKUDataloader.load_a_backtrader_df(
    'SH000300', 
    start_date='20200101',
    end_date='20231231'
)

# 创建Cerebro引擎
cerebro = bt.Cerebro()
cerebro.addstrategy(MAStrategy)

# 添加数据
data = bt.feeds.PandasData(dataname=df_bt)
cerebro.adddata(data)

# 设置初始资金
cerebro.broker.setcash(100000.0)

# 运行回测
print(f'初始资金: {cerebro.broker.getvalue():.2f}')
cerebro.run()
print(f'最终资金: {cerebro.broker.getvalue():.2f}')
```

## 5. 工作流示例

### 5.1 完整的特征工程流水线

```python
from hikyuu.interactive import *
from datafeed.hku_dataloader import HKUDataloader
from datafeed.features.feature_utils import *
from config.settings import *
from common.cls_base import ResMonitor

def feature_engineering_pipeline(symbols, start_date='20200101'):
    """完整的特征工程流水线"""
    
    rm = ResMonitor()
    rm.start_timer("total_pipeline")
    
    # 1. 数据加载
    rm.log_memory_usage("开始数据加载")
    df = HKUDataloader.load_df_all(
        symbols=symbols,
        start_date=start_date,
        freq='D',
        set_index=False
    )
    rm.log_memory_usage(f"数据加载完成，共{len(df)}条记录")
    
    # 2. 数据预处理
    df = FeatPreprocessing.fill_missing(df, method='ffill')
    df = FeatPreprocessing.mad_clip_df(df, k=3)
    rm.log_memory_usage("数据预处理完成")
    
    # 3. 特征工程
    df_lag = FeatEngineering.create_lag_features(
        df, lags=[1, 5, 10], column_value='close'
    )
    
    df_rolling = FeatEngineering.create_rolling_features(
        df, windows=[5, 10, 20], 
        funcs=['mean', 'std'], 
        column_value='close'
    )
    
    # 合并特征
    df_features = df.merge(df_lag, left_index=True, right_index=True, how='left')
    df_features = df_features.merge(df_rolling, left_index=True, right_index=True, how='left')
    
    rm.log_memory_usage("特征工程完成")
    
    # 4. 数据标准化
    numeric_cols = df_features.select_dtypes(include=['float64', 'int64']).columns
    numeric_cols = [col for col in numeric_cols if col not in ['symbol']]
    
    df_norm = FeatPreprocessing.norm_df(
        df_features[numeric_cols + ['symbol']], 
        window=500,
        algomode=0
    )
    
    rm.log_memory_usage("数据标准化完成")
    
    elapsed = rm.end_timer("total_pipeline")
    print(f"流水线总耗时: {elapsed}")
    
    return df_norm

# 运行流水线
symbols = ['SH000300', 'SZ399006']
result_df = feature_engineering_pipeline(symbols)

print(f"最终特征数量: {len(result_df.columns)}")
print(f"数据行数: {len(result_df)}")
```

### 5.2 多框架策略对比

```python
def compare_strategies(symbol='SH000300', start_date='20200101'):
    """对比Hikyuu和BackTrader策略结果"""
    
    # Hikyuu策略
    stock = sm[symbol]
    sys_hku = create_ma_strategy()
    sys_hku.run(stock, Query(-1000))
    
    returns_hku = []
    for trade in sys_hku.transactionList:
        returns_hku.append(trade.realPrice)
    
    # BackTrader策略  
    df_bt = HKUDataloader.load_a_backtrader_df(symbol, start_date=start_date)
    cerebro = bt.Cerebro()
    cerebro.addstrategy(MAStrategy)
    cerebro.adddata(bt.feeds.PandasData(dataname=df_bt))
    cerebro.broker.setcash(100000.0)
    
    final_value = cerebro.run()
    
    print(f"Hikyuu策略交易次数: {len(returns_hku)}")
    print(f"BackTrader最终资金: {cerebro.broker.getvalue():.2f}")
    
    return returns_hku, cerebro.broker.getvalue()

# 运行对比
# hku_returns, bt_value = compare_strategies()
```

## 6. 故障排除

### 6.1 常见问题

#### 问题1: ImportError: No module named 'hikyuu'
**解决方案:**
```bash
pip install hikyuu
# 或从源码安装
git clone https://github.com/fasiondog/hikyuu.git
cd hikyuu
python setup.py install
```

#### 问题2: TSFresh特征提取速度慢
**解决方案:**
```python
# 使用高效特征集
df_tsfresh = FeatEngineering.extract_tsfresh_features(
    df, feature_set='efficient',  # 而不是'comprehensive'
    n_jobs=4  # 增加并行进程数
)
```

#### 问题3: 内存不足
**解决方案:**
```python
# 分批处理大数据集
def process_in_batches(df, batch_size=1000):
    results = []
    for i in range(0, len(df), batch_size):
        batch = df.iloc[i:i+batch_size]
        result = process_features(batch)
        results.append(result)
    return pd.concat(results)
```

### 6.2 调试技巧

```python
# 启用详细日志
from common.cls_base import BaseObj

class MyProcessor(BaseObj):
    def process(self, df):
        self.log("开始处理数据", level="DEBUG")
        
        # 检查数据完整性
        if df.empty:
            self.log("数据为空", level="WARNING")
            return df
            
        self.log(f"处理{len(df)}条记录", level="INFO")
        # 处理逻辑...
        
        self.log("处理完成", level="INFO")
        return result

# 性能分析
import cProfile

def profile_function():
    # 你的代码
    pass

cProfile.run('profile_function()')
```

## 7. 下一步

现在你已经掌握了XentZ的基础使用方法，可以：

1. **深入学习**: 阅读[技术文档](technical.md)和[API文档](api_reference.md)
2. **策略开发**: 参考`mystrat/`目录下的策略示例
3. **自定义扩展**: 开发自己的特征函数和策略模块
4. **实盘交易**: 配置QMT接口进行实盘交易
5. **社区交流**: 参与Hikyuu社区讨论

## 8. 资源链接

- [项目GitHub](https://github.com/your-repo)
- [Hikyuu文档](https://hikyuu.readthedocs.io/zh_CN/latest/)
- [BackTrader文档](https://backtrader.readthedocs.io/)
- [TSFresh文档](https://tsfresh.readthedocs.io/)
- [问题反馈](https://github.com/your-repo/issues)

祝你在量化交易的道路上取得成功！🚀 