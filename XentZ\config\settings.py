from pathlib import Path
import os
from dynaconf import Dynaconf
from functools import lru_cache

from common.cls_base import BaseObj

config_dir = Path(__file__).parent
# 获取环境变量
ENV = os.getenv("ENV_FOR_DYNACONF", "default")

# 初始化Dynaconf
settings = Dynaconf(
    environments=False,
    settings_files=[
        f"{config_dir}/{ENV}/research.toml",
        f"{config_dir}/{ENV}/backtest.toml", 
        f"{config_dir}/{ENV}/live.toml",
    ],
    # core_loaders=['tomli'], 不能要这行
    silent=False,
)
# ============================== 命名约定 ============================== #
def get_label_single_tdelay():
    return settings.get('label.single.tdelay', 1)

def get_label_single_names():
    return settings.get('label.single.names', ['label_1'])

def get_label_multiple_tdelay():
    return settings.get('label.multiple.tdelay', [1, 5, 10, 20])

def get_label_multiple_names():
    return settings.get('label.multiple.names', ['label_1', 'label_5', 'label_10', 'label_20'])

# ============================== 数据预处理 ============================== #
def get_missing_mode():
    """获取缺失值处理模式"""
    return settings.get('missing.mode', 'ffill')

def get_outliers_params():
    """根据 mode 获取对应的异常值处理参数"""
    mode = settings.get("outliers.mode", None)

    if mode is None:
        return {}

    param_key = f"outliers.{mode}"
    params = settings.get(param_key, {}) # params都是dict, key对应入参字段
    
    BaseObj.log(f"mode: {mode}, params: {params}", level="DEBUG")
    
    return params

def get_norm_cols():
    """获取归一化列"""
    return {
        'base2keep': settings.get('norm.cols.base2keep', []),
        'base2norm': settings.get('norm.cols.base2norm', []),
        'feat2keep': settings.get('norm.cols.prefix.feat2keep', ''),
        'featlabel': settings.get('norm.cols.prefix.featlabel', ''),
    }

@lru_cache(maxsize=32)
def get_norm_params(model_type="linear", data_type="X"):
    """获取归一化参数"""
    res = settings.norm[model_type][data_type]
    # BaseObj.log(f"{res}", level="DEBUG")
    
    return res

# ============================== 特征配置 ============================== #
def get_feat_norm_model():
    """获取特征归一化模型"""
    return settings.get('feat.norm.model', 'robust')

def get_feat_drop_names():
    """获取需要删除的特征列名"""
    return settings.get('feat.drop.names', [])

def get_feat_selected_names():
    """获取需要选择的特征列名"""
    return settings.get('feat.selected.names', [])

# ============================== 挖掘配置 ============================== #
def get_mine_norm_model():
    """获取挖掘时, 归一化模型"""
    return settings.get('mine.norm.model', 'linear')

def get_mine_run_perf_daybars():
    """获取挖掘时, 用日线"""
    return settings.get('mine.run.perf.daybars', 1)

def get_mine_run_perf_anndays():
    """获取挖掘时, 年化天数"""
    return settings.get('mine.run.perf.anndays', 252)

def get_mine_run_nextbar():
    """获取挖掘时, 是否延迟到下一bar开盘open, 否则用close"""
    return settings.get('mine.run.nextbar', True)

def get_mine_run_jobnum():
    """获取挖掘时, 用-1表示使用所有可用核心"""
    return settings.get('mine.run.jobnum', -1)

def get_mine_run_fee():
    """获取挖掘时, 手续费"""
    return settings.get('mine.run.fee', 0.002)

def get_mine_run_free():
    """获取挖掘时, 无风险利率"""
    return settings.get('mine.run.free', 0.03)

def get_mine_run_mode():
    """获取挖掘时, 运行模式"""
    return settings.get('mine.run.mode', 'test')

def get_mine_filter_skewthresh():
    """获取挖掘后筛选时, 偏度阈值"""
    return settings.get('mine.filter.skewthresh', 0.5)

def get_mine_filter_kurtthresh():
    """获取挖掘后筛选时, 峰度阈值"""
    return settings.get('mine.filter.kurtthresh', 5)

def get_mine_filter_corrthresh():
    """获取挖掘后筛选时, 相关性阈值"""
    return settings.get('mine.filter.corrthresh', 0.3)

def get_mine_filter_bymetric():
    """获取挖掘后筛选时, 用什么指标筛选"""
    return settings.get('mine.filter.bymetric', 'sic')

def get_mine_filter_sr_thresh():
    """获取挖掘后筛选时, 夏普比率阈值"""
    return settings.get('mine.filter.sr.srthresh', 0.8)

def get_mine_filter_pjsr_thresh():
    """获取挖掘后筛选时, 夏普比率阈值"""
    return settings.get('mine.filter.sr.pjsrthresh', 0.2)

def get_mine_filter_rankic_tdelay():
    """获取挖掘后筛选时, 用t期收益率算ic"""
    return settings.get('mine.filter.rankic.tdelay', 1)

# ============================== 配置文件管理函数 ============================== #
def update_config(value, *keys):
    """更新配置
    
    Args:
        value: 要设置的值
        *keys: 配置路径，如 'norm', 'window'
    
    Returns:
        bool: 是否更新成功
    """
    if not keys:
        return False
        
    # 构建配置路径
    config_path = '.'.join(keys)
    
    # 确定要更新的文件
    section = keys[0]
    config_dir = Path(f"config/{ENV}")
    file_path = config_dir / f"{section}.toml"
    
    try:
        # 更新设置
        settings.set(config_path, value)
        
        # 确保目录存在
        config_dir.mkdir(parents=True, exist_ok=True)
        
        # 写入文件
        settings.write(file_path, encoding='utf-8')
        
        # 清除缓存
        get_norm_params.cache_clear()
        
        return True
    except Exception as e:
        print(f"更新配置失败: {e}")
        return False

def reload_config():
    """重新加载配置"""
    settings.reload()
    get_norm_params.cache_clear()

if __name__ == '__main__':
    # reload_config()
    x = get_norm_params(model_type="linear", data_type="label")
    print(x)
    