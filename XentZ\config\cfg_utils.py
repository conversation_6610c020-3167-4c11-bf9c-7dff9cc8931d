from pathlib import Path
from dynaconf import Dynaconf

# 避免循环导入：直接计算ROOT_DIR
ROOT_DIR = Path(__file__).parent.parent

cfg_path = ROOT_DIR / "config" / "tasks" / "ts_feat.toml"
cfg_feat = Dynaconf(settings_files=[str(cfg_path)], encoding="utf-8", silent=True)
cfg_path = ROOT_DIR / "config" / "tasks" / "ts_gplearn.toml"
cfg_mine = Dynaconf(settings_files=[str(cfg_path)], encoding="utf-8", silent=True)
cfg_path = ROOT_DIR / "config" / "tasks" / "ts_l3_wfa.toml"
cfg_wfa = Dynaconf(settings_files=[str(cfg_path)], encoding="utf-8", silent=True)

def _convert_dynabox_to_dict(obj):
    """将DynaBox对象转换为普通字典"""
    if hasattr(obj, 'to_dict'):
        return obj.to_dict()
    elif isinstance(obj, dict):
        # 递归处理嵌套的DynaBox对象
        return {k: _convert_dynabox_to_dict(v) for k, v in obj.items()}
    else:
        return obj

def get_norm_params(model_type: str, norm_type: str):
    """返回挖掘模型norm参数"""
    norm_params = cfg_mine.get(f'norm.{model_type}.{norm_type}')
    return _convert_dynabox_to_dict(norm_params)

def mine_norm_params_X():
    """返回挖掘模型norm参数"""
    model_type = cfg_mine.get('mine.norm.model')
    X_params = cfg_mine.get(f'norm.{model_type}.X')
    return _convert_dynabox_to_dict(X_params)

def mine_gparams():
    # 获取基础参数
    base_params = {k: v for k, v in cfg_mine.gparams.items() 
                   if not k.startswith(('@', 'dynaconf_'))}
    current_mode = cfg_mine.workflow.mode
    mode_params = cfg_mine.mode[current_mode].gparams
    # 合并两个字典，并确保转换DynaBox对象
    final_params = {**_convert_dynabox_to_dict(base_params), **_convert_dynabox_to_dict(mode_params)}
    
    # 特殊处理：将init_depth转换为元组
    if 'init_depth' in final_params:
        init_depth = final_params['init_depth']
        if isinstance(init_depth, str) and init_depth.startswith('(') and init_depth.endswith(')'):
            # 安全地评估字符串为元组
            final_params['init_depth'] = eval(init_depth)
        elif isinstance(init_depth, list):
            # 如果是列表，转换为元组
            final_params['init_depth'] = tuple(init_depth)
    
    return final_params

def mine_runnum():
    """返回挖掘轮次"""
    current_mode = cfg_mine.workflow.mode
    return cfg_mine.mode[current_mode].mine.runnum

def fitness_params():
    """返回metric参数字典，用于**kwargs解包"""
    return {
        'day_bars': cfg_mine.mine.run.perf.daybars,
        'ann_days': cfg_mine.mine.run.perf.anndays,
        'fixed_return': cfg_mine.mine.run.free,
        'fee_rate': cfg_mine.mine.run.fee,
        'nextbar_open': cfg_mine.mine.run.nextbar,
        'norm_pos_params': get_norm_params(cfg_mine.mine.norm.model, 'pos')
    }
    
def sr_params():
    """返回夏普比率筛选因子参数字典，用于**kwargs解包"""
    return {
        'sr_thresh': cfg_mine.mine.filter.sr.srthresh,
        'pjsr_thresh': cfg_mine.mine.filter.sr.pjsrthresh,
        'day_bars': cfg_mine.mine.run.perf.daybars,
        'ann_days': cfg_mine.mine.run.perf.anndays,
        'fixed_return': cfg_mine.mine.run.free,
        'fee_rate': cfg_mine.mine.run.fee,
        'nextbar_open': cfg_mine.mine.run.nextbar,
        'norm_pos_params': get_norm_params(cfg_mine.mine.norm.model, 'pos')
    }
    
def rankic_params():
    """返回rankic完整参数字典，用于**kwargs解包"""
    return {
        'window': cfg_mine.mine.filter.rankic.window,
        'min_period': cfg_mine.mine.filter.rankic.minperiod,
        'top_pct': cfg_mine.mine.filter.rankic.toppct,
        'pj_pct': cfg_mine.mine.filter.rankic.pjpct,
        'norm_pos_params': get_norm_params(cfg_mine.mine.norm.model, 'pos')
    }

if __name__ == "__main__":
    print(mine_gparams())