#!/usr/bin/env python
# -*- coding: utf-8 -*- 
'''
<AUTHOR> 木头左
@Date         : 2025-01-03 08:35:37
@LastEditTime : 2025-03-11 08:54:07
'''
import akshare as ak
import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
from datetime import datetime, <PERSON><PERSON><PERSON>

def select_etf_pool_for_rotation(returns_df, lookback_period=252, max_etfs=15, 
                                min_etfs=8, corr_threshold=0.7):
    """
    为轮动策略选择ETF池 - 专为后续遗传算法优化权重设计
    
    参数:
    returns_df: ETF收益率DataFrame
    lookback_period: 回看期(默认一年)
    max_etfs: 最大ETF数量
    min_etfs: 最小ETF数量
    corr_threshold: 相关性阈值，高于此值的ETF对将被筛选
    
    返回:
    selected_etfs: 选中的ETF列表
    """
    import numpy as np
    import pandas as pd
    
    # 计算相关性矩阵
    corr_matrix = returns_df.corr()
    
    # 计算每个ETF的关键指标
    etf_metrics = {}
    for etf in returns_df.columns:
        # 计算年化收益率
        annual_return = returns_df[etf].mean() * 252
        
        # 计算年化波动率
        annual_vol = returns_df[etf].std() * np.sqrt(252)
        
        # 计算最大回撤
        cum_returns = (1 + returns_df[etf]).cumprod()
        running_max = cum_returns.cummax()
        drawdown = (cum_returns / running_max) - 1
        max_drawdown = drawdown.min()
        
        # 计算夏普比率 (假设无风险利率为3%)
        sharpe = (annual_return - 0.03) / annual_vol if annual_vol > 0 else 0
        
        # 计算与其他ETF的平均相关性
        avg_corr = corr_matrix[etf].mean()
        
        # 计算信息多样性 (1 - 平均相关性)
        info_diversity = 1 - avg_corr
        
        # 计算初始综合得分
        diversity_score = info_diversity
        sharpe_score = max(0, sharpe)  # 确保非负
        drawdown_score = 1 / (abs(max_drawdown) + 0.01)  # 避免除以0
        drawdown_score = min(1, drawdown_score / 10)  # 限制在0-1之间
        
        # 综合评分
        score = 0.5 * diversity_score + 0.3 * sharpe_score + 0.2 * drawdown_score
        
        etf_metrics[etf] = {
            'return': annual_return,
            'volatility': annual_vol,
            'max_drawdown': max_drawdown,
            'sharpe': sharpe,
            'avg_corr': avg_corr,
            'info_diversity': info_diversity,
            'score': score  # 初始化综合得分
        }
    
    # 第一步：按信息多样性排序，选择前max_etfs*2个ETF作为候选
    sorted_by_diversity = sorted(etf_metrics.items(), 
                                key=lambda x: x[1]['info_diversity'], 
                                reverse=True)
    
    candidate_etfs = [item[0] for item in sorted_by_diversity[:max_etfs*2]]
    
    # 第二步：使用贪心算法选择最终ETF池
    selected_etfs = []
    
    # 首先选择信息多样性最高的ETF
    selected_etfs.append(candidate_etfs[0])
    
    # 贪心选择剩余ETF
    while len(selected_etfs) < max_etfs and len(candidate_etfs) > 0:
        best_etf = None
        best_score = float('-inf')
        
        for etf in candidate_etfs:
            if etf in selected_etfs:
                continue
            
            # 计算与已选ETF的相关系数
            corrs = [corr_matrix.loc[etf, selected] for selected in selected_etfs]
            max_corr = max(corrs)
            
            # 如果相关性太高，跳过
            if max_corr > corr_threshold:
                continue
            
            # 计算评分 (考虑多个因素)
            # 1. 信息多样性 (权重0.4)
            # 2. 夏普比率 (权重0.3)
            # 3. 最大回撤的绝对值倒数 (权重0.3) - 回撤越小越好
            diversity_score = etf_metrics[etf]['info_diversity']
            sharpe_score = max(0, etf_metrics[etf]['sharpe'])  # 确保非负
            drawdown_score = 1 / (abs(etf_metrics[etf]['max_drawdown']) + 0.01)  # 避免除以0
            
            # 标准化得分
            drawdown_score = min(1, drawdown_score / 10)  # 限制在0-1之间
            
            # 综合评分
            score = 0.5 * diversity_score + 0.3 * sharpe_score + 0.2 * drawdown_score
            
            if score > best_score:
                best_score = score
                best_etf = etf
        
        # 如果找不到合适的ETF，或已达到最小要求，则停止
        if best_etf is None:
            if len(selected_etfs) >= min_etfs:
                break
            else:
                # 放宽相关性限制，继续寻找
                corr_threshold += 0.1
                continue
        
        # 更新最佳ETF的综合得分
        etf_metrics[best_etf]['score'] = best_score
        
        # 添加最佳ETF到选择列表
        selected_etfs.append(best_etf)
    
    # 打印选择结果
    print(f"\n为轮动策略选择的{len(selected_etfs)}个ETF池:")
    print("代码\t综合得分\t年化收益\t波动率\t最大回撤\t夏普比率\t信息多样性")
    for etf in selected_etfs:
        metrics = etf_metrics[etf]
        print(f"{etf}\t{metrics['score']:.4f}\t{metrics['return']:.2%}\t{metrics['volatility']:.2%}\t"
              f"{metrics['max_drawdown']:.2%}\t{metrics['sharpe']:.2f}\t"
              f"{metrics['info_diversity']:.2f}")
    
    # 计算并打印选中ETF之间的相关性矩阵
    sub_matrix = corr_matrix.loc[selected_etfs, selected_etfs]
    print("\n选中ETF之间的相关性矩阵:")
    print(sub_matrix)
    
    # 计算平均相关系数
    corr_values = []
    for i in range(len(selected_etfs)):
        for j in range(i+1, len(selected_etfs)):
            corr_values.append(sub_matrix.iloc[i, j])
    
    avg_corr = sum(corr_values) / len(corr_values) if corr_values else 0
    print(f"\n选中ETF的平均相关系数: {avg_corr:.4f}")
    
    return selected_etfs

def select_etf_portfolio(corr_matrix, max_correlation=0.5, min_etfs=3, max_etfs=6, 
                         target_etfs=None, balance_factor=0.8, penalize_high_corr=True):
    """
    从相关性矩阵中选择低相关性的ETF组合
    
    参数:
    corr_matrix: 相关性矩阵DataFrame
    max_correlation: 最大允许正相关系数
    min_etfs: 最少选择的ETF数量
    max_etfs: 最多选择的ETF数量
    target_etfs: 目标ETF数量，如果设置，算法会尝试选择这么多ETF
    balance_factor: 平衡因子(0-1)，越接近1越重视低相关性，越接近0越重视ETF数量
    penalize_high_corr: 是否惩罚高相关性
    
    返回:
    selected_etfs: 选中的ETF列表
    """
    import numpy as np
    
    # 获取所有ETF代码
    all_etfs = list(corr_matrix.columns)
    
    # 如果设置了target_etfs，则尝试选择这么多ETF
    if target_etfs is not None and target_etfs > 0:
        target_count = min(target_etfs, max_etfs)
    else:
        target_count = max_etfs
    
    # 尝试每个ETF作为起点，选择相关性最低的组合
    best_portfolios = []  # 存储多个候选组合
    
    for start_etf in all_etfs:
        # 贪心算法选择低相关ETF
        selected_etfs = [start_etf]
        
        # 贪心选择剩余ETF
        while len(selected_etfs) < max_etfs:
            best_etf = None
            best_score = float('inf')
            
            # 遍历所有未选择的ETF
            for etf in all_etfs:
                if etf in selected_etfs:
                    continue
                    
                # 计算与已选ETF的相关系数
                corrs = [corr_matrix.loc[etf, selected] for selected in selected_etfs]
                
                # 使用平均相关系数和最大相关系数的加权和作为评分
                # 这样可以同时考虑整体相关性和极端相关性
                avg_corr = sum(corrs) / len(corrs)
                max_corr = max(corrs)
                
                # 评分 = 70% * 最大相关系数 + 30% * 平均相关系数
                # 这样可以更强调避免高相关性
                score = 0.7 * max_corr + 0.3 * avg_corr
                
                # 更新最佳ETF (寻找相关性最低的ETF)
                if score < best_score:
                    best_score = score
                    best_etf = etf
            
            # 如果最佳相关性超过阈值，且已选ETF数量达到最小要求，则停止
            # 使用最大相关系数作为判断标准，而不是评分
            if max_corr > max_correlation and len(selected_etfs) >= min_etfs:
                break
                
            # 添加最佳ETF到选择列表
            if best_etf:
                selected_etfs.append(best_etf)
            else:
                break
        
        # 如果选择的ETF数量达到最小要求，则记录当前组合
        if len(selected_etfs) >= min_etfs:
            # 计算相关系数矩阵
            sub_matrix = corr_matrix.loc[selected_etfs, selected_etfs]
            
            # 计算所有相关系数（不包括对角线）
            corr_values = []
            for i in range(len(selected_etfs)):
                for j in range(i+1, len(selected_etfs)):
                    corr_values.append(sub_matrix.iloc[i, j])
            
            # 计算平均相关系数和最大相关系数
            avg_corr = sum(corr_values) / len(corr_values) if corr_values else 0
            max_corr = max(corr_values) if corr_values else 0
            
            # 计算正相关和负相关的统计信息（仅用于显示）
            pos_corrs = [c for c in corr_values if c >= 0]
            neg_corrs = [c for c in corr_values if c < 0]
            
            avg_pos_corr = sum(pos_corrs) / len(pos_corrs) if pos_corrs else 0
            avg_neg_corr = sum(neg_corrs) / len(neg_corrs) if neg_corrs else 0
            max_pos_corr = max(pos_corrs) if pos_corrs else 0
            min_neg_corr = min(neg_corrs) if neg_corrs else 0
            neg_corr_ratio = len(neg_corrs) / len(corr_values) if corr_values else 0
            
            # 计算评分 - 更强调相关性质量而非数量
            etf_count_ratio = len(selected_etfs) / target_count
            
            # 基础评分 (较高的分数更好)
            # 增加balance_factor默认值到0.8，更强调相关性
            score = balance_factor * (1 - avg_corr) + (1 - balance_factor) * etf_count_ratio
            
            # 更强力地惩罚高相关性
            if penalize_high_corr and max_pos_corr > max_correlation:
                # 惩罚系数：超出阈值的部分乘以一个更大的惩罚因子
                penalty = (max_pos_corr - max_correlation) * 3
                score = score * (1 - penalty)
            
            # 检查是否已经有相同的组合（不考虑顺序）
            portfolio_set = set(selected_etfs)
            if not any(set(p[0]) == portfolio_set for p in best_portfolios):
                best_portfolios.append((selected_etfs, score, avg_corr, max_corr, 
                                       avg_pos_corr, max_pos_corr, avg_neg_corr, 
                                       min_neg_corr, neg_corr_ratio))
    
    # 按评分排序，选择最佳组合
    best_portfolios.sort(key=lambda x: x[1], reverse=True)
    
    # 打印前三个最佳组合供参考
    print("\n前三个最佳ETF组合:")
    for i, (portfolio, score, avg_corr, max_corr, avg_pos, max_pos, 
            avg_neg, min_neg, neg_ratio) in enumerate(best_portfolios[:3], 1):
        print(f"组合{i}: {len(portfolio)}个ETF, 评分: {score:.4f}")
        print(f"  总体相关性: 平均={avg_corr:.4f}, 最大={max_corr:.4f}")
        print(f"  正相关: 平均={avg_pos:.4f}, 最大={max_pos:.4f}")
        print(f"  负相关: 平均={avg_neg:.4f}, 最小={min_neg:.4f}, 比例={neg_ratio:.2f}")
        for etf in portfolio:
            print(f"  - {etf}")
    
    # 使用最佳组合
    selected_etfs = best_portfolios[0][0] if best_portfolios else []
    
    # 打印选择结果
    print(f"\n最终选择{len(selected_etfs)}个低相关ETF:")
    for etf in selected_etfs:
        print(f"  - {etf}")
    
    # 打印选中ETF之间的相关性子矩阵
    sub_matrix = corr_matrix.loc[selected_etfs, selected_etfs]
    print("\n选中ETF之间的相关性矩阵:")
    print(sub_matrix)
    
    # 计算相关系数统计
    corr_values = []
    for i in range(len(selected_etfs)):
        for j in range(i+1, len(selected_etfs)):
            corr_values.append(sub_matrix.iloc[i, j])
    
    # 计算总体统计
    avg_corr = sum(corr_values) / len(corr_values) if corr_values else 0
    max_corr = max(corr_values) if corr_values else 0
    min_corr = min(corr_values) if corr_values else 0
    
    # 计算正负相关统计
    pos_corrs = [c for c in corr_values if c >= 0]
    neg_corrs = [c for c in corr_values if c < 0]
    
    avg_pos_corr = sum(pos_corrs) / len(pos_corrs) if pos_corrs else 0
    avg_neg_corr = sum(neg_corrs) / len(neg_corrs) if neg_corrs else 0
    max_pos_corr = max(pos_corrs) if pos_corrs else 0
    min_neg_corr = min(neg_corrs) if neg_corrs else 0
    neg_corr_ratio = len(neg_corrs) / len(corr_values) if corr_values else 0
    
    print(f"\n总体相关性统计: 平均={avg_corr:.4f}, 最大={max_corr:.4f}, 最小={min_corr:.4f}")
    print(f"正相关统计: 平均={avg_pos_corr:.4f}, 最大={max_pos_corr:.4f}")
    print(f"负相关统计: 平均={avg_neg_corr:.4f}, 最小={min_neg_corr:.4f}, 比例={neg_corr_ratio:.2f}")
    
    return selected_etfs

def visualize_selected_etfs(corr_matrix, selected_etfs):
    """可视化选中ETF的相关性热力图"""
    sub_matrix = corr_matrix.loc[selected_etfs, selected_etfs]
    
    plt.figure(figsize=(10, 8))
    sns.heatmap(sub_matrix, annot=True, cmap='coolwarm', center=0, fmt='.2f')
    plt.title('选中ETF之间的相关性热力图')
    plt.xticks(rotation=45)
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.savefig('selected_etfs_correlation.png')
    plt.show()
    
    # 计算平均相关系数（不包括对角线）
    corr_values = []
    for i in range(len(selected_etfs)):
        for j in range(i+1, len(selected_etfs)):
            corr_values.append(abs(sub_matrix.iloc[i, j]))
    
    avg_corr = sum(corr_values) / len(corr_values) if corr_values else 0
    max_corr = max(corr_values) if corr_values else 0
    
    print(f"选中ETF的平均相关系数: {avg_corr:.4f}")
    print(f"选中ETF的最大相关系数: {max_corr:.4f}")
    
    return avg_corr, max_corr

def corr():
    """计算ETF相关性并绘制热力图"""

    # ETF代码列表
    trade_code_list = [
        '518880.SH',  # 黄金ETF
        '159985.SZ',  # 豆粕ETF
        '513100.SH',  # 纳指ETF
        '510300.SH',  # 沪深300ETF
        '159915.SZ',  # 创业板
        '159992.SZ',  # 创新药ETF
        '515700.SH',  # 新能车ETF
        '510150.SH',  # 消费ETF
        '515790.SH',  # 光伏ETF
        '515880.SH',  # 通信ETF
        '512720.SH',  # 计算机ETF
        '512660.SH',  # 军工ETF
        '159740.SZ'   # 恒生科技ETF
    ]

    # 获取各ETF收盘价数据
    df_list = []
    for code in trade_code_list:
        try:
            stock_code = code.split('.')[0]
            market = code.split('.')[1].lower()
            df = ak.fund_etf_hist_sina(symbol=market + stock_code)
            df = df[['date', 'close']].rename(columns={'close': code})
            df_list.append(df[-250:])  # 取最近一年数据
        except Exception as e:
            print(f"获取{code}数据失败: {e}")
    
    # 合并数据
    merged_df = df_list[0]
    for df in df_list[1:]:
        merged_df = pd.merge(merged_df, df, on='date', how='outer')
    print(merged_df)
    
    # 填充缺失值
    merged_df = merged_df.fillna(method='ffill').fillna(method='bfill')
    
    # 计算收益率数据
    returns_df = merged_df.drop(columns=['date']).pct_change().dropna()
    
    # 计算相关性矩阵
    corr_matrix = merged_df.drop(columns=['date']).corr()
    print(corr_matrix)
    
    # 设置中文显示
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号    
    # 绘制热力图
    plt.figure(figsize=(12, 10))
    sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, fmt='.2f')
    plt.title('ETF相关性热力图(近一年)')
    plt.xticks(rotation=45)
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.show()
    # 选择低相关性的ETF组合
    # selected_etfs = select_etf_portfolio(
    #     corr_matrix, 
    #     max_correlation=0.4,  # 相关性阈值
    #     min_etfs=3, 
    #     max_etfs=6,
    #     target_etfs=4,  # 目标ETF数量
    #     balance_factor=0.8,  # 更重视相关性
    #     penalize_high_corr=True  # 惩罚高相关性
    # ) 
    # 为轮动策略选择ETF池
    selected_etfs = select_etf_pool_for_rotation(
        returns_df,
        lookback_period=252,  # 一年
        max_etfs=12,          # 最多12个ETF
        min_etfs=8,           # 至少8个ETF
        corr_threshold=0.7    # 相关性阈值
    ) 
    # 将选中的ETF保存到文件
    with open('selected_etfs.txt', 'w') as f:
        for etf in selected_etfs:
            f.write(f"{etf}\n")    

    visualize_selected_etfs(corr_matrix, selected_etfs)    
    
    # 个股历史行情数据
def get_df():
    # 获取最近一年数据
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
    df = ak.stock_zh_a_daily(symbol='sh600000', start_date=start_date, end_date=end_date, adjust="hfq")
    print(df)
def get_etf_df():    
    df = ak.fund_etf_hist_sina(symbol="sz159998")
    print(df)
# 获取最近一年数据
end_date = datetime.now().strftime('%Y%m%d')
start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
if __name__ == '__main__':
    corr()
    # get_df()
    # get_etf_df()
